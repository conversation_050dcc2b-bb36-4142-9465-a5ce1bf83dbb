# Orbyta Engineering SRL - Construction Management System

## Overview

This is a construction management system for Orbyta Engineering SRL that handles two main areas:
1. **Safety Coordination (Coordinamento Sicurezza)** - Managing construction sites (cantieri) and safety inspections (sopralluoghi)
2. **Work Direction (Direzione Lavori)** - Managing work direction activities and official reports

The application is built with a modern full-stack architecture using React frontend, Express backend, and PostgreSQL database with Drizzle ORM.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack Query (React Query) for server state
- **UI Framework**: shadcn/ui components with Radix UI primitives
- **Styling**: Tailwind CSS with CSS custom properties
- **Forms**: React Hook Form with Zod validation
- **Build Tool**: Vite with React plugin

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Neon serverless driver
- **ORM**: Drizzle ORM with schema-first approach
- **File Upload**: Multer for handling photo uploads
- **PDF Generation**: jsPDF for generating inspection reports
- **Build**: esbuild for production bundling

### Project Structure
```
/client          # React frontend
  /src
    /components  # Reusable UI components
    /pages       # Page components
    /lib         # Utilities and query client
    /hooks       # Custom React hooks
/server          # Express backend
/shared          # Shared types and schema
/migrations      # Database migrations
```

## Key Components

### Database Schema
- **cantieri**: Construction sites with project details, timelines, and status
- **tecnici**: Technical personnel/safety coordinators
- **sopralluoghi**: Safety inspections with checklists and reports
- **persone_presenti**: People present during inspections
- **direzione_lavori**: Work direction projects
- **direttori_lavori**: Work directors
- **verbali_direzione_lavori**: Official work direction reports
- **users**: User authentication (basic structure)

### Core Features
1. **Construction Site Management**: Create, edit, and track construction projects
2. **Safety Inspections**: Comprehensive inspection workflows with checklists, photos, and signatures
3. **Work Direction**: Manage work direction activities with official reporting
4. **PDF Generation**: Automated generation of official inspection and work direction reports
5. **Personnel Management**: Manage technical staff and work directors
6. **Search and Filtering**: Advanced search capabilities across all entities

### Authentication
Basic user authentication structure is in place but not fully implemented. The system uses a simple username/password model.

## Data Flow

1. **Client Requests**: React components use TanStack Query for data fetching
2. **API Layer**: Express routes handle CRUD operations and business logic
3. **Database**: Drizzle ORM manages PostgreSQL interactions
4. **File Storage**: Photos uploaded to local filesystem under `/uploads`
5. **PDF Generation**: Server-side PDF creation with jsPDF for official reports

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: Serverless PostgreSQL driver
- **drizzle-orm**: Type-safe ORM with PostgreSQL dialect
- **@tanstack/react-query**: Server state management
- **wouter**: Lightweight React router
- **jspdf**: PDF generation
- **multer**: File upload handling
- **zod**: Schema validation
- **tailwindcss**: Utility-first CSS framework

### UI Components
- **@radix-ui**: Headless UI components for accessibility
- **shadcn/ui**: Pre-built component library
- **lucide-react**: Icon library
- **react-hook-form**: Form state management

## Deployment Strategy

### Development
- **Environment**: Replit with Node.js 20
- **Database**: PostgreSQL 16 module
- **Hot Reload**: Vite development server with HMR
- **Build Command**: `npm run dev` (starts development server on port 5000)

### Production
- **Build Process**: 
  1. `vite build` - Builds React frontend to `dist/public`
  2. `esbuild` - Bundles Express server to `dist/index.js`
- **Runtime**: Node.js with `npm run start`
- **Deployment**: Replit autoscale deployment target
- **Port Configuration**: Internal port 5000, external port 80

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string (required)
- `NODE_ENV`: Environment mode (development/production)

### Data Persistence
- **Database**: PostgreSQL data is fully persistent across redeploys
- **File Storage**: Uploads folder protected with .gitkeep files and backup system
- **Backup System**: Automatic verification and metadata tracking of uploaded files
- **Recovery**: Files are preserved during deployment with integrity checks

## Changelog
```
Changelog:
- June 13, 2025. Initial setup
- June 13, 2025. Added draft saving functionality for direzione lavori verbali with status field (bozza/finalizzato)
- June 13, 2025. Removed "Direttori Lavori Assegnati" section from commessa detail page per user request
- June 13, 2025. Extended draft saving functionality to safety verbali (sopralluoghi) with same workflow
- June 16, 2025. Implemented three optional sections for work direction verbali:
  * Controlli dimensionali e geometrici (dimensional and geometric controls)
  * Eventuali non conformità riscontrate (any non-conformities found)
  * Indicazioni operative fornite all'impresa (operational instructions provided to company)
- June 16, 2025. Added millimetric drawing capabilities and photo upload for optional sections
- June 16, 2025. Enhanced PDF generation with proper photo embedding using base64 encoding
- June 16, 2025. Fixed signature collection to include both work director and all people present
- June 16, 2025. Increased photo size in PDFs to 3x original size (180x135px) per user request
- June 16, 2025. Added work director signature at bottom of PDF documents
- June 16, 2025. Implemented three-logo header support for PDF documents with proportional scaling
- June 16, 2025. Changed PDF font from Helvetica to user-preferred choice, settled on Helvetica
- June 16, 2025. Fixed column ordering in inspection tables (ID Sopralluogo vs Numero Progressivo)
- June 16, 2025. Updated progressive number format to: codiceCommessa-VDL-numeroProgressivo-AAAAMMGG
- June 16, 2025. Updated sopralluoghi ID format to: CODICECOMMESSA-CSE-NRPROGRESSIVO-AAAAMMGG
- June 16, 2025. Fixed signature canvas touch accuracy by implementing proper coordinate scaling
- June 17, 2025. Final progressive numbering configuration:
  * Safety inspections (sopralluoghi): All cantieri start from 001, no special cases
  * Work direction verbali: Only BC074 starts from 020, all other commesse start from 001
- June 17, 2025. Reset all databases: deleted existing sopralluoghi and verbali to implement clean numbering
- June 19, 2025. Enhanced photo handling in PDFs:
  * Implemented automatic EXIF orientation correction during photo upload using Sharp
  * Increased photo dimensions by 25%: checklist photos from 25x16px to 31x20px, optional sections from 65px to 81px
  * Fixed multiple photo display to show all photos in sequence rather than just one
  * Added proper spacing between multiple photos in PDF generation
- June 19, 2025. Major PDF layout improvements:
  * Reorganized page structure: "SEZIONI TECNICHE AGGIUNTIVE" on dedicated page
  * "Controlli dimensionali" remains on same page under main title
  * "Non conformità" and "Indicazioni operative" start on separate pages
  * Each additional point (2nd onwards) in all sections goes to new page
  * Moved "ASPETTI SULLA SICUREZZA" (formerly "Osservazioni") to dedicated page before signatures
  * Repositioned drawings to appear after photos instead of before
  * Implemented automatic uppercase conversion for all user-entered text
  * Added timestamp watermark on all pages: rotated 90°, positioned in bottom-right corner with Italian timezone correction
- June 19, 2025. Removed mandatory people validation:
  * "Persone presenti" no longer required for both verbali direzione lavori and sopralluoghi
  * Updated UI to remove asterisks and mandatory indicators
  * Users can now proceed with empty people lists
  * Simplified workflow allowing completion without personnel data
- June 19, 2025. Applied elegant table formatting to work direction verbali PDFs:
  * Copied "INFORMAZIONI GENERALI" table structure from safety inspections
  * Added gray background for labels, clean borders, and organized layout
  * Conditional fields: CIG and Qualifica appear only when present
  * Consistent styling across both document types for professional appearance
- June 19, 2025. Fixed progressive numbering for work direction verbali:
  * Each commessa now starts from progressive number 001 (not database ID)
  * Counts only finalized verbali per commessa for accurate numbering
  * BE034 continues from 002 (has 1 existing), new commesse start from 001
  * Maintained BC074 special rule starting from 020
- July 14, 2025. Enhanced verbale management with editable progressive numbers:
  * Progressive numbers now editable by tecnico during creation (proposes n+1 but modifiable)
  * Added delete functionality for draft verbali only
  * Added emergency PDF upload for draft verbali (finalizes verbale automatically)
  * Fixed server-side logic to use tecnico-provided progressive number instead of auto-generated
  * UI improvements with action buttons for draft management
  * Implemented dedicated multer configuration for PDF uploads with proper validation
  * Added secure PDF download route with correct headers (Content-Type: application/pdf)
  * Complete workflow: draft creation → PDF upload → automatic finalization → secure download
- July 14, 2025. Optimized PDF generation and image compression:
  * Fixed critical bug in PDF generation preventing file creation for verbali direzione lavori
  * Implemented automatic image compression during upload (max 1200px, 70% quality, mozjpeg encoder)
  * Enhanced PDF generation workflow with proper JSON parsing for personePresenti field
  * Added automatic PDF regeneration when verbale status changes from draft to finalized
  * Corrected download route path to point to uploads/verbali instead of uploads/pdfs
  * Complete PDF workflow now functional: generation → storage → secure download with proper headers
- July 15, 2025. Enhanced image compression and chronological ordering:
  * Increased image compression for lighter PDFs: max resolution reduced from 1200px to 800px
  * Reduced JPEG quality from 70% to 50% for maximum file size reduction (60-70% total reduction)
  * Fixed chronological ordering for both sopralluoghi and verbali direzione lavori
  * Most recent entries now appear at the top of lists in both cantieri and commesse views
  * Applied changes to both database storage and in-memory storage implementations
- July 15, 2025. Added home page selection interface:
  * Created dedicated home page with clear selection between Coordinamento Sicurezza and Direzione Lavori
  * Responsive design with gradient background, icons, and professional card layout
  * Added Home button to navigation bar for easy return to main selection
  * Company logo now clickable and redirects to home page
  * Improved user experience with clear separation of the two main system areas
- July 15, 2025. **RESTORE POINT**: Starting PWA + Cache implementation
  * All current functionality working correctly (chronological ordering, image compression, PDF generation)
  * Home page selection interface fully functional
  * Ready to implement offline capabilities with fallback option
- July 15, 2025. PWA + Cache Intelligente Implementation - COMPLETATA:
  * Created PWA manifest with installable app configuration
  * Implemented comprehensive Service Worker with offline-first strategy
  * Added intelligent caching system with IndexedDB storage
  * Network-first for fresh data, cache fallback for offline access
  * Offline action queue with automatic sync when connection restored
  * Real-time offline/online indicator with install PWA option
  * Enhanced Query Client with automatic offline data persistence
  * Complete offline workflow: view cached data → queue actions → sync online
  * RISOLTO: Service Worker interferenza con verbali - aggiunta esclusione per /verbali/con-firme
  * Funzionalità offline completamente operative con verbali funzionanti
- July 15, 2025. Sistema offline alternativo per verbali implementato:
  * Creato sistema localStorage dedicato (offline-verbali.ts) per bypass Service Worker
  * Logica di fallback: verifica navigator.onLine prima di tentativi di rete
  * Salvataggio automatico offline per verbali bozza e finalizzati
  * Sincronizzazione automatica quando torna connessione
  * Sistema robusto: localStorage + IndexedDB per backup completo
  * Gestione sanitizzazione dati per verbali vuoti o incompleti
- July 15, 2025. UX offline migliorata per verbali:
  * RISOLTO: Dopo firma verbale offline, app ora naviga automaticamente alla pagina commessa
  * Rimosso pulsante "Salva Vuoto (Offline)" non necessario per volontà utente
  * Aggiunto messaggio informativo offline negli elenchi verbali e sopralluoghi
  * Messaggio: "Modalità offline: verbali/sopralluoghi offline saranno visualizzati una volta ristabilita connessione"
  * UX completa: creazione offline → firma → redirect automatico → messaggio informativo nell'elenco
- July 15, 2025. Sistema persistenza dati e protezione redeploy:
  * Implementato sistema automatico di verifica e protezione cartelle uploads
  * Creati file .gitkeep per garantire persistenza directory durante redeploy
  * Aggiunto backup metadata automatico per tracciare file caricati
  * Sistema di verifica integrità file all'avvio del server
  * PostgreSQL database già persistente, ora anche file uploads protetti
  * Log automatico numero file preservati ad ogni riavvio server
- July 15, 2025. Aggiornato sistema loghi e correzioni UX:
  * Aggiunto nuovo logo Orbyta come logo predefinito per tutti i PDF
  * Corretto numero progressivo nella prima schermata creazione verbale (VDL invece di SOP-DL)
  * Unificato comportamento pulsanti download PDF (apertura automatica nel browser)
  * Sistema fallback intelligente per loghi: nuovo Orbyta → vecchi loghi → nessun logo
  * Ottimizzate dimensioni logo per migliore presentazione nei documenti
- July 16, 2025. Aggiunta gestione email e telefono per persone presenti nei verbali direzione lavori:
  * Esteso schema database personePresenti con campi email e telefono (opzionali)
  * Aggiornato form creazione verbali con campi email e telefono per ogni persona presente
  * Implementato auto-completamento con dati direttori esistenti (include email e telefono)
  * Aggiornata generazione PDF server-side per mostrare email e telefono a fianco di nome e qualifica
  * Aggiornata generazione PDF offline per includere i nuovi contatti
  * Layout responsive con griglia 5 colonne per nome, qualifica, email, telefono e azioni
  * Formato visualizzazione: "NOME - QUALIFICA - EMAIL: email - TEL: telefono"
  * Implementato formato tabellare per persone presenti nei PDF con 4 colonne: nominativo, qualifica, telefono, email
  * Testo con wrapping automatico per evitare overflow fuori dai margini pagina
  * Tabella con header grigio e bordi per presentazione professionale sia online che offline
  * Allineamento verticale centrato del testo nelle celle della tabella per migliore leggibilità
  * Sostituito simbolo occhio con pulsante "Visualizza" più grande e tablet-friendly nell'elenco verbali
  * Risolto problema firme PDF: ora solo nome e cognome con firma a fianco (non tabella completa)
  * Corretto comportamento gomma nel disegno millimetrico: ora disegna bianco invece di cancellare tutto
  * Implementato sistema rubrica persone presenti per commessa con tabella dedicata
  * Persone presenti automaticamente salvate in rubrica per riutilizzo nei verbali successivi
  * API complete per gestione rubrica: GET, POST, DELETE per ogni commessa
```

## Sicurezza e Privacy

### Vulnerabilità Identificate e Mitigazioni

#### 🔴 CRITICITÀ ALTA
1. **JWT Secret Hardcoded**
   - `JWT_SECRET` ha fallback hardcoded in codice (`server/routes.ts:20`)
   - **Rischio**: Compromissione autenticazione se .env non configurato
   - **Mitigazione**: Utilizzare sempre variabile ambiente in produzione

2. **Console.log con Dati Sensibili** 
   - Presenza di `console.log` che possono esporre dati in produzione (`server/routes.ts:165`, client verbali)
   - **Rischio**: Log leak di informazioni sensibili
   - **Mitigazione**: Rimuovere o condizionalizzare per development only

3. **Mancanza Validazione Autorizzazione**
   - Routes autenticati ma senza controllo granulare permessi
   - **Rischio**: Privilege escalation tra utenti
   - **Mitigazione**: Implementare RBAC (Role-Based Access Control)

#### 🟡 CRITICITÀ MEDIA
1. **Upload File Security**
   - Validazione magic numbers implementata ma migliorabile
   - **Rischio**: Upload file malevoli
   - **Mitigazione**: Scanner antivirus + sandbox per file uploads

2. **Rate Limiting Configurazione**
   - Rate limiting implementato ma configurazione potrebbe essere più granulare
   - **Rischio**: DoS attacks
   - **Mitigazione**: Rate limiting per endpoint specifici

### Aspetti Privacy e GDPR

#### 🟢 CONFORMITÀ PRIVACY POLICY/COOKIES
**NON NECESSARIA** per deployment privato Replit con le seguenti condizioni:
- ✅ Deploy privato dietro autenticazione Replit
- ✅ Utenti interni all'azienda (Orbyta Engineering)
- ✅ Nessun tracking di terze parti
- ✅ Nessun cookie marketing/analytics
- ✅ Solo localStorage tecnico per funzionalità app

**NECESSARIA SE**:
- 📍 Deploy pubblico o semi-pubblico
- 📍 Clienti esterni accedono al sistema
- 📍 Integrazione Google Analytics o tracking
- 📍 Cookie non essenziali

#### Dati Trattati
- **Personali**: Nomi, cognomi, email, telefoni (tecnici e persone presenti)
- **Professionali**: Qualifiche, aziende, progetti
- **Tecnici**: IP, timestamp, log accessi
- **Finalità**: Gestione cantieri e sicurezza sul lavoro (base giuridica: contratto)

### Pattern di Sicurezza Implementati

#### ✅ BEST PRACTICES PRESENTI
- Helmet.js per security headers (`server/routes.ts:98`)
- JWT con scadenza (7 giorni) (`server/routes.ts:161`)
- Password hashing con bcrypt (`server/routes.ts:155`)
- Input validation con Zod (schema validation)
- File type validation con magic numbers (`server/routes.ts:52`)
- Path traversal protection (`server/routes.ts:42`)
- Express rate limiting (`server/routes.ts:115-131`)
- Secure filename generation (`server/routes.ts:72`)
- CORS configurazione
- Registrazione disabilitata (`server/routes.ts:136`)

#### 🔧 MIGLIORAMENTI CONSIGLIATI
1. **Environment Variables**
   ```bash
   # Variabili critiche da settare
   JWT_SECRET=random_256_bit_key
   DATABASE_URL=postgresql://...
   NODE_ENV=production
   ```

2. **Logging Sicuro**
   ```javascript
   // Sostituire console.log con logger condizionale
   const logger = NODE_ENV === 'development' ? console : { log: () => {}, error: console.error }
   ```

3. **Autorizzazione Granulare**
   - Implementare ruoli utente (admin, tecnico, read-only)
   - Middleware autorizzazione per singoli endpoint
   - Controllo accesso basato su cantiere/commessa

4. **Audit Trail**
   - Log modifiche critiche (creazione/eliminazione verbali)
   - Timestamp e utente per ogni azione
   - Retention policy per log

### Compliance e Certificazioni

#### GDPR (Regolamento Generale Protezione Dati)
- **Base giuridica**: Esecuzione contratto (Art. 6.1.b)
- **Minimizzazione dati**: Solo dati necessari per gestione cantieri
- **Diritti interessati**: Accesso, rettifica, cancellazione implementabili
- **Data breach**: Procedura notifica entro 72h da implementare

#### ISO 27001 Alignment
- ✅ Controllo accessi logici
- ✅ Crittografia dati in transito (HTTPS)
- 🔧 Gestione vulnerabilità da sistematizzare
- 🔧 Business continuity da formalizzare

### Code Quality e Pattern

#### ✅ PATTERN POSITIVI
- Architettura ben strutturata (React + Express + PostgreSQL)
- TypeScript per type safety
- Zod per validazione input (`shared/schema.ts`)
- Drizzle ORM per database safety
- Componenti modulari e riutilizzabili
- PWA con offline support
- Backup automatico file uploads

#### 🔧 AREE DI MIGLIORAMENTO
- Presenza `console.log` in produzione
- Pattern di gestione errori inconsistenti
- Hardcoded fallback per JWT secret

### Raccomandazioni Immediate

#### Priorità Alta (🔴)
1. **Impostare JWT_SECRET in variabile ambiente**
2. **Rimuovere console.log da codice di produzione**
3. **Implementare RBAC per autorizzazioni**

#### Priorità Media (🟡)
4. **Configurare backup automatici database**
5. **Monitoring sicurezza e performance**
6. **Implementare audit trail**

#### Priorità Bassa (🟢)
7. **Scanner antivirus per upload**
8. **Business continuity plan**
9. **Penetration testing periodico**

## User Preferences
```
Preferred communication style: Simple, everyday language.
```