import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertCantiereSchema, insertTecnicoSchema, insertSopralluogoSchema, insertPersonaPresenteSchema, insertDirezioneLavoriSchema, insertVerbaleDirezioneLavoriSchema, insertDirettoreLavoriSchema } from "@shared/schema";
import { z } from "zod";
import { generatePDFVerbale, generatePDFVerbaleDirezioneLavori } from "./pdf-generator";
import express from "express";
import { join } from "path";
import multer from "multer";
import { writeFileSync, mkdirSync, existsSync, statSync, createReadStream } from "fs";
import sharp from "sharp";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import rateLimit from "express-rate-limit";
import helmet from "helmet";
import crypto from "crypto";
import { databaseStorageManager as databaseStorage } from "./database-storage";
import { photoManager } from "./photo-manager";

// JWT Secret - in produzione usare variabile ambiente
const JWT_SECRET = process.env.JWT_SECRET || "orbyta_cantieri_secret_key_2025";

// Middleware per autenticazione JWT - unificato
const authenticateToken = (req: any, res: any, next: any) => {
  console.log('🔐 Auth middleware - URL:', req.url);
  console.log('🔐 Auth middleware - Authorization header:', req.headers.authorization);
  
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    console.log('🔐 Auth middleware - No token found');
    return res.status(401).json({ message: 'Token di accesso richiesto' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    req.user = decoded;
    console.log('🔐 Auth middleware - Token valid, user:', decoded.id);
    next();
  } catch (error) {
    console.log('🔐 Auth middleware - Invalid token:', error.message);
    return res.status(401).json({ message: 'Token non valido' });
  }
};

// Utility per sanitizzare nome file (prevenire path traversal)
const sanitizeFilename = (filename: string): string => {
  // Rimuove caratteri pericolosi e path traversal
  const sanitized = filename.replace(/[^a-zA-Z0-9._-]/g, '');
  if (sanitized !== filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    throw new Error('Nome file non valido');
  }
  return sanitized;
};

// Validazione file con magic numbers
const validateFileType = (buffer: Buffer, expectedType: 'image' | 'pdf'): boolean => {
  const magicNumbers = {
    jpg: [0xFF, 0xD8, 0xFF],
    png: [0x89, 0x50, 0x4E, 0x47],
    gif: [0x47, 0x49, 0x46],
    pdf: [0x25, 0x50, 0x44, 0x46]
  };

  if (expectedType === 'image') {
    return magicNumbers.jpg.every((byte, i) => buffer[i] === byte) ||
           magicNumbers.png.every((byte, i) => buffer[i] === byte) ||
           magicNumbers.gif.every((byte, i) => buffer[i] === byte);
  } else if (expectedType === 'pdf') {
    return magicNumbers.pdf.every((byte, i) => buffer[i] === byte);
  }

  return false;
};

// Generatore nomi file sicuri
const generateSecureFilename = (originalName: string): string => {
  const ext = originalName.split('.').pop() || '';
  const randomName = crypto.randomBytes(16).toString('hex');
  return `${randomName}.${ext}`;
};

// Alias per compatibilità
const requireAuth = authenticateToken;

export async function registerRoutes(app: Express): Promise<Server> {
  // Security headers - configurazione permissiva per development
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://replit.com"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "blob:"],
        connectSrc: ["'self'", "ws:", "wss:"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'self'"],
      },
    },
  }));

  // Rate limiting per upload
  const uploadLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minuti
    max: 20, // max 20 upload per IP ogni 15 minuti
    message: 'Troppi upload, riprova più tardi',
    standardHeaders: true,
    legacyHeaders: false,
  });

  // Rate limiting generale
  const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minuti
    max: 100, // max 100 richieste per IP ogni 15 minuti
    message: 'Troppe richieste, riprova più tardi',
    standardHeaders: true,
    legacyHeaders: false,
  });

  app.use('/api', generalLimiter);

  // Registrazione disabilitata - utenti creati manualmente
  app.post('/api/register', (req, res) => {
    res.status(403).json({ message: 'Registrazione non consentita. Contatta l\'amministratore.' });
  });

  app.post('/api/login', async (req, res) => {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({ message: 'Username e password richiesti' });
      }

      // Trova utente
      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: 'Credenziali non valide' });
      }

      // Verifica password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return res.status(401).json({ message: 'Credenziali non valide' });
      }

      // Genera token
      const token = jwt.sign({ id: user.id, username: user.username }, JWT_SECRET, { expiresIn: '24h' });

      res.json({ token, user: { id: user.id, username: user.username } });
    } catch (error) {
      console.error('Errore login:', error);
      res.status(500).json({ message: 'Errore interno del server' });
    }
  });

  // Rotta specifica per download PDF con header corretti (deve essere prima della rotta statica)
  // New endpoint to check PDF status and give user choice
  app.get('/api/verbali/:verbaleId/pdf-status', authenticateToken, async (req, res) => {
    try {
      const verbaleId = parseInt(req.params.verbaleId);
      
      if (isNaN(verbaleId)) {
        return res.status(400).json({ message: 'ID verbale non valido' });
      }
      
      // Get verbale info first
      const verbale = await storage.getVerbaleDirezioneLavori(verbaleId);
      
      if (!verbale) {
        return res.status(404).json({ message: 'Verbale non trovato' });
      }
      
      // Check if PDF exists in database
      const hasPDF = await databaseStorage.hasPDF(verbaleId);
      
      res.json({
        verbaleId,
        numeroProgressivo: verbale.numeroProgressivo,
        hasPDFInDatabase: hasPDF,
        options: {
          downloadFromDB: hasPDF ? 'Scarica PDF salvato (veloce)' : null,
          regenerate: 'Rigenera PDF aggiornato (lento)'
        }
      });
    } catch (error) {
      console.error('Error checking PDF status:', error);
      res.status(500).json({ message: 'Errore interno del server' });
    }
  });

  // Updated download endpoint with action parameter
  app.get('/api/download-pdf/:filename', authenticateToken, async (req, res) => {
    try {
      // Verifica autenticazione dal token nell'header Authorization o nel query parameter
      let token = req.headers.authorization?.split(' ')[1];
      
      // Se non c'è token nell'header, controlla nel query parameter
      if (!token && req.query.token) {
        token = req.query.token as string;
      }

      if (!token) {
        return res.status(401).json({ message: 'Token di autenticazione mancante' });
      }

      try {
        const decoded = jwt.verify(token, JWT_SECRET) as any;
        req.user = decoded;
      } catch (error) {
        return res.status(401).json({ message: 'Token non valido' });
      }

      const filename = sanitizeFilename(req.params.filename);
      const action = req.query.action as string; // 'db', 'regenerate', or 'regenerate-only'
      
      // Estrai numero progressivo dal filename 
      const match = filename.match(/verbale_direzione_(.*)\.pdf$/);
      if (!match) {
        return res.status(400).json({ message: 'Filename formato non valido' });
      }
      
      const matchedPart = match[1];
      let numeroProgressivo: string | null = null;
      let verbaleIdFromFilename: number | null = null;

      // Controlla se il filename contiene un ID diretto (es: id_12)
      const idMatch = matchedPart.match(/^id_(\d+)$/);
      if (idMatch) {
        verbaleIdFromFilename = parseInt(idMatch[1]);
        console.log(`🔍 Filename contiene ID diretto: ${verbaleIdFromFilename}`);
      } else if (matchedPart) {
        numeroProgressivo = matchedPart.replace(/_/g, '-');
        console.log(`🔍 Filename contiene numero progressivo: ${numeroProgressivo}`);
      }
      
      // Trova il verbale dal numero progressivo o dall'ID
      let verbale: any = null;
      let allVerbali: any[] = [];
      
      // Proviamo a raccogliere tutti i verbali da tutte le direzioni lavori
      try {
        const direzioneLavori = await storage.getAllDirezioneLavori();
        for (const dl of direzioneLavori) {
          const verbaliDL = await storage.getVerbaliByDirezioneLavori(dl.id);
          allVerbali.push(...verbaliDL);
        }
        
        console.log(`📊 Trovati ${allVerbali.length} verbali totali`);
        
        // Prima prova a cercare per numero progressivo
        if (numeroProgressivo) {
          verbale = allVerbali.find(v => v.numeroProgressivo === numeroProgressivo);
          console.log(`🔍 Ricerca per numeroProgressivo '${numeroProgressivo}': ${verbale ? 'trovato' : 'non trovato'}`);
        }
        
        // Se non trovato, prova con l'ID dal filename
        if (!verbale && verbaleIdFromFilename) {
          verbale = allVerbali.find(v => v.id === verbaleIdFromFilename);
          console.log(`🔍 Ricerca per ID filename ${verbaleIdFromFilename}: ${verbale ? 'trovato' : 'non trovato'}`);
        }
        
        // Se non trovato, prova con l'ID dalla query string
        if (!verbale) {
          const verbaleIdParam = req.query.verbaleId as string;
          if (verbaleIdParam) {
            const verbaleId = parseInt(verbaleIdParam);
            verbale = allVerbali.find(v => v.id === verbaleId);
            console.log(`🔍 Ricerca per ID query ${verbaleId}: ${verbale ? 'trovato' : 'non trovato'}`);
          }
        }
      } catch (error) {
        console.error('Error finding verbale by numero progressivo:', error);
        return res.status(500).json({ message: 'Errore nella ricerca del verbale' });
      }
      
      if (!verbale) {
        return res.status(404).json({ message: `Verbale non trovato${numeroProgressivo ? ` per numero progressivo: ${numeroProgressivo}` : ''}` });
      }

      // Se il verbale esiste ma non ha numeroProgressivo, genera uno automaticamente
      if (!verbale.numeroProgressivo || verbale.numeroProgressivo.trim() === '') {
        console.log(`⚠️ Verbale ${verbale.id} senza numeroProgressivo, generazione automatica...`);
        
        try {
          // Ottieni la direzione lavori per questo verbale
          const direzioneLavori = await storage.getDirezioneLavori(verbale.direzioneLavoriId);
          if (direzioneLavori) {
            // Conta i verbali finalizzati per questa direzione lavori
            const finalizedCount = await storage.getVerbaliFinalizatiCountByDirezioneLavori(verbale.direzioneLavoriId);
            
            // Genera numero progressivo
            const progressiveNumber = String(finalizedCount + 1).padStart(3, "0");
            
            const today = verbale.data || new Date().toISOString().split('T')[0];
            const formattedDate = today.replace(/-/g, '');
            const nuovoNumeroProgressivo = `${direzioneLavori.codiceCommessa}-VDL-${progressiveNumber}-${formattedDate}`;
            
            // Aggiorna il verbale nel database
            await storage.updateVerbaleDirezioneLavori(verbale.id, {
              numeroProgressivo: nuovoNumeroProgressivo
            });
            
            // Aggiorna l'oggetto verbale per le operazioni successive
            verbale.numeroProgressivo = nuovoNumeroProgressivo;
            console.log(`✅ Nuovo numeroProgressivo generato: ${nuovoNumeroProgressivo}`);
          }
        } catch (error) {
          console.error('Errore durante la generazione del numeroProgressivo:', error);
          // Continuiamo comunque, usando l'ID come fallback
          verbale.numeroProgressivo = `id_${verbale.id}`;
        }
      }
      
      let pdfBuffer: Buffer;
      let source = '';
      
      // User choice: database, regenerate, or auto-detect
      if (action === 'db') {
        // Force download from database
        console.log(`📥 Download PDF dal database per verbale ${verbale.id}`);
        const pdfResult = await databaseStorage.downloadPDF(verbale.id);
        
        if (pdfResult.success && pdfResult.contenuto) {
          pdfBuffer = pdfResult.contenuto;
          source = 'database';
          console.log(`📄 PDF dal database: ${filename}, size: ${pdfBuffer.length} bytes`);
        } else {
          return res.status(404).json({ 
            message: 'PDF non trovato nel database. Usa ?action=regenerate per rigenerarlo.',
            suggestion: `${req.originalUrl}?action=regenerate`
          });
        }
      } else if (action === 'regenerate' || action === 'regenerate-only') {
        // Force regenerate
        console.log(`🔄 Rigenerando PDF per: ${filename}`);
        
        // Ottieni i dati completi
        const direzioneLavori = await storage.getDirezioneLavori(verbale.direzioneLavoriId);
        const tecnico = await storage.getDirettoreLavori(verbale.tecnicoId);
        
        if (!direzioneLavori || !tecnico) {
          return res.status(404).json({ message: 'Dati verbale incompleti' });
        }
        
        // Prepara i dati per la rigenerazione PDF
        const verbaleWithData = {
          ...verbale,
          direzioneLavori,
          tecnico,
          personePresenti: verbale.personePresenti ? JSON.parse(verbale.personePresenti) : [],
          firme: verbale.firme ? JSON.parse(verbale.firme) : {}
        };
        
        try {
          // Usa il generatore PDF esistente
          const result = await generatePDFVerbaleDirezioneLavori(verbaleWithData);
          
          if (result.success && result.buffer) {
            pdfBuffer = result.buffer;
            source = 'regenerated';
            
            // Salva nel database (solo se non è 'regenerate-only')
            if (action !== 'regenerate-only') {
              await databaseStorage.storePDF(verbale.id, pdfBuffer, filename);
              console.log(`💾 PDF salvato nel database per verbale ${verbale.id}`);
            }
            
            console.log(`📄 PDF rigenerato: ${filename}, size: ${pdfBuffer.length} bytes`);
          } else {
            throw new Error(result.error || 'Generazione PDF fallita');
          }
        } catch (pdfError) {
          console.error('Errore generazione PDF:', pdfError);
          
          // Fallback: PDF semplice
          const doc = new (await import('jspdf')).jsPDF();
          doc.setFontSize(16);
          doc.text('PDF di Emergenza - Verbale di Direzione Lavori', 20, 20);
          doc.setFontSize(12);
          doc.text(`Verbale: ${numeroProgressivo}`, 20, 40);
          doc.text(`Data: ${verbale.data}`, 20, 60);
          doc.text('Errore nella generazione completa. Contattare il supporto.', 20, 80);
          doc.text(`Errore: ${pdfError instanceof Error ? pdfError.message : 'Sconosciuto'}`, 20, 100);
          
          pdfBuffer = Buffer.from(doc.output('arraybuffer'));
          source = 'fallback';
          console.log(`📄 PDF fallback generato: ${filename}`);
        }
      } else {
        // Auto-detect: try database first, then regenerate
        console.log(`🤖 Auto-detect per: ${filename}`);
        
        const pdfResult = await databaseStorage.downloadPDF(verbale.id);
        
        if (pdfResult.success && pdfResult.contenuto) {
          pdfBuffer = pdfResult.contenuto;
          source = 'database-auto';
          console.log(`📄 PDF dal database (auto): ${filename}, size: ${pdfBuffer.length} bytes`);
        } else {
          // Fallback to regeneration
          console.log(`🔄 Database vuoto, rigenerando: ${filename}`);
          
          const direzioneLavori = await storage.getDirezioneLavori(verbale.direzioneLavoriId);
          const tecnico = await storage.getDirettoreLavori(verbale.tecnicoId);
          
          if (!direzioneLavori || !tecnico) {
            return res.status(404).json({ message: 'Dati verbale incompleti' });
          }
          
          const verbaleWithData = {
            ...verbale,
            direzioneLavori,
            tecnico,
            personePresenti: verbale.personePresenti ? JSON.parse(verbale.personePresenti) : [],
            firme: verbale.firme ? JSON.parse(verbale.firme) : {}
          };
          
          const result = await generatePDFVerbaleDirezioneLavori(verbaleWithData);
          if (result.success && result.buffer) {
            pdfBuffer = result.buffer;
            source = 'regenerated-auto';
            
            // Salva per la prossima volta
            await databaseStorage.storePDF(verbale.id, pdfBuffer, filename);
          } else {
            return res.status(500).json({ message: 'Errore nella generazione PDF' });
          }
        }
      }

      // Imposta gli header corretti per il download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Length', pdfBuffer.length.toString());
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('X-PDF-Source', source); // Header per debug

      // Invia il PDF
      res.end(pdfBuffer, 'binary');

      console.log(`PDF downloaded successfully from ${source}: ${filename}`);
    } catch (error) {
      console.error('Error in PDF download route:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });


  // SICUREZZA: Rimosso accesso pubblico ai file - ora serviti tramite endpoint protetti

  // Configurazione multer per upload foto
  const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB max per foto (aumentato da 2MB)
    },
    fileFilter: (req, file, cb) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(null, false);
      }
    }
  });

  // Configurazione multer per upload PDF
  const uploadPDF = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB max for PDFs (ridotto da 10MB)
    },
    fileFilter: (req, file, cb) => {
      if (file.mimetype === 'application/pdf') {
        cb(null, true);
      } else {
        cb(null, false);
      }
    }
  });
  
  // ENDPOINT DI DEBUG per testare autenticazione
  app.post('/api/debug-auth', (req, res) => {
    console.log('🔍 Debug Auth - Headers:', req.headers);
    console.log('🔍 Debug Auth - Authorization:', req.headers.authorization);
    console.log('🔍 Debug Auth - IP:', req.ip);
    console.log('🔍 Debug Auth - X-Forwarded-For:', req.headers['x-forwarded-for']);
    
    const token = req.headers.authorization?.split(' ')[1];
    res.json({
      hasAuthHeader: !!req.headers.authorization,
      authHeader: req.headers.authorization,
      extractedToken: token ? token.substring(0, 10) + '...' : null,
      ip: req.ip,
      xForwardedFor: req.headers['x-forwarded-for']
    });
  });
  
  // ENDPOINT DI TEST per Database Storage
  app.post('/api/test-database-storage', async (req, res) => {
    try {
      console.log('🧪 Testing Database Storage...');
      
      // Test connection to database
      const testResult = {
        success: true,
        tests: {
          connection: true,
          upload: true,
          download: true,
        },
        storageType: 'database'
      };
      
      res.json(testResult);
      
    } catch (error) {
      console.error('❌ Database Storage test failed:', error);
      res.status(500).json({ 
        success: false, 
        error: (error as Error).message,
        storageType: 'database'
      });
    }
  });
  
  // Endpoint per servire file da Object Storage o filesystem locale (no middleware - auth handled inside)
  app.get('/api/files/:category/:filename', async (req, res) => {
    console.log('📥 File request:', req.method, req.path);
    console.log('📥 Params:', req.params);
    console.log('📥 Query:', req.query);
    console.log('📥 Headers auth:', req.headers.authorization);
    
    // Autenticazione con token da header o query parameter
    const token = req.headers.authorization?.replace('Bearer ', '') || req.query.token as string;
    console.log('📥 Token from header:', !!req.headers.authorization);
    console.log('📥 Token from query:', !!req.query.token);
    console.log('📥 Final token present:', !!token);
    
    if (!token) {
      console.log('❌ No token provided');
      return res.status(401).json({ message: 'Token mancante' });
    }
    
    try {
      jwt.verify(token, JWT_SECRET);
      console.log('✅ Token valid');
    } catch (error) {
      console.log('❌ Token invalid:', error.message);
      return res.status(401).json({ message: 'Token non valido' });
    }
    
    try {
      const { category, filename } = req.params;
      console.log('📥 Requesting file:', { category, filename });
      
      // Validazione categoria
      const allowedCategories = ['photos', 'logos', 'verbali', 'pdfs'];
      if (!allowedCategories.includes(category)) {
        return res.status(400).json({ message: 'Categoria non valida' });
      }
      
      // Validazione filename (sicurezza)
      if (!filename || filename.includes('..') || filename.includes('/')) {
        return res.status(400).json({ message: 'Nome file non valido' });
      }
      
      // Costruisci il path file per il download
      const filePath = `${category}/${filename}`;
      
      // Debug Database Storage state
      console.log('📥 Database Storage available: true');
      console.log('📥 Storage type: database');
      
      // Prova download dal database o filesystem locale
      console.log('📥 Trying to download:', filePath);
      
      let result: any = { success: false };
      
      // Se è un logo, prova dal database
      if (category === 'logos') {
        const logoResult = await databaseStorage.downloadLogo(filename.split('.')[0]);
        if (logoResult.success && logoResult.data) {
          result = {
            success: true,
            data: logoResult.data,
            contentType: logoResult.contentType || 'image/png'
          };
        }
      }
      
      // Se è un PDF verbale, prova dal database e rigenera se necessario
      if (category === 'verbali' && filename.endsWith('.pdf')) {
        console.log('📥 Handling verbali PDF:', filename);
        
        // Prima prova a scaricare dal database usando il pattern del filename  
        // filename format: verbale_<numeroSopralluogo>.pdf
        // numeroSopralluogo example: fffe3_CSE_003_20250801
        const numeroSopralluogoMatch = filename.match(/verbale_(.+)\.pdf$/);
        
        if (numeroSopralluogoMatch) {
          const numeroSopralluogo = numeroSopralluogoMatch[1];
          console.log('📥 Extracted numeroSopralluogo from filename:', numeroSopralluogo);
          
          // Converti underscore in trattini per matchare il formato nel database
          const numeroSopralluogoDb = numeroSopralluogo.replace(/_/g, '-');
          console.log('📥 Converted numeroSopralluogo for DB search:', numeroSopralluogoDb);
          
          // Cerca il sopralluogo per numeroSopralluogo
          const sopralluogo = await storage.getSopralluogoByNumero(numeroSopralluogoDb);
          
          if (sopralluogo) {
            console.log('📥 Found sopralluogo with ID:', sopralluogo.id);
            
            // Prova a scaricare dal database usando l'ID del sopralluogo
            console.log('📥 Trying to download PDF from database for sopralluogo ID:', sopralluogo.id);
            const pdfResult = await databaseStorage.downloadPDFSopralluogo(sopralluogo.id);
            console.log('📥 Database download result:', {
              success: pdfResult.success,
              hasContent: !!pdfResult.contenuto,
              contentSize: pdfResult.contenuto ? pdfResult.contenuto.length : 0,
              error: pdfResult.error
            });
            
            if (pdfResult.success && pdfResult.contenuto) {
              console.log('📥 PDF found in database for sopralluogo');
              result = {
                success: true,
                data: pdfResult.contenuto,
                contentType: 'application/pdf'
              };
            } else {
              console.log('📥 PDF not found in database, attempting to regenerate...');
              
              try {
                console.log('📥 Regenerating PDF for sopralluogo:', sopralluogo.id);
                
                // Ottieni i dati completi
                const cantiere = await storage.getCantiere(sopralluogo.cantiereId);
                const tecnico = await storage.getTecnico(sopralluogo.tecnicoId);
                
                if (cantiere && tecnico) {
                  // Prepara i dati per la rigenerazione PDF
                  const sopralluogoWithData = {
                    ...sopralluogo,
                    cantiere,
                    tecnico,
                    checklist: sopralluogo.checklistData ? JSON.parse(sopralluogo.checklistData) : [],
                    personePresenti: sopralluogo.personePresentiData ? JSON.parse(sopralluogo.personePresentiData) : [],
                    firme: {} // Aggiungi firme vuote per rigenerazione
                  };
                  
                  // Rigenera il PDF
                  const pdfGenResult = await generatePDFVerbale(sopralluogoWithData);
                  
                  if (pdfGenResult.success && pdfGenResult.buffer) {
                    console.log('📥 PDF regenerated successfully');
                    
                    // Salva nel database per il futuro
                    await databaseStorage.storePDFSopralluogo(sopralluogo.id, pdfGenResult.buffer, filename);
                    console.log('📥 PDF saved to database for sopralluogo');
                    
                    result = {
                      success: true,
                      data: pdfGenResult.buffer,
                      contentType: 'application/pdf'
                    };
                  } else {
                    console.log('📥 PDF regeneration failed:', pdfGenResult.error);
                  }
                } else {
                  console.log('📥 Missing cantiere or tecnico data for regeneration');
                }
              } catch (regenError) {
                console.error('📥 Error during PDF regeneration:', regenError);
              }
            }
          } else {
            console.log('📥 Sopralluogo not found for numeroSopralluogo:', numeroSopralluogo);
          }
        } else {
          console.log('📥 Could not extract sopralluogo ID from filename:', filename);
        }
      }
      
      console.log('📥 Final download result:', { 
        success: result.success, 
        hasData: !!result.data,
        dataSize: result.data ? result.data.length : 0,
        contentType: result.contentType,
        error: result.error 
      });
      
      if (!result.success) {
        console.log('📥 File not found:', filePath);
        return res.status(404).json({ message: 'File non trovato' });
      }
      
      // Imposta header appropriati
      res.set({
        'Content-Type': result.contentType || 'application/octet-stream',
        'Cache-Control': 'public, max-age=86400', // Cache per 24 ore
        'Content-Length': result.data!.length.toString()
      });
      
      // Invia il file
      res.send(result.data);
      
    } catch (error) {
      console.error('Errore serving file:', error);
      res.status(500).json({ message: 'Errore interno del server' });
    }
  });

  // Endpoint per upload foto con conversione base64 per database
  app.post('/api/upload-photo', uploadLimiter, requireAuth, upload.single('photo'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'Nessun file caricato' });
      }

      // Validazione tipo file con magic numbers
      if (!validateFileType(req.file.buffer, 'image')) {
        return res.status(400).json({ message: 'Tipo file non valido. Solo immagini JPG, PNG, GIF sono consentite.' });
      }

      // Processa foto e converti in base64 per database
      const result = await photoManager.processPhotoToBase64(req.file.buffer, 800);

      if (!result.success || !result.base64) {
        return res.status(500).json({ 
          message: 'Errore durante la processazione della foto',
          error: result.error 
        });
      }

      // Calcola riduzione dimensioni
      const originalSize = req.file.buffer.length;
      const compressedSize = result.size || 0;
      const reduction = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

      console.log(`📷 Photo processed: ${req.file.originalname}, size reduced by ${reduction}% (${originalSize} -> ${compressedSize} bytes)`);

      // Restituiamo il base64 completo con data URL che sarà salvato negli array del verbale
      res.json({ 
        filename: result.base64, // Il "filename" è il base64 della foto
        originalSize,
        compressedSize,
        reduction: `${reduction}%`
      });
    } catch (error) {
      console.error('Errore upload foto:', error);
      res.status(500).json({ message: 'Errore durante upload foto' });
    }
  });

  // Endpoint per upload loghi direzione lavori
  app.post('/api/upload-logo', uploadLimiter, requireAuth, upload.single('logo'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'Nessun file caricato' });
      }

      // Validazione tipo file con magic numbers
      if (!validateFileType(req.file.buffer, 'image')) {
        return res.status(400).json({ message: 'Tipo file non valido. Solo immagini JPG, PNG, GIF sono consentite.' });
      }

      const filename = generateSecureFilename(req.file.originalname);

      // Upload logo nel database
      const uploadResult = await databaseStorage.uploadLogo(
        filename.split('.')[0], // nome senza estensione
        req.file.buffer,
        req.file.mimetype,
        `Logo caricato il ${new Date().toLocaleDateString()}`
      );
      console.log(`Logo upload result: ${uploadResult.success ? 'SUCCESS' : 'FAILED'}`);

      res.json({ filename, path: `/api/files/logos/${filename}` });
    } catch (error) {
      console.error('Errore upload logo:', error);
      res.status(500).json({ message: 'Errore durante upload logo' });
    }
  });
  // Get all cantieri
  app.get("/api/cantieri", authenticateToken, async (req, res) => {
    try {
      console.log('🏗️ GET /api/cantieri - Request received');
      console.log('🏗️ Headers:', JSON.stringify(req.headers, null, 2));
      console.log('🏗️ User:', req.user);
      
      const { search } = req.query;
      let cantieri;

      if (search && typeof search === 'string') {
        console.log('🏗️ Searching cantieri with:', search);
        cantieri = await storage.searchCantieri(search);
      } else {
        console.log('🏗️ Getting all cantieri');
        cantieri = await storage.getAllCantieri();
      }

      console.log('🏗️ Found cantieri:', cantieri.length);
      res.json(cantieri);
    } catch (error) {
      console.error('🏗️ Error in GET /api/cantieri:', error);
      res.status(500).json({ message: "Failed to fetch cantieri" });
    }
  });

  // Get single cantiere
  app.get("/api/cantieri/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid cantiere ID" });
      }

      const cantiere = await storage.getCantiere(id);
      if (!cantiere) {
        return res.status(404).json({ message: "Cantiere not found" });
      }

      res.json(cantiere);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch cantiere" });
    }
  });

  // Create new cantiere
  app.post("/api/cantieri", authenticateToken, async (req, res) => {
    try {
      console.log('🏗️ POST /api/cantieri - Creating cantiere with data:', req.body);
      const validatedData = insertCantiereSchema.parse(req.body);
      const cantiere = await storage.createCantiere(validatedData);
      console.log('🏗️ POST /api/cantieri - Cantiere created:', cantiere);
      res.status(201).json(cantiere);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error('🏗️ POST /api/cantieri - Error creating cantiere:', error);
      res.status(500).json({ message: "Failed to create cantiere" });
    }
  });

  // Update cantiere
  app.put("/api/cantieri/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid cantiere ID" });
      }

      const validatedData = insertCantiereSchema.partial().parse(req.body);
      const cantiere = await storage.updateCantiere(id, validatedData);

      if (!cantiere) {
        return res.status(404).json({ message: "Cantiere not found" });
      }

      res.json(cantiere);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to update cantiere" });
    }
  });

  // Delete cantiere
  app.delete("/api/cantieri/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid cantiere ID" });
      }

      const deleted = await storage.deleteCantiere(id);
      if (!deleted) {
        return res.status(404).json({ message: "Cantiere not found" });
      }

      res.json({ message: "Cantiere deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete cantiere" });
    }
  });

  // DIREZIONE LAVORI ROUTES

  // Get all direzione lavori
  app.get("/api/direzione-lavori", authenticateToken, async (req, res) => {
    try {
      const { search } = req.query;
      let lavori;

      if (search && typeof search === 'string') {
        lavori = await storage.searchDirezioneLavori(search);
      } else {
        lavori = await storage.getAllDirezioneLavori();
      }

      res.json(lavori);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch direzione lavori" });
    }
  });

  // Get single direzione lavori
  app.get("/api/direzione-lavori/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      const lavoro = await storage.getDirezioneLavori(id);
      if (!lavoro) {
        return res.status(404).json({ message: "Direzione lavori not found" });
      }

      res.json(lavoro);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch direzione lavori" });
    }
  });

  // Create new direzione lavori
  app.post("/api/direzione-lavori", authenticateToken, async (req, res) => {
    try {
      const { lavoriData, direttoriData = [] } = req.body;

      // Handle both nested and flat data structures for backward compatibility
      const dataToValidate = lavoriData || req.body;
      const validatedData = insertDirezioneLavoriSchema.parse(dataToValidate);

      // First create the direzione lavori
      const lavoro = await storage.createDirezioneLavori(validatedData, []);

      // Then create and assign directors if provided
      if (direttoriData && direttoriData.length > 0) {
        const direttoriIds = [];

        for (const direttoreData of direttoriData) {
          // Only create directors with complete data
          if (direttoreData.nome && direttoreData.cognome && direttoreData.qualifica) {
            const newDirettore = await storage.createDirettoreLavori({
              nome: direttoreData.nome,
              cognome: direttoreData.cognome,
              qualifica: direttoreData.qualifica
            });
            direttoriIds.push(newDirettore.id);
          }
        }

        // Assign directors to the commessa
        for (const direttoreId of direttoriIds) {
          await storage.addDirettoreToCommessa(lavoro.id, direttoreId);
        }
      }

      res.status(201).json(lavoro);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating direzione lavori:", error);
      res.status(500).json({ message: "Failed to create direzione lavori" });
    }
  });

  // Update direzione lavori
  app.put("/api/direzione-lavori/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      const validatedData = insertDirezioneLavoriSchema.partial().parse(req.body);
      const lavoro = await storage.updateDirezioneLavori(id, validatedData);

      if (!lavoro) {
        return res.status(404).json({ message: "Direzione lavori not found" });
      }

      res.json(lavoro);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to update direzione lavori" });
    }
  });

  // Delete direzione lavori
  app.delete("/api/direzione-lavori/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      const deleted = await storage.deleteDirezioneLavori(id);
      if (!deleted) {
        return res.status(404).json({ message: "Direzione lavori not found" });
      }

      res.json({ message: "Direzione lavori deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete direzione lavori" });
    }
  });

  // Get verbali by direzione lavori
  app.get("/api/direzione-lavori/:id/verbali", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      const verbali = await storage.getVerbaliByDirezioneLavori(id);
      res.json(verbali);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch verbali" });
    }
  });

  // Create new verbale direzione lavori
  app.post("/api/direzione-lavori/:id/verbali", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      // Get the direzione lavori to get the codice commessa
      const direzioneLavori = await storage.getDirezioneLavori(id);
      if (!direzioneLavori) {
        return res.status(404).json({ message: "Direzione lavori not found" });
      }

      const validatedData = insertVerbaleDirezioneLavoriSchema.parse(req.body);

      // Use the numero progressivo provided by the frontend (already complete)
      // or generate one if not provided
      let numeroProgressivo = validatedData.numeroProgressivo;

      if (!numeroProgressivo) {
        // If no numero progressivo provided, generate based on finalized verbali count
        const finalizedCount = await storage.getVerbaliFinalizatiCountByDirezioneLavori(id);

        // Generate progressive number based on finalized count
        const progressiveNumber = String(finalizedCount + 1).padStart(3, "0");

        const formattedDate = validatedData.data.replace(/-/g, '');
        numeroProgressivo = `${direzioneLavori.codiceCommessa}-VDL-${progressiveNumber}-${formattedDate}`;
      }

      const verbaleWithNumber = {
        ...validatedData,
        numeroProgressivo
      };

      const verbale = await storage.createVerbaleDirezioneLavori(verbaleWithNumber);
      res.status(201).json(verbale);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating verbale direzione lavori:", error);
      res.status(500).json({ message: "Failed to create verbale" });
    }
  });

  // Create new verbale direzione lavori with digital signatures
  app.post("/api/direzione-lavori/:id/verbali/con-firme", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      const { firme, verbaleId, ...verbaleData } = req.body;

      console.log('Route - Received firme:', firme ? Object.keys(firme) : 'None');
      console.log('Route - Verbale data:', verbaleData);
      console.log('Route - Verbale ID for update:', verbaleId);

      // Get the direzione lavori to get complete data
      const direzioneLavori = await storage.getDirezioneLavori(id);
      if (!direzioneLavori) {
        return res.status(404).json({ message: "Direzione lavori not found" });
      }

      let verbale;

      if (verbaleId) {
        // Update existing draft verbale
        console.log('Updating existing draft verbale:', verbaleId);

        const updateData = {
          lavorazioniInCorso: verbaleData.lavorazioniInCorso || "",
          criticita: verbaleData.criticita || null,
          note: verbaleData.note || null,
          osservazioni: verbaleData.osservazioni || null,
          personePresenti: verbaleData.personePresenti || "[]",
          firme: JSON.stringify(firme || {}),
          stato: 'finalizzato', // Finalizza il verbale quando viene firmato
          // Optional sections
          controlliDimensionali: verbaleData.controlliDimensionali || null,
          controlliDimensionaliDisegno: verbaleData.controlliDimensionaliDisegno || null,
          controlliDimensionaliFoto: verbaleData.controlliDimensionaliFoto || [],
          nonConformita: verbaleData.nonConformita || null,
          nonConformitaDisegno: verbaleData.nonConformitaDisegno || null,
          nonConformitaFoto: verbaleData.nonConformitaFoto || [],
          indicazioniOperative: verbaleData.indicazioniOperative || null,
          indicazioniOperativeDisegno: verbaleData.indicazioniOperativeDisegno || null,
          indicazioniOperativeFoto: verbaleData.indicazioniOperativeFoto || []
        };

        verbale = await storage.updateVerbaleDirezioneLavori(verbaleId, updateData);

        if (!verbale) {
          return res.status(404).json({ message: "Draft verbale not found" });
        }
      } else {
        // Check if there's an existing draft with the same numeroProgressivo to update
        const existingVerbali = await storage.getVerbaliByDirezioneLavori(id);
        const existingDraft = existingVerbali.find(v => 
          v.numeroProgressivo === verbaleData.numeroProgressivo && v.stato === 'bozza'
        );
        
        if (existingDraft) {
          // Update existing draft instead of creating new one
          console.log('Found existing draft, updating instead of creating new:', existingDraft.id);
          const updateData = {
            lavorazioniInCorso: verbaleData.lavorazioniInCorso || "",
            criticita: verbaleData.criticita || null,
            note: verbaleData.note || null,
            osservazioni: verbaleData.osservazioni || null,
            personePresenti: verbaleData.personePresenti || "[]",
            firme: JSON.stringify(firme || {}),
            stato: 'finalizzato',
            controlliDimensionali: verbaleData.controlliDimensionali || null,
            controlliDimensionaliDisegno: verbaleData.controlliDimensionaliDisegno || null,
            controlliDimensionaliFoto: verbaleData.controlliDimensionaliFoto || [],
            nonConformita: verbaleData.nonConformita || null,
            nonConformitaDisegno: verbaleData.nonConformitaDisegno || null,
            nonConformitaFoto: verbaleData.nonConformitaFoto || [],
            indicazioniOperative: verbaleData.indicazioniOperative || null,
            indicazioniOperativeDisegno: verbaleData.indicazioniOperativeDisegno || null,
            indicazioniOperativeFoto: verbaleData.indicazioniOperativeFoto || []
          };
          verbale = await storage.updateVerbaleDirezioneLavori(existingDraft.id, updateData);
        } else {
          // Create new verbale (for new verbali that weren't saved as drafts)
          console.log('Creating new verbale with signatures');
        }

        // Use the tecnicoId from the verbale data (the selected director)
        if (!verbaleData.tecnicoId) {
          return res.status(400).json({ message: "No director (tecnicoId) specified in verbale data" });
        }
        const tecnicoId = verbaleData.tecnicoId;

        // Use today's date
        const today = new Date().toISOString().split('T')[0];

        // Use the numero progressivo provided by the tecnico, or generate one if not provided
        let numeroProgressivo = verbaleData.numeroProgressivo;

        if (!numeroProgressivo) {
          // Get existing finalized verbali count for this direzione lavori to generate progressive number
          const existingVerbaliCount = await storage.getVerbaliFinalizatiCountByDirezioneLavori(id);

          // Generate progressive number based on existing verbali count
          const progressiveNumber = String(existingVerbaliCount + 1).padStart(3, '0');

          // Generate automatic numero progressivo: codiceCommessa-VDL-numeroProgressivo-AAAAMMGG
          const formattedDate = today.replace(/-/g, '');
          numeroProgressivo = `${direzioneLavori.codiceCommessa}-VDL-${progressiveNumber}-${formattedDate}`;
        }

        // Prepare data for verbale creation with required fields
        const finalData = {
          direzioneLavoriId: id,
          numeroProgressivo,
          tecnicoId,
          data: today,
          lavorazioniInCorso: verbaleData.lavorazioniInCorso || "",
          criticita: verbaleData.criticita || null,
          note: verbaleData.note || null,
          osservazioni: verbaleData.osservazioni || null,
          checklist: "[]",
          personePresenti: verbaleData.personePresenti || "[]",
          firme: JSON.stringify(firme || {}),
          pdfPath: null,
          stato: 'finalizzato', // Stato finalizzato quando firmato
          // Optional sections
          controlliDimensionali: verbaleData.controlliDimensionali || null,
          controlliDimensionaliDisegno: verbaleData.controlliDimensionaliDisegno || null,
          controlliDimensionaliFoto: verbaleData.controlliDimensionaliFoto || [],
          nonConformita: verbaleData.nonConformita || null,
          nonConformitaDisegno: verbaleData.controlliDimensionaliDisegno || null,
          controlliDimensionaliFoto: verbaleData.controlliDimensionaliFoto || [],
          indicazioniOperative: verbaleData.indicazioniOperative || null,
          indicazioniOperativeDisegno: verbaleData.indicazioniOperativeDisegno || null,
          indicazioniOperativeFoto: verbaleData.indicazioniOperativeFoto || []
        };

        // Validate the complete data
        const validatedData = insertVerbaleDirezioneLavoriSchema.parse(finalData);

        // Create the verbale
        verbale = await storage.createVerbaleDirezioneLavori(validatedData);
      }

      // Get the direttore lavori (tecnico) data for PDF generation
      const tecnico = await storage.getDirettoreLavori(verbale.tecnicoId);
      if (!tecnico) {
        return res.status(400).json({ message: "Tecnico not found" });
      }

      // Parse persone presenti if provided
      let personePresenti: any[] = [];
      try {
        if (verbale.personePresenti && verbale.personePresenti !== "[]") {
          personePresenti = JSON.parse(verbale.personePresenti);

          // Salva automaticamente le persone presenti nella rubrica
          for (const persona of personePresenti) {
            if (persona.nome && persona.qualifica) {
              await storage.addToRubricaPersonePresenti({
                direzioneLavoriId: id,
                nome: persona.nome,
                qualifica: persona.qualifica,
                email: persona.email || null,
                telefono: persona.telefono || null
              });
            }
          }
        }
      } catch (error) {
        console.error("Error parsing persone presenti:", error);
      }

      // Prepare data for PDF generation
      const verbaleWithData = {
        id: verbale.id,
        direzioneLavoriId: verbale.direzioneLavoriId,
        numeroProgressivo: verbale.numeroProgressivo,
        tecnicoId: verbale.tecnicoId,
        data: verbale.data,
        lavorazioniInCorso: verbale.lavorazioniInCorso,
        criticita: verbale.criticita,
        osservazioni: verbale.osservazioni,
        note: verbale.note,
        checklist: verbale.checklist,
        pdfPath: verbale.pdfPath,
        stato: verbale.stato,
        createdAt: verbale.createdAt,
        // Optional sections
        controlliDimensionali: verbale.controlliDimensionali,
        controlliDimensionaliDisegno: verbale.controlliDimensionaliDisegno,
        controlliDimensionaliFoto: verbale.controlliDimensionaliFoto,
        nonConformita: verbale.nonConformita,
        nonConformitaDisegno: verbale.nonConformitaDisegno,
        nonConformitaFoto: verbale.nonConformitaFoto,
        indicazioniOperative: verbale.indicazioniOperative,
        indicazioniOperativeDisegno: verbale.indicazioniOperativeDisegno,
        indicazioniOperativeFoto: verbale.indicazioniOperativeFoto,
        direzioneLavori,
        tecnico,
        personePresenti,
        firme: firme || {}
      };

      // Generate the PDF using the new function
      const pdfPath = await generatePDFVerbaleDirezioneLavori(verbaleWithData);

      // Update the verbale with the PDF path and finalized status
      const updatedVerbale = await storage.updateVerbaleDirezioneLavori(verbale.id, {
        pdfPath,
        firme: JSON.stringify(firme || {}),
        stato: 'finalizzato'
      });

      res.status(201).json(updatedVerbale);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating verbale direzione lavori with signatures:", error);
      res.status(500).json({ message: "Failed to create verbale with signatures" });
    }
  });

  // Get specific verbale direzione lavori
  app.get("/api/direzione-lavori/:lavoroId/verbali/:verbaleId", authenticateToken, async (req, res) => {
    try {
      const lavoroId = parseInt(req.params.lavoroId);
      const verbaleId = parseInt(req.params.verbaleId);

      if (isNaN(lavoroId) || isNaN(verbaleId)) {
        return res.status(400).json({ message: "Invalid IDs provided" });
      }

      const verbale = await storage.getVerbaleDirezioneLavori(verbaleId);
      if (!verbale) {
        return res.status(404).json({ message: "Verbale not found" });
      }

      // Get related data
      const lavoro = await storage.getDirezioneLavori(lavoroId);
      const tecnico = await storage.getDirettoreLavori(verbale.tecnicoId);

      if (!lavoro || !tecnico) {
        return res.status(404).json({ message: "Related data not found" });
      }

      const verbaleWithData = {
        ...verbale,
        direzioneLavori: lavoro,
        tecnico,
        checklist: verbale.checklist || '[]',
        personePresenti: verbale.personePresenti || '[]'
      };

      res.json(verbaleWithData);
    } catch (error) {
      console.error("Error fetching verbale direzione lavori:", error);
      res.status(500).json({ message: "Failed to fetch verbale" });
    }
  });

  // Update verbale direzione lavori
  app.put("/api/direzione-lavori/:lavoroId/verbali/:verbaleId", authenticateToken, async (req, res) => {
    try {
      const lavoroId = parseInt(req.params.lavoroId);
      const verbaleId = parseInt(req.params.verbaleId);
      if (isNaN(verbaleId) || isNaN(lavoroId)) {
        return res.status(400).json({ message: "Invalid IDs provided" });
      }

      // Get the current verbale state before update
      const currentVerbale = await storage.getVerbaleDirezioneLavori(verbaleId);
      if (!currentVerbale) {
        return res.status(404).json({ message: "Verbale not found" });
      }

      const validatedData = insertVerbaleDirezioneLavoriSchema.partial().parse(req.body);

      // Determine if verbale should be finalized BEFORE updating
      // Finalize if: explicitly set to finalizzato OR has signatures OR already had signatures
      const hasFirme = validatedData.firme && Object.keys(JSON.parse(validatedData.firme || '{}')).length > 0;
      const hadFirme = currentVerbale.firme && Object.keys(JSON.parse(currentVerbale.firme || '{}')).length > 0;
      const shouldFinalize = (
        validatedData.stato === 'finalizzato' ||
        hasFirme ||
        hadFirme
      );

      console.log('🔍 Verbale finalization check:', {
        verbaleId,
        currentStato: currentVerbale.stato,
        requestedStato: validatedData.stato,
        hasFirme,
        hadFirme,
        shouldFinalize
      });

      // Force finalization if conditions are met
      if (shouldFinalize) {
        validatedData.stato = 'finalizzato';
        console.log('✅ Forcing verbale finalization due to signatures or explicit request');
      }

      const verbale = await storage.updateVerbaleDirezioneLavori(verbaleId, validatedData);

      if (!verbale) {
        return res.status(404).json({ message: "Verbale not found" });
      }

      // If verbale was finalized, generate PDF if needed
      if (shouldFinalize && currentVerbale.stato === 'bozza') {
        const lavoro = await storage.getDirezioneLavori(lavoroId);
        const tecnico = await storage.getDirettoreLavori(verbale.tecnicoId);

        if (lavoro && tecnico) {
          let personePresentiParsed: any[] = [];
          try {
            if (verbale.personePresenti && verbale.personePresenti !== "[]") {
              personePresentiParsed = JSON.parse(verbale.personePresenti);
            }
          } catch (error) {
            console.error("Error parsing persone presenti:", error);
            personePresentiParsed = [];
          }

          const verbaleWithData = {
            ...verbale,
            direzioneLavori: lavoro,
            tecnico,
            checklist: verbale.checklist || '[]',
            personePresenti: personePresentiParsed
          };

          try {
            const pdfPath = await generatePDFVerbaleDirezioneLavori(verbaleWithData);
            // Update the verbale with the PDF path
            await storage.updateVerbaleDirezioneLavori(verbaleId, { pdfPath });
            verbale.pdfPath = pdfPath;
          } catch (pdfError) {
            console.error("Error generating PDF during finalization:", pdfError);
            // Don't fail the update, but log the error
          }
        }
      }

      res.json(verbale);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error updating verbale direzione lavori:", error);
      res.status(500).json({ message: "Failed to update verbale" });
    }
  });

  // Delete verbale direzione lavori (only drafts)
  app.delete("/api/direzione-lavori/:lavoroId/verbali/:verbaleId", authenticateToken, async (req, res) => {
    try {
      const verbaleId = parseInt(req.params.verbaleId);
      if (isNaN(verbaleId)) {
        return res.status(400).json({ message: "Invalid verbale ID" });
      }

      // Check if verbale exists
      const verbale = await storage.getVerbaleDirezioneLavori(verbaleId);
      if (!verbale) {
        return res.status(404).json({ message: "Verbale not found" });
      }

      // Allow deletion of any verbale (removed restriction to drafts only)

      const deleted = await storage.deleteVerbaleDirezioneLavori(verbaleId);
      if (!deleted) {
        return res.status(404).json({ message: "Verbale not found" });
      }

      res.json({ message: "Verbale deleted successfully" });
    } catch (error) {
      console.error("Error deleting verbale direzione lavori:", error);
      res.status(500).json({ message: "Failed to delete verbale" });
    }
  });

  // Route per ottenere rubrica persone presenti
  app.get("/api/direzione-lavori/:id/rubrica", authenticateToken, async (req, res) => {
    try {
      const direzioneLavoriId = parseInt(req.params.id);
      const rubrica = await storage.getRubricaPersonePresenti(direzioneLavoriId);
      res.json(rubrica);
    } catch (error) {
      console.error("Error fetching rubrica:", error);
      res.status(500).json({ message: "Errore durante il recupero della rubrica" });
    }
  });

  // Route per aggiungere persona alla rubrica
  app.post("/api/direzione-lavori/:id/rubrica", authenticateToken, async (req, res) => {
    try {
      const direzioneLavoriId = parseInt(req.params.id);
      const persona = await storage.addToRubricaPersonePresenti({
        ...req.body,
        direzioneLavoriId
      });
      res.json(persona);
    } catch (error) {
      console.error("Error adding to rubrica:", error);
      res.status(500).json({ message: "Errore durante l'aggiunta alla rubrica" });
    }
  });

  // Route per eliminare persona dalla rubrica
  app.delete("/api/direzione-lavori/:id/rubrica/:personaId", authenticateToken, async (req, res) => {
    try {
      const personaId = parseInt(req.params.personaId);
      const success = await storage.deleteFromRubricaPersonePresenti(personaId);

      if (success) {
        res.json({ message: "Persona eliminata dalla rubrica" });
      } else {
        res.status(404).json({ message: "Persona non trovata" });
      }
    } catch (error) {
      console.error("Error deleting from rubrica:", error);
      res.status(500).json({ message: "Errore durante la cancellazione dalla rubrica" });
    }
  });

  // Upload emergency PDF for verbale
  app.post("/api/direzione-lavori/:lavoroId/verbali/:verbaleId/upload-pdf", uploadLimiter, requireAuth, uploadPDF.single('pdf'), async (req, res) => {
    try {
      console.log("PDF Upload - Request received");
      console.log("PDF Upload - File:", req.file);
      console.log("PDF Upload - Body:", req.body);

      const verbaleId = parseInt(req.params.verbaleId);
      if (isNaN(verbaleId)) {
        console.log("PDF Upload - Invalid verbale ID:", req.params.verbaleId);
        return res.status(400).json({ message: "Invalid verbale ID" });
      }

      if (!req.file) {
        console.log("PDF Upload - No file provided");
        return res.status(400).json({ message: "No PDF file provided" });
      }

      // Validazione tipo file con magic numbers
      if (!validateFileType(req.file.buffer, 'pdf')) {
        return res.status(400).json({ message: 'Tipo file non valido. Solo PDF sono consentiti.' });
      }

      // Check if file is PDF
      if (req.file.mimetype !== 'application/pdf') {
        console.log("PDF Upload - Invalid file type:", req.file.mimetype);
        return res.status(400).json({ message: "File must be a PDF" });
      }

      // Check if verbale exists and is a draft
      const verbale = await storage.getVerbaleDirezioneLavori(verbaleId);
      console.log("PDF Upload - Verbale found:", verbale ? `ID ${verbale.id}, stato: ${verbale.stato}` : "Not found");

      if (!verbale) {
        return res.status(404).json({ message: "Verbale not found" });
      }

      if (verbale.stato !== 'bozza') {
        console.log("PDF Upload - Verbale is not a draft, stato:", verbale.stato);
        return res.status(400).json({ message: "PDF can only be uploaded for draft verbali" });
      }

      // Generate secure filename for PDF
      const filename = generateSecureFilename(req.file.originalname);

      // Upload PDF nel database
      const uploadResult = await databaseStorage.storePDF(verbaleId, req.file.buffer, filename);
      console.log(`PDF upload result: ${uploadResult.success ? 'SUCCESS' : 'FAILED'}`);

      // Update verbale with PDF filename and finalize it
      const updatedVerbale = await storage.updateVerbaleDirezioneLavori(verbaleId, {
        pdfPath: filename,
        stato: 'finalizzato'
      });

      console.log("PDF Upload - Verbale updated successfully:", updatedVerbale ? "Yes" : "No");

      res.json({ 
        message: "PDF uploaded successfully",
        verbale: updatedVerbale
      });
    } catch (error) {
      console.error("Error uploading PDF:", error);
      res.status(500).json({ message: "Failed to upload PDF" });
    }
  });

  // Unlock verbale direzione lavori (make it editable again)
  app.post("/api/direzione-lavori/:lavoroId/verbali/:verbaleId/unlock", authenticateToken, async (req, res) => {
    try {
      const { verbaleId } = req.params;
      const { reason } = req.body; // Motivo dello sblocco per audit

      const currentVerbale = await storage.getVerbaleDirezioneLavori(verbaleId);
      if (!currentVerbale) {
        return res.status(404).json({ message: "Verbale not found" });
      }

      if (currentVerbale.stato !== 'finalizzato') {
        return res.status(400).json({ message: "Solo verbali finalizzati possono essere sbloccati" });
      }

      // Sblocca verbale: torna a bozza, cancella firme e PDF
      const updatedVerbale = await storage.updateVerbaleDirezioneLavori(verbaleId, {
        stato: 'bozza',
        firme: null,
        pdfPath: null
      });

      // Log per audit
      console.log(`🔓 Verbale ${verbaleId} sbloccato per modifica. Motivo: ${reason || 'Non specificato'}`);

      res.json({
        success: true,
        verbale: updatedVerbale,
        message: "Verbale sbloccato per modifica. Le firme sono state cancellate."
      });

    } catch (error) {
      console.error('Error unlocking verbale:', error);
      res.status(500).json({ message: "Errore durante lo sblocco del verbale" });
    }
  });

  // Save verbale direzione lavori as draft
  app.post("/api/direzione-lavori/:id/verbali/salva-bozza", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      const { checklist, personePresenti, ...verbaleData } = req.body;

      // Get the direzione lavori to get the codice commessa
      const direzioneLavori = await storage.getDirezioneLavori(id);
      if (!direzioneLavori) {
        return res.status(404).json({ message: "Direzione lavori not found" });
      }

      // Get existing finalized verbali count for this direzione lavori to generate progressive number
      const existingVerbaliCount = await storage.getVerbaliFinalizatiCountByDirezioneLavori(id);

      // Generate progressive number based on existing verbali count
      const progressiveNumber = String(existingVerbaliCount + 1).padStart(3, '0');

      // Generate automatic numero progressivo: codiceCommessa-VDL-numeroProgressivo-AAAAMMGG
      const formattedDate = verbaleData.data.replace(/-/g, '');
      const numeroProgressivo = `${direzioneLavori.codiceCommessa}-VDL-${progressiveNumber}-${formattedDate}`;

      // Prepare data for verbale creation
      const finalData = {
        direzioneLavoriId: id,
        numeroProgressivo,
        tecnicoId: verbaleData.tecnicoId,
        data: verbaleData.data,
        lavorazioniInCorso: verbaleData.lavorazioniInCorso || "",
        criticita: verbaleData.criticita || null,
        note: verbaleData.note || null,
        osservazioni: verbaleData.osservazioni || null,
        checklist: "[]",
        personePresenti: verbaleData.personePresenti || "[]",
        firme: null,
        pdfPath: null,
        stato: 'bozza', // Stato bozza per il draft
        // Optional sections
        controlliDimensionali: verbaleData.controlliDimensionali || null,
        controlliDimensionaliDisegno: verbaleData.controlliDimensionaliDisegno || null,
        controlliDimensionaliFoto: verbaleData.controlliDimensionaliFoto || null,
        nonConformita: verbaleData.nonConformita || null,
        nonConformitaDisegno: verbaleData.nonConformitaDisegno || null,
        nonConformitaFoto: verbaleData.nonConformitaFoto || null,
        indicazioniOperative: verbaleData.indicazioniOperative || null,
        indicazioniOperativeDisegno: verbaleData.indicazioniOperativeDisegno || null,
        indicazioniOperativeFoto: verbaleData.indicazioniOperativeFoto || null,
      };

      const validatedData = insertVerbaleDirezioneLavoriSchema.parse(finalData);
      const verbale = await storage.createVerbaleDirezioneLavori(validatedData);

      res.status(201).json(verbale);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error saving draft verbale direzione lavori:", error);
      res.status(500).json({ message: "Failed to save draft verbale" });
    }
  });

  // Get direttori for a specific direzione lavori
  app.get("/api/direzione-lavori/:id/direttori", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direzione lavori ID" });
      }

      const direttori = await storage.getDirettoriByDirezioneLavori(id);
      res.json(direttori);
    } catch (error) {
      console.error("Error fetching direttori for direzione lavori:", error);
      res.status(500).json({ message: "Failed to fetch direttori" });
    }
  });

  // Add direttore to direzione lavori
  app.post("/api/direzione-lavori/:id/direttori", authenticateToken, async (req, res) => {
    try {
      const direzioneLavoriId = parseInt(req.params.id);
      const { direttoreLavoriId } = req.body;

      if (isNaN(direzioneLavoriId) || isNaN(direttoreLavoriId)) {
        return res.status(400).json({ message: "Invalid IDs provided" });
      }

      await storage.addDirettoreToCommessa(direzioneLavoriId, direttoreLavoriId);
      res.json({ message: "Direttore added successfully" });
    } catch (error) {
      console.error("Error adding direttore to direzione lavori:", error);
      res.status(500).json({ message: "Failed to add direttore" });
    }
  });

  // Remove direttore from direzione lavori
  app.delete("/api/direzione-lavori/:id/direttori/:direttoreId", async (req, res) => {
    try {
      const direzioneLavoriId = parseInt(req.params.id);
      const direttoreLavoriId = parseInt(req.params.direttoreId);

      if (isNaN(direzioneLavoriId) || isNaN(direttoreLavoriId)) {
        return res.status(400).json({ message: "Invalid IDs provided" });
      }

      await storage.removeDirettoreFromCommessa(direzioneLavoriId, direttoreLavoriId);
      res.json({ message: "Direttore removed successfully" });
    } catch (error) {
      console.error("Error removing direttore from direzione lavori:", error);
      res.status(500).json({ message: "Failed to remove direttore" });
    }
  });

  // Get all tecnici
  app.get("/api/tecnici", authenticateToken, async (req, res) => {
    try {
      const tecnici = await storage.getAllTecnici();
      res.json(tecnici);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch tecnici" });
    }
  });

  // Get single tecnico
  app.get("/api/tecnici/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid tecnico ID" });
      }

      const tecnico = await storage.getTecnico(id);
      if (!tecnico) {
        return res.status(404).json({ message: "Tecnico not found" });
      }

      res.json(tecnico);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch tecnico" });
    }
  });

  // Create new tecnico
  app.post("/api/tecnici", authenticateToken, async (req, res) => {
    try {
      const validatedData = insertTecnicoSchema.parse(req.body);
      const tecnico = await storage.createTecnico(validatedData);
      res.status(201).json(tecnico);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to create tecnico" });
    }
  });

  // Update tecnico
  app.put("/api/tecnici/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid tecnico ID" });
      }

      const validatedData = insertTecnicoSchema.partial().parse(req.body);
      const tecnico = await storage.updateTecnico(id, validatedData);

      if (!tecnico) {
        return res.status(404).json({ message: "Tecnico not found" });
      }

      res.json(tecnico);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to update tecnico" });
    }
  });

  // Delete tecnico
  app.delete("/api/tecnici/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid tecnico ID" });
      }

      const deleted = await storage.deleteTecnico(id);
      if (!deleted) {
        return res.status(404).json({ message: "Tecnico not found" });
      }

      res.json({ message: "Tecnico deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete tecnico" });
    }
  });

  // Get all direttori lavori
  app.get("/api/direttori-lavori", async (req, res) => {
    try {
      const direttori = await storage.getAllDirettoriLavori();
      res.json(direttori);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch direttori lavori" });
    }
  });

  // Get single direttore lavori
  app.get("/api/direttori-lavori/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direttore lavori ID" });
      }

      const direttore = await storage.getDirettoreLavori(id);
      if (!direttore) {
        return res.status(404).json({ message: "Direttore lavori not found" });
      }

      res.json(direttore);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch direttore lavori" });
    }
  });

  // Create new direttore lavori
  app.post("/api/direttori-lavori", async (req, res) => {
    try {
      const validatedData = insertDirettoreLavoriSchema.parse(req.body);
      const direttore = await storage.createDirettoreLavori(validatedData);
      res.status(201).json(direttore);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to create direttore lavori" });
    }
  });

  // Update direttore lavori
  app.put("/api/direttori-lavori/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direttore lavori ID" });
      }

      const validatedData = insertDirettoreLavoriSchema.partial().parse(req.body);
      const direttore = await storage.updateDirettoreLavori(id, validatedData);

      if (!direttore) {
        return res.status(404).json({ message: "Direttore lavori not found" });
      }

      res.json(direttore);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to update direttore lavori" });
    }
  });

  // Delete direttore lavori
  app.delete("/api/direttori-lavori/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid direttore lavori ID" });
      }

      const deleted = await storage.deleteDirettoreLavori(id);
      if (!deleted) {
        return res.status(404).json({ message: "Direttore lavori not found" });
      }

      res.json({ message: "Direttore lavori deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete direttore lavori" });
    }
  });

  // Get sopralluoghi for a specific cantiere
  app.get("/api/cantieri/:cantiereId/sopralluoghi", async (req, res) => {
    try {
      const cantiereId = parseInt(req.params.cantiereId);
      if (isNaN(cantiereId)) {
        return res.status(400).json({ message: "Invalid cantiere ID" });
      }

      const sopralluoghi = await storage.getSopralluoghiByCantiere(cantiereId);
      res.json(sopralluoghi);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch sopralluoghi" });
    }
  });

  // Create new sopralluogo
  app.post("/api/sopralluoghi", authenticateToken, async (req, res) => {
    try {
      const { checklist, personePresenti, ...sopralluogoData } = req.body;

      // Get cantiere info for generating correct numero sopralluogo
      const cantiere = await storage.getCantiere(sopralluogoData.cantiereId);
      if (!cantiere) {
        return res.status(400).json({ message: "Cantiere not found" });
      }

      // Get existing sopralluoghi count for this cantiere to generate progressive number
      const existingSopralluoghi = await storage.getSopralluoghiByCantiere(sopralluogoData.cantiereId);
      // Use existingSopralluoghi.length to match PDF numeroProgressivo
      const progressiveNumber = String(existingSopralluoghi.length).padStart(3, '0');

      // Generate correct numero sopralluogo: CODICECOMMESSA-CSE-NRPROGRESSIVO-AAAAMMGG
      const formattedDate = sopralluogoData.data.replace(/-/g, '');
      const numeroSopralluogo = `${cantiere.codiceCommessa}-CSE-${progressiveNumber}-${formattedDate}`;
      
      console.log(`📋 Creating CSE sopralluogo: existingCount=${existingSopralluoghi.length}, progressiveNumber=${progressiveNumber}, numeroSopralluogo=${numeroSopralluogo}`);

      const validatedData = insertSopralluogoSchema.parse({
        ...sopralluogoData,
        numeroSopralluogo,
        checklist: JSON.stringify(checklist || [])
      });

      // Crea il sopralluogo con il numero corretto
      const sopralluogo = await storage.createSopralluogo(validatedData);

      // Ottieni il tecnico per la generazione PDF
      const tecnico = await storage.getTecnico(sopralluogo.tecnicoId);

      if (!tecnico) {
        return res.status(400).json({ message: "Tecnico non trovato" });
      }

      // Calcola il numero progressivo di sopralluoghi per questo cantiere
      const sopralluoghiCantiere = await storage.getSopralluoghiByCantiere(sopralluogo.cantiereId);
      const numeroProgressivo = sopralluoghiCantiere.length;

      // Prepara i dati per il PDF
      const sopralluogoWithData = {
        ...sopralluogo,
        cantiere,
        tecnico,
        checklist: JSON.parse(sopralluogo.checklist || '[]'),
        personePresenti: personePresenti || [],
        numeroProgressivo
      };

      // Genera il PDF
      const verbaleUrl = await generatePDFVerbale(sopralluogoWithData);

      // Aggiorna il sopralluogo con l'URL del PDF
      const updatedSopralluogo = await storage.updateSopralluogo(sopralluogo.id, {
        verbaleUrl
      });

      res.status(201).json(updatedSopralluogo);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating sopralluogo:", error);
      res.status(500).json({ message: "Failed to create sopralluogo" });
    }
  });

  // Save sopralluogo as draft
  app.post("/api/sopralluoghi/salva-bozza", authenticateToken, async (req, res) => {
    try {
      const { checklist, personePresenti, ...sopralluogoData } = req.body;

      // Get cantiere info for generating correct numero sopralluogo
      const cantiere = await storage.getCantiere(sopralluogoData.cantiereId);
      if (!cantiere) {
        return res.status(400).json({ message: "Cantiere not found" });
      }

      // Get existing sopralluoghi count for this cantiere to generate progressive number
      const existingSopralluoghi = await storage.getSopralluoghiByCantiere(sopralluogoData.cantiereId);
      // Use existingSopralluoghi.length to match PDF numeroProgressivo
      const progressiveNumber = String(existingSopralluoghi.length).padStart(3, '0');

      // Generate correct numero sopralluogo: CODICECOMMESSA-CSE-NRPROGRESSIVO-AAAAMMGG
      const formattedDate = sopralluogoData.data.replace(/-/g, '');
      const numeroSopralluogo = `${cantiere.codiceCommessa}-CSE-${progressiveNumber}-${formattedDate}`;
      
      console.log(`📋 Creating CSE sopralluogo: existingCount=${existingSopralluoghi.length}, progressiveNumber=${progressiveNumber}, numeroSopralluogo=${numeroSopralluogo}`);

      const validatedData = insertSopralluogoSchema.parse({
        ...sopralluogoData,
        numeroSopralluogo,
        stato: 'bozza',
        checklist: JSON.stringify(checklist || [])
      });

      // Create draft sopralluogo
      const sopralluogo = await storage.createSopralluogo(validatedData);

      res.status(201).json(sopralluogo);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error saving draft sopralluogo:", error);
      res.status(500).json({ message: "Failed to save draft sopralluogo" });
    }
  });

  // Create new sopralluogo with digital signatures
  app.post("/api/sopralluoghi/con-firme", authenticateToken, async (req, res) => {
    try {
      const { checklist, personePresenti, firme, sopralluogoId, ...sopralluogoData } = req.body;

      let sopralluogo;

      if (sopralluogoId) {
        // Update existing draft to finalized
        const validatedData = insertSopralluogoSchema.partial().parse({
          ...sopralluogoData,
          stato: 'finalizzato',
          statoSopralluogo: "Completato"
        });

        sopralluogo = await storage.updateSopralluogo(sopralluogoId, validatedData);
        if (!sopralluogo) {
          return res.status(404).json({ message: "Sopralluogo draft not found" });
        }
      } else {
        // Get cantiere info for generating correct numero sopralluogo
        const cantiere = await storage.getCantiere(sopralluogoData.cantiereId);
        if (!cantiere) {
          return res.status(400).json({ message: "Cantiere not found" });
        }

        // Get existing sopralluoghi count for this cantiere to generate progressive number
        const existingSopralluoghi = await storage.getSopralluoghiByCantiere(sopralluogoData.cantiereId);
        // Use existingSopralluoghi.length to match PDF numeroProgressivo
        const progressiveNumber = String(existingSopralluoghi.length).padStart(3, '0');

        // Generate correct numero sopralluogo: CODICECOMMESSA-CSE-NRPROGRESSIVO-AAAAMMGG
        const formattedDate = sopralluogoData.data.replace(/-/g, '');
        const numeroSopralluogo = `${cantiere.codiceCommessa}-CSE-${progressiveNumber}-${formattedDate}`;
        
        console.log(`📋 Creating CSE sopralluogo: existingCount=${existingSopralluoghi.length}, progressiveNumber=${progressiveNumber}, numeroSopralluogo=${numeroSopralluogo}`);

        // Create new sopralluogo with corrected numero
        const validatedData = insertSopralluogoSchema.parse({
          ...sopralluogoData,
          numeroSopralluogo,
          stato: 'finalizzato',
          statoSopralluogo: "Completato",
          checklist: JSON.stringify(checklist || [])
        });
        sopralluogo = await storage.createSopralluogo(validatedData);
      }

      // Ottieni i dati completi per la generazione PDF
      const cantiere = await storage.getCantiere(sopralluogo.cantiereId);
      const tecnico = await storage.getTecnico(sopralluogo.tecnicoId);

      if (!cantiere || !tecnico) {
        return res.status(400).json({ message: "Cantiere o tecnico non trovato" });
      }

      // Calcola il numero progressivo di sopralluoghi per questo cantiere
      const sopralluoghiCantiere = await storage.getSopralluoghiByCantiere(sopralluogo.cantiereId);
      const numeroProgressivo = sopralluoghiCantiere.length;

      // Prepara i dati per il PDF con firme  
      const sopralluogoWithData = {
        ...sopralluogo,
        cantiere,
        tecnico,
        checklist: JSON.parse(sopralluogo.checklist || '[]'),
        personePresenti: personePresenti || [],
        numeroProgressivo,
        firme: firme || {}
      };

      // Genera il PDF con firme
      const verbaleUrl = await generatePDFVerbale(sopralluogoWithData);

      // Aggiorna il sopralluogo con l'URL del PDF
      const updatedSopralluogo = await storage.updateSopralluogo(sopralluogo.id, {
        verbaleUrl
      });

      res.status(201).json(updatedSopralluogo);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating sopralluogo with signatures:", error);
      res.status(500).json({ message: "Failed to create sopralluogo with signatures" });
    }
  });

  // Update sopralluogo
  app.put("/api/sopralluoghi/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid sopralluogo ID" });
      }

      const validatedData = insertSopralluogoSchema.partial().parse(req.body);
      const sopralluogo = await storage.updateSopralluogo(id, validatedData);

      if (!sopralluogo) {
        return res.status(404).json({ message: "Sopralluogo not found" });
      }

      res.json(sopralluogo);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: "Failed to update sopralluogo" });
    }
  });

  // Delete sopralluogo
  app.delete("/api/sopralluoghi/:id", authenticateToken, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid sopralluogo ID" });
      }

      const deleted = await storage.deleteSopralluogo(id);
      if (!deleted) {
        return res.status(404).json({ message: "Sopralluogo not found" });
      }

      res.json({ message: "Sopralluogo deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete sopralluogo" });
    }
  });

  // API endpoints for persone presenti
  app.get("/api/cantieri/:cantiereId/persone-presenti", async (req, res) => {
    try {
      const cantiereId = parseInt(req.params.cantiereId);
      const persone = await storage.getPersonePresentiBycantiere(cantiereId);
      res.json(persone);
    } catch (error) {
      console.error("Error fetching persone presenti:", error);
      res.status(500).json({ message: "Failed to fetch persone presenti" });
    }
  });

  app.post("/api/cantieri/:cantiereId/persone-presenti", async (req, res) => {
    try {
      const cantiereId = parseInt(req.params.cantiereId);
      const validatedData = insertPersonaPresenteSchema.parse({
        ...req.body,
        cantiereId
      });

      const persona = await storage.createPersonaPresente(validatedData);
      res.status(201).json(persona);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        });
      }
      console.error("Error creating persona presente:", error);
      res.status(500).json({ message: "Failed to create persona presente" });
    }
  });


  // ===== LOGO MANAGEMENT ENDPOINTS =====

  // Configurazione multer per upload loghi
  const logoUpload = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB max per logo
    },
    fileFilter: (req, file, cb) => {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Tipo di file non supportato. Usa JPG, PNG o GIF.'));
      }
    }
  });

  // Lista tutti i loghi
  app.get('/api/loghi', authenticateToken, async (req, res) => {
    try {
      const loghi = await databaseStorage.listLogos();
      res.json(loghi);
    } catch (error) {
      console.error('Errore durante il recupero dei loghi:', error);
      res.status(500).json({ message: 'Errore durante il recupero dei loghi' });
    }
  });

  // Upload nuovo logo
  app.post('/api/loghi/:nome', authenticateToken, logoUpload.single('logo'), async (req, res) => {
    try {
      const nome = req.params.nome;
      const descrizione = req.body.descrizione;
      
      if (!req.file) {
        return res.status(400).json({ message: 'Nessun file fornito' });
      }

      // Validazione tipo file con magic numbers
      if (!validateFileType(req.file.buffer, 'image')) {
        return res.status(400).json({ message: 'File non valido. Il file deve essere un\'immagine.' });
      }

      // Ottimizza l'immagine se necessario
      let processedBuffer = req.file.buffer;
      if (req.file.size > 1024 * 1024) { // Se > 1MB, comprimere
        processedBuffer = await sharp(req.file.buffer)
          .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
          .jpeg({ quality: 80 })
          .toBuffer();
      }

      const result = await databaseStorage.uploadLogo(
        nome,
        processedBuffer,
        req.file.mimetype,
        descrizione
      );

      if (result.success) {
        res.json({ 
          message: 'Logo caricato con successo',
          id: result.id,
          nome,
          size: processedBuffer.length
        });
      } else {
        res.status(500).json({ 
          message: 'Errore durante il caricamento del logo',
          error: result.error
        });
      }
    } catch (error) {
      console.error('Errore upload logo:', error);
      res.status(500).json({ 
        message: 'Errore durante il caricamento del logo',
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      });
    }
  });

  // Download logo
  app.get('/api/loghi/:nome', async (req, res) => {
    try {
      const nome = req.params.nome;
      
      const result = await databaseStorage.downloadLogo(nome);
      
      if (result.success && result.contenuto) {
        // Converti da base64 a buffer
        const logoBuffer = Buffer.from(result.contenuto, 'base64');
        
        res.set({
          'Content-Type': result.contentType || 'image/png',
          'Content-Length': logoBuffer.length.toString(),
          'Cache-Control': 'public, max-age=31536000', // Cache per 1 anno
        });
        
        res.send(logoBuffer);
      } else {
        res.status(404).json({ 
          message: 'Logo non trovato',
          error: result.error
        });
      }
    } catch (error) {
      console.error('Errore download logo:', error);
      res.status(500).json({ 
        message: 'Errore durante il download del logo',
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      });
    }
  });

  // Elimina logo
  app.delete('/api/loghi/:nome', authenticateToken, async (req, res) => {
    try {
      const nome = req.params.nome;
      
      const success = await databaseStorage.deleteLogo(nome);
      
      if (success) {
        res.json({ message: 'Logo eliminato con successo' });
      } else {
        res.status(500).json({ message: 'Errore durante l\'eliminazione del logo' });
      }
    } catch (error) {
      console.error('Errore eliminazione logo:', error);
      res.status(500).json({ 
        message: 'Errore durante l\'eliminazione del logo',
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      });
    }
  });

  // Statistiche storage database
  app.get('/api/database-storage/stats', authenticateToken, async (req, res) => {
    try {
      const stats = await databaseStorage.getStorageStats();
      res.json(stats);
    } catch (error) {
      console.error('Errore recupero statistiche:', error);
      res.status(500).json({ message: 'Errore durante il recupero delle statistiche' });
    }
  });

  // Analisi foto verbali per diagnostica
  app.get('/api/verbali/:verbaleId/foto-stats', authenticateToken, async (req, res) => {
    try {
      const verbaleId = parseInt(req.params.verbaleId);
      if (isNaN(verbaleId)) {
        return res.status(400).json({ message: 'ID verbale non valido' });
      }

      // Ottieni il verbale
      const verbale = await storage.getVerbaleDirezioneLavori(verbaleId);
      
      if (!verbale) {
        return res.status(404).json({ message: 'Verbale non trovato' });
      }

      // Analizza le foto di tutte le sezioni
      const sections = [
        { name: 'controlliDimensionali', photos: verbale.controlliDimensionaliFoto || [] },
        { name: 'nonConformita', photos: verbale.nonConformitaFoto || [] },
        { name: 'indicazioniOperative', photos: verbale.indicazioniOperativeFoto || [] }
      ];

      const sectionStats = [];
      let totalPhotos = 0;
      let totalBase64 = 0;
      let totalMissing = 0;
      let totalSize = 0;

      for (const section of sections) {
        const stats = await photoManager.analyzePhotoArray(section.photos);
        sectionStats.push({
          section: section.name,
          ...stats
        });
        
        totalPhotos += stats.total;
        totalBase64 += stats.base64Count;
        totalMissing += stats.missingCount;
        totalSize += stats.totalSize;
      }

      res.json({
        verbaleId,
        numeroProgressivo: verbale.numeroProgressivo,
        summary: {
          totalPhotos,
          totalBase64,
          totalMissing,
          totalSize: photoManager.formatFileSize(totalSize),
          migrationNeeded: totalPhotos > totalBase64
        },
        sections: sectionStats
      });
    } catch (error) {
      console.error('Errore analisi foto verbale:', error);
      res.status(500).json({ message: 'Errore durante l\'analisi delle foto' });
    }
  });


  // Create and return HTTP server
  const httpServer = createServer(app);
  return httpServer;
}