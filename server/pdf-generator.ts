import { jsPDF } from 'jspdf';
import * as path from 'path';
import { join } from 'path';
import { readFileSync, existsSync } from 'fs';
import sizeOf from 'image-size';
import sharp from 'sharp';
import * as EXIF from 'exif-js';
import { databaseStorageManager as databaseStorage } from './database-storage';
import { photoManager } from './photo-manager';

// Helper function to convert image to base64 for PDF embedding with EXIF orientation correction
async function getImageBase64Corrected(photoPath: string): Promise<string | null> {
  try {
    // Se è un filename, prova prima Object Storage
    let filename: string;
    if (photoPath.includes('/')) {
      filename = photoPath.split('/').pop() || photoPath;
    } else {
      filename = photoPath;
    }
    
    // Smart photo loading: base64 or filesystem path
    let imageBuffer: Buffer;
    
    // Check if it's already base64 data
    if (photoPath.startsWith('data:image/')) {
      // Already base64, return as-is
      console.log(`📷 Photo already in base64 format`);
      return photoPath;
    }
    
    // Try to load from filesystem (legacy support)
    const fullPath = join(process.cwd(), photoPath);
    if (!existsSync(fullPath)) {
      console.error(`📷 Photo file not found: ${fullPath} - may need re-upload`);
      return null;
    }
    
    imageBuffer = readFileSync(fullPath);
    console.log(`📷 Photo loaded from filesystem: ${fullPath}`);
    
    // Apply EXIF orientation correction and maintain compressed size
    try {
      const processedBuffer = await sharp(imageBuffer)
        .rotate() // Automatic EXIF orientation correction
        .jpeg({ quality: 75 }) // Maintain quality
        .toBuffer();
      
      console.log(`📷 Photo EXIF corrected: ${fullPath}`);
      return `data:image/jpeg;base64,${processedBuffer.toString('base64')}`;
    } catch (error) {
      console.warn('Sharp processing failed, using original image:', error);
      return `data:image/jpeg;base64,${imageBuffer.toString('base64')}`;
    }
    
  } catch (error) {
    console.error(`Error processing photo ${photoPath}:`, error);
    return null;
  }
}

// Backward compatibility - keep original function for other uses
async function getImageBase64(photoPath: string): Promise<string | null> {
  try {
    const fullPath = join(process.cwd(), photoPath);
    if (!existsSync(fullPath)) {
      console.error(`Photo not found: ${fullPath}`);
      return null;
    }
    const imageBuffer = readFileSync(fullPath);
    
    // Applica correzione orientamento EXIF e riduce risoluzione per PDF
    try {
      const processedBuffer = await sharp(imageBuffer)
        .rotate() // Correzione automatica EXIF
        .resize(800, 600, { fit: 'inside', withoutEnlargement: true }) // Riduce risoluzione
        .jpeg({ quality: 75 }) // Riduce qualità per dimensioni minori
        .toBuffer();
      
      return `data:image/jpeg;base64,${processedBuffer.toString('base64')}`;
    } catch (error) {
      console.log('Sharp processing not available, using original');
      return `data:image/jpeg;base64,${imageBuffer.toString('base64')}`;
    }
  } catch (error) {
    console.error(`Error reading photo ${photoPath}:`, error);
    return null;
  }
}

async function getLogoBase64(logoPath: string): Promise<string | null> {
  try {
    if (!logoPath) return null;
    
    // Se è un path API, estrai il filename
    let filename: string;
    if (logoPath.startsWith('/api/files/logos/')) {
      filename = logoPath.replace('/api/files/logos/', '');
    } else if (logoPath.startsWith('logos/')) {
      filename = logoPath.replace('logos/', '');
    } else {
      // Prova a usare il path così com'è
      filename = logoPath.split('/').pop() || logoPath;
    }
    
    // Rimuovi l'estensione dal filename per la ricerca nel database
    const filenameWithoutExt = filename.split('.')[0];
    
    // Prima prova Database Storage con nome senza estensione
    const result = await databaseStorage.downloadLogo(filenameWithoutExt);
    
    if (result.success && result.contenuto) {
      return result.contenuto; // È già in formato base64
    }
    
    // Fallback: prova il path originale nel filesystem
    const cleanPath = logoPath.startsWith('/') ? logoPath.substring(1) : logoPath;
    const fullPath = join(process.cwd(), cleanPath);
    
    if (existsSync(fullPath)) {
      const imageBuffer = readFileSync(fullPath);
      const extension = logoPath.toLowerCase().split('.').pop();
      const mimeType = extension === 'png' ? 'png' : 'jpeg';
      return `data:image/${mimeType};base64,${imageBuffer.toString('base64')}`;
    }
    
    console.error(`Logo not found: ${logoPath} (tried Database Storage and local filesystem)`);
    return null;
    
  } catch (error) {
    console.error('Error reading logo:', error);
    return null;
  }
}
import type { Cantiere, Sopralluogo, Tecnico, DirezioneLavori, VerbaleDirezioneLavori, DirettoreLavori } from '@shared/schema';

type ChecklistItem = {
  id: string;
  categoria: string;
  domanda: string;
  risposta?: "conforme" | "non_conforme" | "non_pertinente";
  note?: string;
  foto?: string[]; // Array di nomi file delle foto salvate
};

type PersonaPresente = {
  id: string;
  nome: string;
  qualifica: string;
  email?: string;
  telefono?: string;
};

type SopralluogoWithData = Sopralluogo & {
  cantiere: Cantiere;
  tecnico: Tecnico;
  checklist: ChecklistItem[];
  personePresenti?: PersonaPresente[];
  numeroProgressivo: number;
  firme?: Record<string, string>;
};

// Funzione per simulare testo giustificato
function addJustifiedText(doc: any, text: string, x: number, y: number, maxWidth: number, lineHeight: number = 4): number {
  const lines = doc.splitTextToSize(text, maxWidth);
  let currentY = y;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const isLastLine = i === lines.length - 1;
    
    if (!isLastLine && line.trim().split(' ').length > 1) {
      // Giustifica la riga (tranne l'ultima)
      const words = line.trim().split(' ');
      const lineWidth = doc.getTextWidth(line);
      const spaceToDistribute = maxWidth - lineWidth;
      const gaps = words.length - 1;
      
      if (gaps > 0) {
        const extraSpacePerGap = spaceToDistribute / gaps;
        let currentX = x;
        
        for (let j = 0; j < words.length; j++) {
          doc.text(words[j], currentX, currentY);
          if (j < words.length - 1) {
            currentX += doc.getTextWidth(words[j]) + doc.getTextWidth(' ') + extraSpacePerGap;
          }
        }
      } else {
        doc.text(line, x, currentY);
      }
    } else {
      // Ultima riga o riga con una sola parola - allineamento normale
      doc.text(line, x, currentY);
    }
    
    currentY += lineHeight;
  }
  
  return currentY;
}

// Funzione per aggiungere testo con gestione automatica page break
function addTextWithPageBreak(doc: any, text: string, x: number, startY: number, maxWidth: number, lineHeight: number = 6): number {
  const lines = doc.splitTextToSize(text, maxWidth);
  let currentY = startY;
  const pageHeight = doc.internal.pageSize.height;
  const footerMargin = 30; // Spazio riservato per footer/timestamp

  for (let i = 0; i < lines.length; i++) {
    // Controlla se la riga corrente supererebbe il margine del footer
    if (currentY + lineHeight > pageHeight - footerMargin) {
      doc.addPage();
      currentY = 20; // Margine top nuova pagina

      // Aggiungi timestamp watermark su nuova pagina
      addTimestampWatermark(doc);
    }

    doc.text(lines[i], x, currentY);
    currentY += lineHeight;
  }

  return currentY;
}

// Funzione per aggiungere timestamp watermark
function addTimestampWatermark(doc: any): void {
  const timestamp = new Date().toLocaleString('it-IT', {
    timeZone: 'Europe/Rome',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });

  doc.setFontSize(8);
  doc.setTextColor(150, 150, 150);
  doc.text(`Generato: ${timestamp}`, 10, 290, { angle: 90 });
  doc.setTextColor(0, 0, 0); // Reset color
}

export async function generatePDFVerbale(sopralluogoData: SopralluogoWithData): Promise<string> {
  console.log('Generating PDF with signatures:', sopralluogoData.firme ? Object.keys(sopralluogoData.firme) : 'No signatures');
  // Inizializza PDF (compressione rimossa temporaneamente per compatibilità)
  const doc = new jsPDF();
  let yPosition = 20;
  const pageWidth = doc.internal.pageSize.width;
  const margin = 20;
  const maxWidth = pageWidth - 2 * margin;

  // Intestazione con logo
  try {
    // Prova a scaricare il logo dal database
    let logoBuffer = null;
    
    try {
      const logoResult = await databaseStorage.downloadLogo('orbyta_logo');
      if (logoResult.success && logoResult.data) {
        logoBuffer = logoResult.data;
      }
    } catch (error) {
      console.log('⚠️ Logo aziendale non trovato nel database');
    }
    let logoBase64 = null;
    let logoType = 'JPEG'; // Default per orbyta_logo.jpg
    
    if (logoBuffer) {
      logoBase64 = logoBuffer.toString('base64');
    }
    
    if (logoBase64) {
      // Aggiungi logo nelle dimensioni ottimizzate
      const logoX = 10; // Più a sinistra
      const logoY = 10; // Più in alto
      // Dimensioni ottimizzate per il nuovo logo
      const logoWidth = 60;
      const logoHeight = 15;
      doc.addImage(`data:image/${logoType.toLowerCase()};base64,${logoBase64}`, logoType, logoX, logoY, logoWidth, logoHeight);
      
      // Il resto del contenuto mantiene la posizione originale
      yPosition = 50; // Maggiore spazio per evitare sovrapposizione con il logo
    } else {
      // Fallback senza logo
      yPosition = 50;
    }
  } catch (error) {
    // Fallback senza logo in caso di errore
    console.error('Error loading logo for safety verbale:', error);
    yPosition = 50;
  }

  // Header documento
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('VERBALE DI SOPRALLUOGO', pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 12;

  // Informazioni generali - Tabella
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('INFORMAZIONI GENERALI', margin, yPosition);
  yPosition += 3;

  // Prepara i dati per la tabella informazioni generali
  const dataObj = new Date(sopralluogoData.data);
  const dataFormattedInfo = dataObj.toLocaleDateString('it-IT');
  
  // Usa l'ora corrente per la generazione del PDF con fuso orario italiano
  const now = new Date();
  const oraFormatted = now.toLocaleTimeString('it-IT', { 
    hour: '2-digit', 
    minute: '2-digit',
    timeZone: 'Europe/Rome'
  });
  
  const infoGeneraliData = [
    ['Identificativo Sopralluogo', sopralluogoData.numeroSopralluogo.toUpperCase()],
    ['Numero Sopralluogo', sopralluogoData.numeroProgressivo?.toString() || sopralluogoData.id.toString()],
    ['Data', dataFormattedInfo],
    ['Ora', oraFormatted],
    ['Codice Commessa', sopralluogoData.cantiere.codiceCommessa.toUpperCase()],
    ['Indirizzo', sopralluogoData.cantiere.indirizzo.toUpperCase()],
    ['Committente', sopralluogoData.cantiere.committente.toUpperCase()],
    ['Oggetto', sopralluogoData.cantiere.oggetto.toUpperCase()],
    ['CSE Nominato', sopralluogoData.cantiere.cseNominato.toUpperCase()],
    ['Tecnico Incaricato', `${sopralluogoData.tecnico.nome} ${sopralluogoData.tecnico.cognome}`.toUpperCase()],
    ['Specializzazione', sopralluogoData.tecnico.specializzazione.toUpperCase()]
  ];

  // Tabella informazioni generali
  const infoTableWidth = maxWidth;
  const labelWidth = infoTableWidth * 0.3;
  const valueWidth = infoTableWidth * 0.7;
  const infoRowHeight = 8;

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.setDrawColor(200, 200, 200);
  
  infoGeneraliData.forEach(([label, value]) => {
    // Calcola altezza dinamica basata sul testo
    doc.setFont('helvetica', 'normal');
    const valueLines = doc.splitTextToSize(value, valueWidth - 4);
    const dynamicRowHeight = Math.max(infoRowHeight, valueLines.length * 4 + 4);
    
    // Disegna le celle con altezza dinamica
    doc.rect(margin, yPosition, labelWidth, dynamicRowHeight);
    doc.rect(margin + labelWidth, yPosition, valueWidth, dynamicRowHeight);
    
    // Sfondo grigio per le etichette
    doc.setFillColor(240, 240, 240);
    doc.rect(margin, yPosition, labelWidth, dynamicRowHeight, 'F');
    
    // Testo etichetta (grassetto) centrato verticalmente
    doc.setFont('helvetica', 'bold');
    const labelY = yPosition + dynamicRowHeight / 2 + 1;
    doc.text(label, margin + 2, labelY);
    
    // Testo valore (normale) inizia dal top della cella
    doc.setFont('helvetica', 'normal');
    doc.text(valueLines, margin + labelWidth + 2, yPosition + 5.5);
    
    yPosition += dynamicRowHeight;
  });

  yPosition += 5;

  // Note generali
  if (sopralluogoData.note) {
    doc.setFont('helvetica', 'bold');
    doc.text('NOTE GENERALI:', margin, yPosition);
    yPosition += 4;
    
    doc.setFont('helvetica', 'normal');
    const noteLines = doc.splitTextToSize(sopralluogoData.note.toUpperCase(), maxWidth);
    doc.text(noteLines, margin, yPosition);
    yPosition += noteLines.length * 4 + 10;
  } else {
    yPosition += 10;
  }

  // Titolo Premessa
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.text('PREMESSA', margin, yPosition);
  yPosition += 8;

  // Sezione introduttiva con persone presenti
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(0, 0, 0);
  
  const introduzione = "Ai sensi di quanto previsto dalla normativa in materia, il presente verbale viene redatto in ottemperanza agli obblighi previsti per il Coordinatore per la Sicurezza in fase Esecutiva – CSE (art. 92 D.lgs. 81/08 e s.m.i.). In occasione della visita presso il cantiere in oggetto, in presenza dei Sig.ri:";
  
  const introLines = doc.splitTextToSize(introduzione, maxWidth);
  doc.text(introLines, margin, yPosition);
  yPosition += introLines.length * 3 + 6;

  // Tabella persone presenti
  if (sopralluogoData.personePresenti && sopralluogoData.personePresenti.length > 0) {
    const personeTableWidth = maxWidth;
    const nomeWidth = personeTableWidth * 0.5;
    const qualificaWidth = personeTableWidth * 0.5;
    const personaRowHeight = 8;

    // Header tabella persone
    doc.setFillColor(240, 240, 240);
    doc.rect(margin, yPosition, personeTableWidth, personaRowHeight, 'F');
    
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(9);
    doc.setDrawColor(200, 200, 200);
    doc.rect(margin, yPosition, nomeWidth, personaRowHeight);
    doc.rect(margin + nomeWidth, yPosition, qualificaWidth, personaRowHeight);
    
    doc.text('Nome e Cognome', margin + 2, yPosition + 5.5);
    doc.text('Qualifica', margin + nomeWidth + 2, yPosition + 5.5);
    yPosition += personaRowHeight;

    // Righe persone
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(8);
    
    sopralluogoData.personePresenti.forEach(persona => {
      doc.setDrawColor(200, 200, 200);
      doc.rect(margin, yPosition, nomeWidth, personaRowHeight);
      doc.rect(margin + nomeWidth, yPosition, qualificaWidth, personaRowHeight);
      
      doc.text(persona.nome.toUpperCase(), margin + 2, yPosition + 5.5);
      doc.text(persona.qualifica.toUpperCase(), margin + nomeWidth + 2, yPosition + 5.5);
      yPosition += personaRowHeight;
    });
    
    yPosition += 8;
  }

  // Sezione Analisi delle lavorazioni in corso
  if (sopralluogoData.lavorazioniInCorso && sopralluogoData.lavorazioniInCorso.trim() !== "") {
    yPosition += 8;
    
    // Titolo sezione lavorazioni
    doc.setFontSize(11);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('ANALISI DELLE LAVORAZIONI IN CORSO - PROCEDURE', margin, yPosition);
    yPosition += 8;
    
    // Contenuto lavorazioni in corso (in maiuscolo come richiesto)
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(0, 0, 0);
    
    const lavorazioniText = sopralluogoData.lavorazioniInCorso.toUpperCase();
    const lavorazioniLines = doc.splitTextToSize(lavorazioniText, maxWidth);
    doc.text(lavorazioniLines, margin, yPosition);
    yPosition += lavorazioniLines.length * 4 + 8;
  }

  // Forza nuova pagina per checklist di sicurezza
  doc.addPage();
  yPosition = 20;

  // Titolo Checklist di Sicurezza
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.text('CHECKLIST DI SICUREZZA', margin, yPosition);
  yPosition += 8;

  // Legenda
  doc.setFontSize(10);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.text('LEGENDA:', margin, yPosition);
  yPosition += 5;
  
  doc.setFont('helvetica', 'normal');
  doc.text('C = Conforme    NC = Non Conforme    NP = Non Pertinente', margin, yPosition);
  yPosition += 8;

  // Riferimenti normativi
  const categorieConRiferimenti = [
    {
      nome: "Organizzazione generale del cantiere",
      riferimentoNormativo: "Rif. Sezione II – Disposizioni di carattere generale - Titolo IV – D.lgs. n°81/08 e s.m.i. – in particolare: artt. 108, 109, 110, 114"
    },
    {
      nome: "Scavi", 
      riferimentoNormativo: "Rif. Sezione III – Scavi e Fondazioni - Titolo IV – D.lgs. n°81/08 e s.m.i. – in particolare: artt. 118 e 119"
    },
    {
      nome: "Scale",
      riferimentoNormativo: "Rif. Sezione II – Disposizioni di carattere generale - Titolo IV – D.lgs. n°81/08 e s.m.i. – in particolare: artt. 111, 113"
    },
    {
      nome: "Ponteggi Fissi e mobili",
      riferimentoNormativo: "Rif. Sezioni IV – V - VI – \"Ponteggi in legname ed altre opere provvisionali\"; \"Ponteggi fissi\"; \"Ponteggi movibili\" - Titolo IV – D.lgs. n°81/08 e s.m.i. – ALLEGATI XVIII e XIX"
    },
    {
      nome: "Organizzazione e tenuta del Cantiere",
      riferimentoNormativo: "Attrezzature, mezzi, apparecchi di sollevamento, protezione aperture, passarelle, ecc."
    }
  ];

  // Raggruppa checklist per categoria
  const categorie = Array.from(new Set(sopralluogoData.checklist.map(item => item.categoria)));
  
  for (const categoria of categorie) {
    // Verifica se serve una nuova pagina (lascia spazio per piè di pagina)
    if (yPosition > 220) {
      doc.addPage();
      yPosition = 20;
    }

    // Titolo categoria centrato
    doc.setFontSize(11);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text(categoria, pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 4;

    // Riferimento normativo centrato
    const categoriaInfo = categorieConRiferimenti.find(cat => cat.nome === categoria);
    if (categoriaInfo) {
      doc.setFontSize(8);
      doc.setFont('helvetica', 'italic');
      const riferimentoLines = doc.splitTextToSize(categoriaInfo.riferimentoNormativo, maxWidth);
      for (const line of riferimentoLines) {
        doc.text(line, pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 3;
      }
      yPosition += 2;
    }

    // Header tabella
    const tableStartY = yPosition;
    const colWidths = [8, 90, 25, 25, 25]; // N°, Descrizione, C, NC, NP
    const rowHeight = 8;
    
    // Header
    doc.setFillColor(240, 240, 240);
    doc.rect(margin, yPosition, colWidths.reduce((a, b) => a + b, 0), rowHeight, 'F');
    
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(9);
    doc.setTextColor(0, 0, 0);
    
    let currentX = margin;
    doc.text('N°', currentX + 2, yPosition + 5);
    currentX += colWidths[0];
    doc.text('DESCRIZIONE', currentX + 2, yPosition + 5);
    currentX += colWidths[1];
    doc.text('C', currentX + 10, yPosition + 5);
    currentX += colWidths[2];
    doc.text('NC', currentX + 9, yPosition + 5);
    currentX += colWidths[3];
    doc.text('NP', currentX + 9, yPosition + 5);
    
    yPosition += rowHeight;

    // Items della categoria
    const itemsCategoria = sopralluogoData.checklist.filter(item => item.categoria === categoria);
    
    for (let index = 0; index < itemsCategoria.length; index++) {
      const item = itemsCategoria[index];
      // Verifica se serve una nuova pagina
      if (yPosition > 270) {
        doc.addPage();
        yPosition = 20;
        
        // Ripeti header tabella
        doc.setFillColor(240, 240, 240);
        doc.rect(margin, yPosition, colWidths.reduce((a, b) => a + b, 0), rowHeight, 'F');
        
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(9);
        doc.setTextColor(0, 0, 0);
        
        let headerX = margin;
        doc.text('N°', headerX + 2, yPosition + 5);
        headerX += colWidths[0];
        doc.text('DESCRIZIONE', headerX + 2, yPosition + 5);
        headerX += colWidths[1];
        doc.text('C', headerX + 10, yPosition + 5);
        headerX += colWidths[2];
        doc.text('NC', headerX + 9, yPosition + 5);
        headerX += colWidths[3];
        doc.text('NP', headerX + 9, yPosition + 5);
        
        yPosition += rowHeight;
      }

      // Calcola altezza riga in base al testo
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      const domandaLines = doc.splitTextToSize(item.domanda, colWidths[1] - 4);
      const currentRowHeight = Math.max(rowHeight, domandaLines.length * 4 + 4);

      // Verifica se c'è spazio sufficiente per la riga (lascia 30px per piè di pagina)
      if (yPosition + currentRowHeight > doc.internal.pageSize.height - 30) {
        doc.addPage();
        yPosition = 20;
        
        // Ridisegna header su nuova pagina
        doc.setFillColor(240, 240, 240);
        doc.rect(margin, yPosition, colWidths.reduce((a, b) => a + b, 0), rowHeight, 'F');
        
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(9);
        doc.setTextColor(0, 0, 0);
        
        let headerX = margin;
        doc.text('N°', headerX + 2, yPosition + 5);
        headerX += colWidths[0];
        doc.text('DESCRIZIONE', headerX + 2, yPosition + 5);
        headerX += colWidths[1];
        doc.text('C', headerX + 10, yPosition + 5);
        headerX += colWidths[2];
        doc.text('NC', headerX + 9, yPosition + 5);
        headerX += colWidths[3];
        doc.text('NP', headerX + 9, yPosition + 5);
        
        yPosition += rowHeight;
        
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(8);
      }

      // Bordi celle
      doc.setDrawColor(200, 200, 200);
      let cellX = margin;
      
      // Cella numero
      doc.rect(cellX, yPosition, colWidths[0], currentRowHeight);
      doc.text(`${index + 1}`, cellX + 2, yPosition + 5);
      cellX += colWidths[0];
      
      // Cella descrizione
      doc.rect(cellX, yPosition, colWidths[1], currentRowHeight);
      doc.text(domandaLines, cellX + 2, yPosition + 5);
      cellX += colWidths[1];
      
      // Celle risposte con indicatori
      for (let i = 0; i < 3; i++) {
        doc.rect(cellX, yPosition, colWidths[2 + i], currentRowHeight);
        
        const risposte = ['conforme', 'non_conforme', 'non_pertinente'];
        if (item.risposta === risposte[i]) {
          // Pallino colorato al centro della cella
          const circleX = cellX + colWidths[2 + i] / 2;
          const circleY = yPosition + currentRowHeight / 2;
          const circleRadius = 3;
          
          if (i === 0) doc.setFillColor(34, 197, 94); // Verde
          else if (i === 1) doc.setFillColor(239, 68, 68); // Rosso
          else doc.setFillColor(107, 114, 128); // Grigio
          
          doc.circle(circleX, circleY, circleRadius, 'F');
        }
        
        cellX += colWidths[2 + i];
      }
      
      yPosition += currentRowHeight;

      // Riga aggiuntiva per note e foto nei punti non conformi
      if (item.risposta === 'non_conforme' && (item.note?.trim() || (item.foto && item.foto.length > 0))) {
        // Layout orizzontale con foto più grandi richiede più altezza
        const noteRowHeight = 34; // Altezza aumentata per contenere foto 30px + padding
        
        // Verifica spazio per riga note (lascia 30px per piè di pagina)
        if (yPosition + noteRowHeight > doc.internal.pageSize.height - 30) {
          doc.addPage();
          yPosition = 20;
          
          // Ridisegna header su nuova pagina
          doc.setFillColor(240, 240, 240);
          doc.rect(margin, yPosition, colWidths.reduce((a, b) => a + b, 0), rowHeight, 'F');
          
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(9);
          doc.setTextColor(0, 0, 0);
          
          let headerX = margin;
          doc.text('N°', headerX + 2, yPosition + 5);
          headerX += colWidths[0];
          doc.text('DESCRIZIONE', headerX + 2, yPosition + 5);
          headerX += colWidths[1];
          doc.text('C', headerX + 10, yPosition + 5);
          headerX += colWidths[2];
          doc.text('NC', headerX + 9, yPosition + 5);
          headerX += colWidths[3];
          doc.text('NP', headerX + 9, yPosition + 5);
          
          yPosition += rowHeight;
        }
        
        // Bordo per la riga delle note
        doc.setDrawColor(200, 200, 200);
        let noteCellX = margin;
        
        // Cella numero vuota
        doc.rect(noteCellX, yPosition, colWidths[0], noteRowHeight);
        noteCellX += colWidths[0];
        
        // Cella descrizione con note e foto
        doc.rect(noteCellX, yPosition, colWidths[1], noteRowHeight);
        
        let noteY = yPosition + 4;
        
        if (item.note && item.note.trim() !== '') {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(7);
          doc.setTextColor(239, 68, 68);
          doc.text('NOTE:', noteCellX + 2, noteY);
          
          doc.setFont('helvetica', 'normal');
          doc.setTextColor(0, 0, 0);
          doc.setFontSize(6);
          
          // Calcola spazio disponibile per il testo (lascia spazio per le foto se presenti)
          const textWidth = (item.foto && item.foto.length > 0) ? colWidths[1] - 35 : colWidths[1] - 6;
          const noteLines = doc.splitTextToSize(item.note.toUpperCase(), textWidth);
          doc.text(noteLines, noteCellX + 2, noteY + 3);
        }
        
        // Gestisci multiple foto affiancate orizzontalmente
        if (item.foto && item.foto.length > 0) {
          const photoWidth = 40; // Dimensione aumentata per migliore visibilità
          const photoHeight = 30; // Proporzionale mantenendo ratio 4:3
          const photoSpacing = 2; // Spazio tra foto
          const startX = noteCellX + colWidths[1] - (item.foto.length * (photoWidth + photoSpacing)) - 2;
          
          // Layout orizzontale per foto checklist usando foto originali
          for (let index = 0; index < item.foto.length; index++) {
            const foto = item.foto[index];
            try {
              console.log('Trying to load photo:', foto);
              
              // Usa foto originali senza alterazioni per preservare proporzioni
              const base64Image = await getImageBase64Corrected(foto);
              
              if (base64Image) {
                  // Posiziona le foto una affianco all'altra
                  const photoX = startX + (index * (photoWidth + photoSpacing));
                  const photoY = yPosition + 2;
                  
                  console.log(`Adding image ${index + 1} to PDF at position:`, photoX, photoY);
                  doc.addImage(base64Image, 'JPEG', photoX, photoY, photoWidth, photoHeight);
              } else {
                console.log('Photo file not found:', foto);
              }
            } catch (error) {
              console.error('Error loading photo:', error);
            }
          }
        }
        
        noteCellX += colWidths[1];
        
        // Celle risposte vuote
        for (let i = 0; i < 3; i++) {
          doc.rect(noteCellX, yPosition, colWidths[2 + i], noteRowHeight);
          noteCellX += colWidths[2 + i];
        }
        
        yPosition += noteRowHeight;
      }
    }

    yPosition += 8;
  }

  // Verifica se serve una nuova pagina per il testo conclusivo
  if (yPosition > 180) {
    doc.addPage();
    yPosition = 20;
  }

  // Testo conclusivo
  yPosition += 10;
  doc.setFontSize(9);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(0, 0, 0);
  
  const testoConclusivoFirstPart = "Si ricorda che prima dell'inizio dei lavori di competenza, le imprese esecutrici devono trasmettere alle imprese affidatarie i propri piani operativi; l'impresa affidataria lo trasmette al coordinatore per l'esecuzione solo dopo aver verificato la congruenza rispetto al proprio. Le imprese esecutrici che non hanno provveduto in tal senso e che quindi risultano sprovviste di P.O.S. non possono operare sul cantiere.";
  
  const testoConclusivoSecondPart = "Si rammenta, altresì, che eventuali proposte di modifica migliorativa al PSC da parte delle ditte esecutrici ed affidatarie devono essere preventivamente discusse e valutate con il CSE e risulteranno accettate solo in conseguenza del relativo adeguamento/inserimento all'interno del PSC stesso, nonché del POS specifico.";
  
  yPosition = addJustifiedText(doc, testoConclusivoFirstPart, margin, yPosition, maxWidth, 4);
  yPosition += 6;
  
  yPosition = addJustifiedText(doc, testoConclusivoSecondPart, margin, yPosition, maxWidth, 4);
  yPosition += 10;

  // Testo incorniciato
  const testoIncorniciato = "Il presente verbale di coordinamento costituisce integrazione/modifica del PSC in relazione ad eventuali analisi ed approfondimenti riguardanti tutto ciò che è stato discusso ed impartito";
  const incorniciatoLines = doc.splitTextToSize(testoIncorniciato, maxWidth - 16);
  const boxHeight = incorniciatoLines.length * 4 + 8;
  
  // Verifica spazio per il box
  if (yPosition + boxHeight > doc.internal.pageSize.height - 40) {
    doc.addPage();
    yPosition = 20;
  }
  
  // Disegna il box con bordo nero sottile
  doc.setDrawColor(0, 0, 0);
  doc.setLineWidth(0.5);
  doc.rect(margin, yPosition, maxWidth, boxHeight);
  
  // Testo all'interno del box centrato
  doc.setFont('helvetica', 'bold');
  incorniciatoLines.forEach((line: string, index: number) => {
    doc.text(line, pageWidth / 2, yPosition + 6 + (index * 4), { align: 'center' });
  });
  yPosition += boxHeight + 15;

  // Verifica spazio per sezione firme
  if (yPosition > doc.internal.pageSize.height - 80) {
    doc.addPage();
    yPosition = 20;
  }

  // Sezione firme
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  
  const dataFormattedConclusion = new Date(sopralluogoData.data).toLocaleDateString('it-IT');
  
  doc.text(`Luogo: ${sopralluogoData.cantiere.indirizzo.toUpperCase()}`, margin, yPosition);
  yPosition += 6;
  doc.text(`Data: ${dataFormattedConclusion}`, margin, yPosition);
  yPosition += 10;
  
  doc.setFont('helvetica', 'bold');
  doc.text('Letto, firmato e sottoscritto:', margin, yPosition);
  yPosition += 8;
  
  // Elenco persone presenti per le firme
  doc.setFont('helvetica', 'normal');
  
  // Aggiungi sempre il tecnico incaricato
  doc.text(`${sopralluogoData.tecnico.nome.toUpperCase()} ${sopralluogoData.tecnico.cognome.toUpperCase()}`, margin, yPosition);
  
  // Se ci sono firme, mostra la firma digitale, altrimenti mostra lo spazio per firmare
  console.log('Available signatures:', sopralluogoData.firme ? Object.keys(sopralluogoData.firme) : 'None');
  
  let tecnicoSignatureFound = false;
  let tecnicoSignatureData = null;
  
  if (sopralluogoData.firme) {
    // Try different possible keys for tecnico signature
    const tecnicoKeys = [
      `tecnico_${sopralluogoData.tecnico.id}`,
      sopralluogoData.tecnico.id.toString(),
      `${sopralluogoData.tecnico.nome} ${sopralluogoData.tecnico.cognome}`,
      `${sopralluogoData.tecnico.nome}  ${sopralluogoData.tecnico.cognome}`, // with double space
      sopralluogoData.tecnico.nome,
      `${sopralluogoData.tecnico.nome} ${sopralluogoData.tecnico.cognome}`.toUpperCase(),
      `${sopralluogoData.tecnico.nome}  ${sopralluogoData.tecnico.cognome}`.toUpperCase()
    ];
    
    for (const key of tecnicoKeys) {
      if (sopralluogoData.firme[key]) {
        tecnicoSignatureData = sopralluogoData.firme[key];
        tecnicoSignatureFound = true;
        console.log(`Found tecnico signature with key: ${key}`);
        break;
      }
    }
  }
  
  if (tecnicoSignatureFound && tecnicoSignatureData) {
    try {
      console.log('Adding tecnico signature to PDF');
      doc.addImage(tecnicoSignatureData, 'PNG', margin + 80, yPosition - 8, 40, 12);
    } catch (error) {
      console.error('Error adding signature image:', error);
      doc.text('Firma: ____________________________', margin + 80, yPosition);
    }
  } else {
    console.log('No signature found for tecnico, adding line');
    doc.text('Firma: ____________________________', margin + 80, yPosition);
  }
  yPosition += 15; // Aumentato da 8 a 15 per maggiore spazio
  
  // Aggiungi le persone presenti se esistono
  if (sopralluogoData.personePresenti && sopralluogoData.personePresenti.length > 0) {
    sopralluogoData.personePresenti.forEach(persona => {
      if (yPosition > doc.internal.pageSize.height - 30) {
        doc.addPage();
        yPosition = 20;
      }
      
      doc.text(`${persona.nome.toUpperCase()}`, margin, yPosition);
      
      // Se ci sono firme, mostra la firma digitale
      // Try multiple possible signature keys: persona.id, persona.nome, and full name variations
      let signatureFound = false;
      let signatureData = null;
      
      if (sopralluogoData.firme) {
        // Try persona.id first
        if (sopralluogoData.firme[persona.id]) {
          signatureData = sopralluogoData.firme[persona.id];
          signatureFound = true;
        }
        // Try persona.nome 
        else if (sopralluogoData.firme[persona.nome]) {
          signatureData = sopralluogoData.firme[persona.nome];
          signatureFound = true;
        }
        // Try full name variations
        else {
          const fullName = `${persona.nome} ${persona.qualifica || ''}`.trim();
          const fullNameVariations = [
            persona.nome,
            fullName,
            persona.nome.toUpperCase(),
            fullName.toUpperCase()
          ];
          
          for (const variation of fullNameVariations) {
            if (sopralluogoData.firme[variation]) {
              signatureData = sopralluogoData.firme[variation];
              signatureFound = true;
              break;
            }
          }
        }
      }
      
      if (signatureFound && signatureData) {
        try {
          console.log(`Adding signature for persona: ${persona.nome}`);
          doc.addImage(signatureData, 'PNG', margin + 80, yPosition - 8, 40, 12);
        } catch (error) {
          console.error('Error adding signature image:', error);
          doc.text('Firma: ____________________________', margin + 80, yPosition);
        }
      } else {
        console.log(`No signature found for persona: ${persona.nome}, available keys:`, Object.keys(sopralluogoData.firme || {}));
        doc.text('Firma: ____________________________', margin + 80, yPosition);
      }
      yPosition += 15; // Aumentato da 8 a 15 per maggiore spazio
    });
  }

  // Aggiungi piè di pagina a tutte le pagine
  const totalPages = doc.getNumberOfPages();
  const dataFormattedFooter = new Date(sopralluogoData.data).toLocaleDateString('it-IT');
  
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    
    // Note a piè di pagina (sinistra)
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 100, 100);
    
    const footerText = `${sopralluogoData.cantiere.codiceCommessa.toUpperCase()} - ${sopralluogoData.cantiere.indirizzo.toUpperCase()} - ${sopralluogoData.cantiere.committente.toUpperCase()} - ${dataFormattedFooter}`;
    doc.text(footerText, margin, doc.internal.pageSize.height - 10);
    
    // Numerazione pagine (destra)
    const pageText = `PAGINA ${i} DI ${totalPages}`;
    const pageTextWidth = doc.getTextWidth(pageText);
    doc.text(pageText, doc.internal.pageSize.width - margin - pageTextWidth, doc.internal.pageSize.height - 10);
  }

  // Salva il PDF nel database
  const fileName = `verbale_${sopralluogoData.numeroSopralluogo.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
  
  const pdfOutput = doc.output('arraybuffer');
  const pdfBuffer = Buffer.from(pdfOutput);
  
  // Salva il PDF nel database per sopralluogo
  try {
    await databaseStorage.storePDFSopralluogo(sopralluogoData.id, pdfBuffer, fileName);
    console.log(`📄 PDF saved to database for sopralluogo: ${fileName} (${pdfBuffer.length} bytes)`);
  } catch (error) {
    console.error('Error saving PDF to database for sopralluogo:', error);
  }
  
  return `/api/files/verbali/${fileName}`;
}

// Definisce il tipo per i verbali di direzione lavori con dati completi
type VerbaleWithData = Omit<VerbaleDirezioneLavori, 'personePresenti'> & {
  direzioneLavori: DirezioneLavori;
  tecnico: DirettoreLavori;
  personePresenti?: PersonaPresente[];
  firme?: Record<string, string>;
};

export async function generatePDFVerbaleDirezioneLavori(verbaleData: VerbaleWithData): Promise<{success: boolean, buffer?: Buffer, error?: string, filename?: string, path?: string}> {
  console.log('Starting PDF generation for verbale:', verbaleData.numeroProgressivo);
  
  // Inizializza PDF (compressione rimossa temporaneamente per compatibilità)
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const margin = 15;
  const maxWidth = pageWidth - (margin * 2);
  let yPosition = 20;

  // Funzione per aggiungere timestamp ruotato su ogni pagina
  const addTimestampToPage = () => {
    // Crea data corrente con fuso orario italiano (UTC+1/+2)
    const now = new Date();
    // Aggiungi 2 ore per compensare il fuso orario del server
    const italianTime = new Date(now.getTime() + (2 * 60 * 60 * 1000));
    const timestamp = `Verbale generato in data ${italianTime.toLocaleDateString('it-IT')} alle ore ${italianTime.toLocaleTimeString('it-IT')}`;
    
    // Salva stato corrente
    doc.saveGraphicsState();
    
    // Imposta font per timestamp
    doc.setFont('helvetica', 'italic');
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128); // Grigio
    
    // Posizione sul bordo destro, ruotato di 90°
    const pageHeight = doc.internal.pageSize.height;
    const xPos = 205; // Più vicino al bordo destro
    const yPos = pageHeight - 20; // Molto in basso, a 20mm dal fondo (sopra i numeri di pagina)
    
    // Ruota di 90° e aggiungi testo
    doc.text(timestamp, xPos, yPos, { angle: 90 });
    
    // Ripristina stato
    doc.restoreGraphicsState();
  };

  // Override della funzione addPage per aggiungere automaticamente il timestamp
  const originalAddPage = doc.addPage.bind(doc);
  doc.addPage = function() {
    const result = originalAddPage();
    addTimestampToPage();
    return result;
  };

  // Aggiungi timestamp alla prima pagina
  addTimestampToPage();

  // Header con loghi
  const headerHeight = 30;
  const logoYPosition = yPosition; // Posizione Y fissa per tutti i loghi per allinearli orizzontalmente
  let hasLogos = false;

  // Funzione helper per calcolare dimensioni mantenendo proporzioni da base64
  function calculateLogoBase64Dimensions(logoBase64: string | null, maxWidth: number, maxHeight: number) {
    try {
      if (!logoBase64) return { width: maxWidth, height: maxHeight };
      
      // Estrai il buffer dal base64
      const base64Data = logoBase64.replace(/^data:image\/[^;]+;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');
      
      const dimensions = sizeOf(imageBuffer);
      if (!dimensions || !dimensions.width || !dimensions.height) {
        console.warn('Could not determine image dimensions from base64, using defaults');
        return { width: maxWidth, height: maxHeight };
      }
      
      // Calcola il rapporto di scala per rispettare le proporzioni
      const scaleX = maxWidth / dimensions.width;
      const scaleY = maxHeight / dimensions.height;
      const scale = Math.min(scaleX, scaleY); // Usa la scala più piccola per mantenere le proporzioni
      
      console.log(`📐 Logo dimensions: ${dimensions.width}x${dimensions.height} → ${Math.round(dimensions.width * scale)}x${Math.round(dimensions.height * scale)} (scale: ${scale.toFixed(3)})`);
      
      return {
        width: dimensions.width * scale,
        height: dimensions.height * scale
      };
    } catch (error) {
      console.error('Error calculating logo dimensions from base64:', error);
      return { width: maxWidth, height: maxHeight };
    }
  }

  // Logo Sinistra - usa il nuovo logo Orbyta come fallback
  let logoSinistraPath = verbaleData.direzioneLavori.logoSinistra || 'logos/orbyta_logo.jpg';
  const logoSinistraBase64 = await getLogoBase64(logoSinistraPath);
  if (logoSinistraBase64) {
    try {
      const maxLogoWidth = 40;
      const maxLogoHeight = 20;
      const dimensions = calculateLogoBase64Dimensions(logoSinistraBase64, maxLogoWidth, maxLogoHeight);
      
      // Determina il tipo di immagine
      const imageType = logoSinistraBase64.includes('data:image/png') ? 'PNG' : 'JPEG';
      
      // Calcola la posizione Y per centrare verticalmente nell'area del logo
      const centeredY = logoYPosition + (maxLogoHeight - dimensions.height) / 2;
      
      // Posiziona a sinistra, centrato verticalmente
      doc.addImage(logoSinistraBase64, imageType, margin, centeredY, dimensions.width, dimensions.height, undefined, 'FAST');
      hasLogos = true;
    } catch (error) {
      console.error('Error adding left logo:', error);
    }
  }

  // Logo Centrale
  if (verbaleData.direzioneLavori.logoCentrale) {
    const logoBase64 = await getLogoBase64(verbaleData.direzioneLavori.logoCentrale);
    if (logoBase64) {
      try {
        const maxLogoWidth = 40;
        const maxLogoHeight = 20;
        const dimensions = calculateLogoBase64Dimensions(logoBase64, maxLogoWidth, maxLogoHeight);
        
        const imageType = logoBase64.includes('data:image/png') ? 'PNG' : 'JPEG';
        
        // Calcola la posizione per centrare orizzontalmente e verticalmente
        const centerX = (pageWidth - dimensions.width) / 2;
        const centeredY = logoYPosition + (maxLogoHeight - dimensions.height) / 2;
        
        doc.addImage(logoBase64, imageType, centerX, centeredY, dimensions.width, dimensions.height, undefined, 'FAST');
        hasLogos = true;
      } catch (error) {
        console.error('Error adding center logo:', error);
      }
    }
  }

  // Logo Destro
  if (verbaleData.direzioneLavori.logoDestro) {
    const logoBase64 = await getLogoBase64(verbaleData.direzioneLavori.logoDestro);
    if (logoBase64) {
      try {
        const maxLogoWidth = 40;
        const maxLogoHeight = 20;
        const dimensions = calculateLogoBase64Dimensions(logoBase64, maxLogoWidth, maxLogoHeight);
        
        const imageType = logoBase64.includes('data:image/png') ? 'PNG' : 'JPEG';
        
        // Calcola la posizione per allineare a destra e centrare verticalmente
        const rightX = pageWidth - margin - dimensions.width;
        const centeredY = logoYPosition + (maxLogoHeight - dimensions.height) / 2;
        
        doc.addImage(logoBase64, imageType, rightX, centeredY, dimensions.width, dimensions.height, undefined, 'FAST');
        hasLogos = true;
      } catch (error) {
        console.error('Error adding right logo:', error);
      }
    }
  }

  // Se ci sono loghi, sposta il titolo verso il basso
  if (hasLogos) {
    yPosition += headerHeight + 10;
  }

  // Titolo principale
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.text('VERBALE DI DIREZIONE LAVORI', pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 15;

  // Informazioni generali - Tabella (stile sopralluoghi)
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('INFORMAZIONI GENERALI', margin, yPosition);
  yPosition += 3;

  // Prepara i dati per la tabella informazioni generali
  const dataObj = new Date(verbaleData.data);
  const dataFormattedInfo = dataObj.toLocaleDateString('it-IT');
  
  // Usa l'ora corrente per la generazione del PDF con fuso orario italiano
  const now = new Date();
  const oraFormatted = now.toLocaleTimeString('it-IT', { 
    hour: '2-digit', 
    minute: '2-digit',
    timeZone: 'Europe/Rome'
  });
  
  // Estrae il numero progressivo dal formato BC074-VDL-020-20250617
  const progressivoMatch = verbaleData.numeroProgressivo.match(/-VDL-(\d+)-/);
  const numeroProgressivoDisplay = progressivoMatch ? progressivoMatch[1] : verbaleData.id.toString();
  
  const infoGeneraliData = [
    ['Identificativo Verbale', verbaleData.numeroProgressivo?.toUpperCase() || 'N/D'],
    ['Numero Progressivo', numeroProgressivoDisplay],
    ['Data', dataFormattedInfo],
    ['Ora', oraFormatted],
    ['Codice Commessa', verbaleData.direzioneLavori.codiceCommessa.toUpperCase()],
    ['Indirizzo', verbaleData.direzioneLavori.indirizzo.toUpperCase()],
    ['Committente', verbaleData.direzioneLavori.committente.toUpperCase()],
    ['Oggetto', verbaleData.direzioneLavori.oggetto.toUpperCase()],
    ...(verbaleData.direzioneLavori.cig ? [['CIG', verbaleData.direzioneLavori.cig.toUpperCase()]] : []),
    ['Direttore Lavori', `${verbaleData.tecnico.nome} ${verbaleData.tecnico.cognome}`.toUpperCase()],
    ...(verbaleData.tecnico.qualifica ? [['Qualifica', verbaleData.tecnico.qualifica.toUpperCase()]] : [])
  ];

  // Tabella informazioni generali con altezza dinamica
  const infoTableWidth = maxWidth;
  const labelWidth = infoTableWidth * 0.3;
  const valueWidth = infoTableWidth * 0.7;
  const baseRowHeight = 8;
  const lineHeight = 4;

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.setDrawColor(200, 200, 200);
  
  infoGeneraliData.forEach(([label, value]) => {
    // Calcola le linee del testo del valore per determinare l'altezza della riga
    doc.setFont('helvetica', 'normal');
    const valueLines = doc.splitTextToSize(value, valueWidth - 4);
    const numLines = valueLines.length;
    
    // Calcola l'altezza dinamica della riga (minimo baseRowHeight)
    const dynamicRowHeight = Math.max(baseRowHeight, numLines * lineHeight + 4);
    
    // Disegna le celle con altezza dinamica
    doc.rect(margin, yPosition, labelWidth, dynamicRowHeight);
    doc.rect(margin + labelWidth, yPosition, valueWidth, dynamicRowHeight);
    
    // Sfondo grigio per le etichette
    doc.setFillColor(240, 240, 240);
    doc.rect(margin, yPosition, labelWidth, dynamicRowHeight, 'F');
    
    // Testo etichetta (grassetto) - centrato verticalmente
    doc.setFont('helvetica', 'bold');
    const labelYPosition = yPosition + (dynamicRowHeight / 2) + 1;
    doc.text(label, margin + 2, labelYPosition);
    
    // Testo valore (normale) - posizionato correttamente per testo multilinea
    doc.setFont('helvetica', 'normal');
    valueLines.forEach((line: string, index: number) => {
      const lineYPosition = yPosition + 4 + (index * lineHeight);
      doc.text(line, margin + labelWidth + 2, lineYPosition);
    });
    
    yPosition += dynamicRowHeight;
  });

  yPosition += 10;

  // Persone presenti - posizionate subito dopo DATI DEL VERBALE
  if (verbaleData.personePresenti && verbaleData.personePresenti.length > 0) {
    // Filtra le persone presenti escludendo il direttore lavori compilatore
    const tecnicoNomeCompleto = `${verbaleData.tecnico.nome.trim()} ${verbaleData.tecnico.cognome.trim()}`;
    const tecnicoNomeCompletoNormalized = tecnicoNomeCompleto.replace(/\s+/g, ' ').toLowerCase();
    
    const personeFiltered = verbaleData.personePresenti.filter(persona => {
      const personaNomeNormalized = persona.nome.trim().replace(/\s+/g, ' ').toLowerCase();
      return personaNomeNormalized !== tecnicoNomeCompletoNormalized;
    });

    if (personeFiltered.length > 0) {
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text('PERSONE PRESENTI', margin, yPosition);
      yPosition += 8;

      // Creare tabella per persone presenti
      const tableStartY = yPosition;
      const tableWidth = 180;
      const colWidths = [50, 60, 35, 35]; // nominativo, qualifica, telefono, email
      const rowHeight = 8;
      
      // Header tabella
      doc.setFontSize(9);
      doc.setFont('helvetica', 'bold');
      doc.setFillColor(220, 220, 220);
      doc.rect(margin, yPosition, tableWidth, rowHeight, 'F');
      doc.rect(margin, yPosition, tableWidth, rowHeight, 'S');
      
      // Linee verticali header
      let currentX = margin;
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[0];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[1];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[2];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[3];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      
      // Testi header
      doc.text('NOMINATIVO', margin + 2, yPosition + 5);
      doc.text('QUALIFICA', margin + colWidths[0] + 2, yPosition + 5);
      doc.text('TELEFONO', margin + colWidths[0] + colWidths[1] + 2, yPosition + 5);
      doc.text('EMAIL', margin + colWidths[0] + colWidths[1] + colWidths[2] + 2, yPosition + 5);
      
      yPosition += rowHeight;
      
      // Righe dati
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      
      personeFiltered.forEach(persona => {
        const startRowY = yPosition;
        
        // Bordo riga
        doc.rect(margin, yPosition, tableWidth, rowHeight, 'S');
        
        // Linee verticali
        currentX = margin;
        doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
        currentX += colWidths[0];
        doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
        currentX += colWidths[1];
        doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
        currentX += colWidths[2];
        doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
        currentX += colWidths[3];
        doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
        
        // Testi con wrapping
        const nome = persona.nome.toUpperCase();
        const qualifica = persona.qualifica.toUpperCase();
        const telefono = persona.telefono?.trim() || '';
        const email = persona.email?.trim()?.toLowerCase() || '';
        
        // Nominativo - centrato verticalmente
        const nomeLines = doc.splitTextToSize(nome, colWidths[0] - 4);
        const nomeYOffset = (rowHeight - (nomeLines.length * 3)) / 2;
        doc.text(nomeLines, margin + 2, yPosition + 3 + nomeYOffset);
        
        // Qualifica - centrato verticalmente
        const qualificaLines = doc.splitTextToSize(qualifica, colWidths[1] - 4);
        const qualificaYOffset = (rowHeight - (qualificaLines.length * 3)) / 2;
        doc.text(qualificaLines, margin + colWidths[0] + 2, yPosition + 3 + qualificaYOffset);
        
        // Telefono - centrato verticalmente
        const telefonoLines = doc.splitTextToSize(telefono, colWidths[2] - 4);
        const telefonoYOffset = (rowHeight - (telefonoLines.length * 3)) / 2;
        doc.text(telefonoLines, margin + colWidths[0] + colWidths[1] + 2, yPosition + 3 + telefonoYOffset);
        
        // Email - centrato verticalmente
        const emailLines = doc.splitTextToSize(email, colWidths[3] - 4);
        const emailYOffset = (rowHeight - (emailLines.length * 3)) / 2;
        doc.text(emailLines, margin + colWidths[0] + colWidths[1] + colWidths[2] + 2, yPosition + 3 + emailYOffset);
        
        yPosition += rowHeight;
      });
      yPosition += 10;
    }
  }

  // Contenuto del verbale
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('CONTENUTO DEL VERBALE', margin, yPosition);
  yPosition += 8;

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');

  // Lavorazioni in corso
  if (verbaleData.lavorazioniInCorso) {
    doc.setFont('helvetica', 'bold');
    doc.text('Lavorazioni in corso:', margin, yPosition);
    yPosition += 6;

    doc.setFont('helvetica', 'normal');
    yPosition = addTextWithPageBreak(doc, verbaleData.lavorazioniInCorso.toUpperCase(), margin, yPosition, maxWidth);
    yPosition += 8;
  }

  // Criticità
  if (verbaleData.criticita) {
    doc.setFont('helvetica', 'bold');
    doc.text('Criticità riscontrate:', margin, yPosition);
    yPosition += 6;

    doc.setFont('helvetica', 'normal');
    yPosition = addTextWithPageBreak(doc, verbaleData.criticita.toUpperCase(), margin, yPosition, maxWidth);
    yPosition += 8;
  }



  // Helper function to parse optional section items
  const parseOptionalSectionItems = (jsonString: string) => {
    if (!jsonString) return [];
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Error parsing optional section items:', error);
      return [];
    }
  };

  // Helper function to render an optional section item with proper border
  const renderOptionalItem = async (item: any, itemIndex: number, doc: any, startY: number, margin: number, maxWidth: number) => {
    const pageWidth = 210; // A4 width
    const usableWidth = pageWidth - (2 * margin);
    const itemPadding = 5; // Internal padding for border
    
    let currentY = startY + 5; // Start inside the border with padding
    const borderStartY = startY;
    
    // Item number and text on same line
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(10);
    doc.text(`${itemIndex + 1}.`, margin + itemPadding, currentY);
    
    if (item.text && item.text.trim()) {
      doc.setFont('helvetica', 'normal');
      const textLines = doc.splitTextToSize(item.text.toUpperCase(), usableWidth - 30);
      doc.text(textLines[0], margin + itemPadding + 10, currentY);
      
      // Additional lines if text wraps
      if (textLines.length > 1) {
        for (let i = 1; i < textLines.length; i++) {
          currentY += 5;
          doc.text(textLines[i], margin + itemPadding + 10, currentY);
        }
      }
      currentY += 8;
    } else {
      currentY += 8;
    }
    
    // Calcola altezza del testo e disegna cornice nera solo attorno al testo
    const itemTextHeight = currentY - borderStartY;
    doc.setDrawColor(0, 0, 0); // Nero
    doc.setLineWidth(0.5);
    doc.rect(margin, borderStartY, usableWidth, itemTextHeight);
    
    // Spaziatura dopo la cornice del testo
    currentY += 5;
    
    // Media content - foto dopo il testo con cornice, centrate
    let mediaHeight = 0;
    const sectionPadding = 5; // Padding interno alla cornice
    const availableWidth = usableWidth - (sectionPadding * 2); // Usa tutta la larghezza del foglio
    
    // Salva la posizione corrente per inserire le foto dopo il testo
    const textEndY = currentY;
    
    if (item.photos && item.photos.length > 0) {
      console.log(`Processing ${item.photos.length} photos with 2x1 grid layout (2 per page) after text`);

      // Layout griglia 2x1: 2 foto affiancate per pagina
      const photosPerPage = 2;
      const photosPerRow = 2;
      const photoMargin = 10; // spazio tra foto affiancate
      const availablePhotoWidth = (availableWidth - photoMargin) / photosPerRow;
      const maxPhotoWidth = availablePhotoWidth * 0.9; // 90% dello spazio disponibile per foto
      const maxPhotoHeight = 100; // altezza ridotta da 150 a 100

      let currentPhotoY = textEndY + 10; // inizia 10pt dopo il testo
      let photosOnCurrentPage = 0;
      
      for (let photoIndex = 0; photoIndex < item.photos.length; photoIndex++) {
        const photoFilename = item.photos[photoIndex];
        
        // Nuova pagina se abbiamo già 2 foto o se non c'è spazio per una nuova riga
        if (photosOnCurrentPage >= photosPerPage ||
            currentPhotoY + maxPhotoHeight + 30 > doc.internal.pageSize.height - 30) {
          doc.addPage();
          currentPhotoY = 30; // margine top della nuova pagina
          photosOnCurrentPage = 0;
        }

        // Calcola posizione nella griglia 2x1 (dopo eventuale page break)
        const col = photosOnCurrentPage % photosPerRow; // 0 o 1 (sinistra o destra)
        
        try {
          const photoBase64 = await getImageBase64Corrected(photoFilename);
          if (!photoBase64) {
            console.error(`Failed to load photo: ${photoFilename}`);
            continue;
          }
          
          // Calcola dimensioni mantenendo proporzioni
          let photoWidth = maxPhotoWidth;
          let photoHeight = maxPhotoHeight;
          
          try {
            // Carica la foto con il PhotoManager (base64 o filesystem)
            const photoData = await photoManager.loadPhotoSmart(photoFilename);
            if (!photoData) {
              throw new Error('Foto non disponibile - richiede ri-caricamento');
            }
            
            // Converti base64 in buffer per jsPDF
            const base64Data = photoData.startsWith('data:image/') 
              ? photoData.split(',')[1] 
              : photoData;
            const imageBuffer = Buffer.from(base64Data, 'base64');
            const dimensions = sizeOf(imageBuffer);
            
            if (dimensions.width && dimensions.height) {
              const aspectRatio = dimensions.width / dimensions.height;
              
              if (aspectRatio > 1) {
                // Foto orizzontale - adatta alla larghezza disponibile
                photoWidth = Math.min(maxPhotoWidth, maxPhotoHeight * aspectRatio);
                photoHeight = photoWidth / aspectRatio;
              } else {
                // Foto verticale - adatta all'altezza disponibile
                photoHeight = Math.min(maxPhotoHeight, maxPhotoWidth / aspectRatio);
                photoWidth = photoHeight * aspectRatio;
              }
              
              // Verifica che rimanga nei limiti
              if (photoWidth > maxPhotoWidth) {
                photoWidth = maxPhotoWidth;
                photoHeight = photoWidth / aspectRatio;
              }
              if (photoHeight > maxPhotoHeight) {
                photoHeight = maxPhotoHeight;
                photoWidth = photoHeight * aspectRatio;
              }
            }
          } catch (sizeError) {
            console.log('Using default dimensions for photo:', photoFilename);
            photoWidth = maxPhotoWidth * 0.8;
            photoHeight = maxPhotoHeight * 0.8;
          }
          
          // Posiziona foto nella griglia 2x1
          const photoX = margin + sectionPadding + (col * (availablePhotoWidth));
          const photoY = currentPhotoY;

          console.log(`Photo ${photoIndex + 1}: position=${col === 0 ? 'left' : 'right'}, size=${Math.round(photoWidth/28.35*10)/10}x${Math.round(photoHeight/28.35*10)/10}cm`);

          doc.addImage(photoBase64, 'JPEG', photoX, photoY, photoWidth, photoHeight);

          // Caption centrato sotto la foto
          doc.setFont('helvetica', 'italic');
          doc.setFontSize(10);
          const captionText = `Foto ${photoIndex + 1}`;
          const textWidth = doc.getTextWidth(captionText);
          doc.text(captionText, photoX + (photoWidth - textWidth) / 2, photoY + photoHeight + 15);

          // Aggiorna posizione: se è la seconda foto della riga (col=1), vai alla riga successiva
          if (col === 1) {
            currentPhotoY += photoHeight + 35; // spazio per caption e gap tra righe
          }
          photosOnCurrentPage++;
          
          // Aggiorna mediaHeight per includere le foto nella cornice
          if (photoIndex === 0) {
            // Solo per la prima foto, per calcolare l'altezza della sezione
            mediaHeight = Math.max(mediaHeight, currentPhotoY - textEndY);
          }
          
        } catch (error) {
          console.error(`Error adding photo ${photoIndex + 1}:`, error);
        }
      }
      
      // Se ci sono foto, calcola l'altezza totale della sezione incluse le foto
      if (item.photos.length > 0) {
        mediaHeight = currentPhotoY - textEndY + 10;
      }
    }
    
    // Disegno dopo le foto
    if (item.drawing && item.drawing.trim()) {
      try {
        // Posiziona il disegno dopo l'ultima foto
        let drawingY = textEndY + mediaHeight + 10;
        
        // Se siamo troppo in basso, vai a una nuova pagina
        if (drawingY + 60 > doc.internal.pageSize.height - 30) {
          doc.addPage();
          drawingY = 30;
        }
        
        const drawingWidth = availableWidth * 0.8; // 80% della larghezza disponibile
        const drawingHeight = 80; // altezza fissa per il disegno
        const drawingX = margin + (usableWidth - drawingWidth) / 2; // centrato
        
        doc.addImage(item.drawing, 'PNG', drawingX, drawingY, drawingWidth, drawingHeight);
        doc.setFont('helvetica', 'italic');
        doc.setFontSize(9);
        doc.text('Disegno', drawingX + (drawingWidth / 2) - 10, drawingY + drawingHeight + 8);
        
        // Aggiorna mediaHeight per includere il disegno
        mediaHeight = Math.max(mediaHeight, drawingY - textEndY + drawingHeight + 15);
      } catch (error) {
        console.error(`Error adding drawing:`, error);
      }
    }
    
    // Calcola l'altezza totale della sezione includendo foto
    const totalContentHeight = Math.max(mediaHeight + 10, 10);
    
    // Non disegniamo più una cornice generale - le cornici sono sui singoli punti di testo
    
    currentY = textEndY + totalContentHeight + 5;
    
    return currentY + 5; // Return next Y position
  };

  // Parse optional sections
  const controlliItems = parseOptionalSectionItems(verbaleData.controlliDimensionali ?? '');
  const nonConformitaItems = parseOptionalSectionItems(verbaleData.nonConformita ?? '');
  const indicazioniItems = parseOptionalSectionItems(verbaleData.indicazioniOperative ?? '');

  const hasOptionalSections = controlliItems.length > 0 || nonConformitaItems.length > 0 || indicazioniItems.length > 0;

  if (hasOptionalSections) {
    // Nuova pagina per le sezioni tecniche aggiuntive
    doc.addPage();
    yPosition = 20;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('SEZIONI TECNICHE AGGIUNTIVE', margin, yPosition);
    yPosition += 10;

    doc.setFontSize(10);

    // Controlli dimensionali e geometrici
    if (controlliItems.length > 0) {
      doc.setFont('helvetica', 'bold');
      doc.text('Controlli dimensionali e geometrici:', margin, yPosition);
      yPosition += 8;
      
      for (let itemIndex = 0; itemIndex < controlliItems.length; itemIndex++) {
        const item = controlliItems[itemIndex];

        // Stima altezza necessaria per questo item
        const estimatedTextHeight = Math.ceil((item.text?.length || 0) / 80) * 6; // ~80 char per riga, 6pt per riga
        const estimatedPhotoHeight = (item.photos?.length || 0) * 55; // 45px foto + 10px margin
        const estimatedDrawingHeight = item.drawing ? 60 : 0;
        const totalEstimatedHeight = estimatedTextHeight + estimatedPhotoHeight + estimatedDrawingHeight + 30; // +30 per margini

        console.log(`📏 Item ${itemIndex + 1} estimated height: ${totalEstimatedHeight}px (text: ${estimatedTextHeight}, photos: ${estimatedPhotoHeight}, drawing: ${estimatedDrawingHeight})`);

        // Nuova pagina solo se non c'è spazio sufficiente
        if (yPosition + totalEstimatedHeight > doc.internal.pageSize.height - 40) {
          doc.addPage();
          yPosition = 20;

          // Se è il primo item della nuova pagina e non è il primo assoluto, aggiungi titolo sezione
          if (itemIndex > 0) {
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(10);
            doc.text('Controlli dimensionali e geometrici (continua):', margin, yPosition);
            yPosition += 10;
          }
        }

        yPosition = await renderOptionalItem(item, itemIndex, doc, yPosition, margin, maxWidth);
      }
      
      yPosition += 6;
    }

    // Non conformità riscontrate
    if (nonConformitaItems.length > 0) {
      // Nuova pagina per la sezione Non conformità
      doc.addPage();
      yPosition = 20;
      
      doc.setFont('helvetica', 'bold');
      doc.text('Eventuali non conformità riscontrate:', margin, yPosition);
      yPosition += 8;
      
      for (let itemIndex = 0; itemIndex < nonConformitaItems.length; itemIndex++) {
        const item = nonConformitaItems[itemIndex];
        // Nuova pagina per ogni punto aggiuntivo (dal secondo in poi)
        if (itemIndex > 0) {
          doc.addPage();
          yPosition = 20;
        } else if (yPosition > 200) {
          // Solo per il primo punto, controlla se serve una nuova pagina per spazio
          doc.addPage();
          yPosition = 20;
        }
        
        yPosition = await renderOptionalItem(item, itemIndex, doc, yPosition, margin, maxWidth);
      }
      
      yPosition += 6;
    }

    // Indicazioni operative fornite all'impresa
    if (indicazioniItems.length > 0) {
      // Nuova pagina per la sezione Indicazioni operative
      doc.addPage();
      yPosition = 20;
      
      doc.setFont('helvetica', 'bold');
      doc.text('Indicazioni operative fornite all\'impresa:', margin, yPosition);
      yPosition += 8;
      
      for (let itemIndex = 0; itemIndex < indicazioniItems.length; itemIndex++) {
        const item = indicazioniItems[itemIndex];
        // Nuova pagina per ogni punto aggiuntivo (dal secondo in poi)
        if (itemIndex > 0) {
          doc.addPage();
          yPosition = 20;
        } else if (yPosition > 200) {
          // Solo per il primo punto, controlla se serve una nuova pagina per spazio
          doc.addPage();
          yPosition = 20;
        }
        
        yPosition = await renderOptionalItem(item, itemIndex, doc, yPosition, margin, maxWidth);
      }
      
      yPosition += 8;
    }
  }

  // Note aggiuntive
  if (verbaleData.note) {
    if (yPosition > 240) {
      doc.addPage();
      yPosition = 20;
    }
    
    doc.setFont('helvetica', 'bold');
    doc.text('Note aggiuntive:', margin, yPosition);
    yPosition += 6;
    
    doc.setFont('helvetica', 'normal');
    const noteLines = doc.splitTextToSize(verbaleData.note.toUpperCase(), maxWidth);
    doc.text(noteLines, margin, yPosition);
    yPosition += noteLines.length * 6 + 8;
  }

  // ASPETTI SULLA SICUREZZA (ex Osservazioni)
  if (verbaleData.osservazioni) {
    // Nuova pagina per ASPETTI SULLA SICUREZZA
    doc.addPage();
    yPosition = 20;
    
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('ASPETTI SULLA SICUREZZA', margin, yPosition);
    yPosition += 8;
    
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    yPosition = addTextWithPageBreak(doc, verbaleData.osservazioni.toUpperCase(), margin, yPosition, maxWidth);
    yPosition += 10;
  }

  // Verifica se serve una nuova pagina prima delle firme
  if (yPosition > 220) {
    doc.addPage();
    yPosition = 20;
  }

  // Verifica se serve una nuova pagina per le firme
  if (yPosition > 150) {
    doc.addPage();
    yPosition = 20;
  }

  // Sezione firme
  yPosition += 10;
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('FIRME', margin, yPosition);
  yPosition += 10;

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text('Letto, firmato e sottoscritto:', margin, yPosition);
  yPosition += 8;
  
  // Parse signatures if they're stored as JSON string
  let firmeData: Record<string, string> = {};
  if (typeof verbaleData.firme === 'string') {
    try {
      firmeData = JSON.parse(verbaleData.firme);
    } catch (error) {
      console.error('Error parsing signatures JSON:', error);
    }
  } else if (verbaleData.firme && typeof verbaleData.firme === 'object') {
    firmeData = verbaleData.firme as Record<string, string>;
  }
  
  console.log('PDF Generation - Available signatures:', firmeData ? Object.keys(firmeData) : 'None');

  // Firme delle persone presenti (escludi il direttore compilatore)
  if (verbaleData.personePresenti && verbaleData.personePresenti.length > 0) {
    // Filtra le persone presenti escludendo il direttore lavori compilatore
    const tecnicoNomeCompleto = `${verbaleData.tecnico.nome.trim()} ${verbaleData.tecnico.cognome.trim()}`;
    const tecnicoNomeCompletoNormalized = tecnicoNomeCompleto.replace(/\s+/g, ' ').toLowerCase();
    
    const personeFiltered = verbaleData.personePresenti.filter(persona => {
      // Confronta i nomi normalizzando gli spazi e case
      const personaNomeNormalized = persona.nome.trim().replace(/\s+/g, ' ').toLowerCase();
      const shouldExclude = personaNomeNormalized === tecnicoNomeCompletoNormalized;
      return !shouldExclude;
    });

    personeFiltered.forEach(persona => {
      if (yPosition > doc.internal.pageSize.height - 30) {
        doc.addPage();
        yPosition = 20;
      }
      
      // Solo nome e cognome per le firme
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`${persona.nome.toUpperCase()}`, margin, yPosition);
      
      // Robust signature search for person
      let personSignatureFound = false;
      if (firmeData && typeof firmeData === 'object') {
        console.log(`🔍 Searching signature for persona: ${persona.nome}. Available keys:`, Object.keys(firmeData));

        // Build comprehensive list of possible keys
        const personPossibleKeys: string[] = [
          // Exact matches
          persona.nome,
          persona.nome?.toUpperCase(),
          persona.nome?.toLowerCase(),
          // ID-based keys
          persona.id?.toString(),
          `persona_${persona.id}`,
          // Fallback: search for partial matches in available keys
          ...Object.keys(firmeData).filter(key => {
            const keyLower = key.toLowerCase();
            const nomeLower = persona.nome?.toLowerCase() || '';
            return keyLower.includes(nomeLower) || nomeLower.includes(keyLower);
          })
        ].filter((key): key is string => Boolean(key)); // Remove null/undefined values and type guard

        console.log(`🔍 Trying keys for ${persona.nome}:`, personPossibleKeys);

        for (const key of personPossibleKeys) {
          if (firmeData[key]) {
            try {
              // Validate base64 signature data
              if (typeof firmeData[key] === 'string' && firmeData[key].startsWith('data:image/')) {
                console.log(`✅ PDF Generation - Adding signature for persona: ${persona.nome} with key: ${key}`);
                doc.addImage(firmeData[key], 'PNG', margin + 80, yPosition - 8, 40, 12);
                personSignatureFound = true;
                break;
              } else {
                console.warn(`⚠️ Invalid signature data format for key ${key}`);
              }
            } catch (error) {
              console.error(`❌ Error adding person signature image with key ${key}:`, error);
            }
          }
        }
      }
      
      if (!personSignatureFound) {
        console.log(`❌ No signature found for persona: ${persona.nome}. Available signatures:`, Object.keys(firmeData || {}));
        doc.text('Firma: ____________________________', margin + 80, yPosition);
      }
      yPosition += 15;
    });
  }

  // Add final signature section for the work director at the bottom
  if (yPosition > doc.internal.pageSize.height - 80) {
    doc.addPage();
    yPosition = 20;
  }
  
  yPosition += 20;
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(10);
  doc.text('IL DIRETTORE LAVORI', margin, yPosition);
  yPosition += 15;
  
  // Add work director's signature at the bottom
  doc.setFont('helvetica', 'normal');
  doc.text(`${verbaleData.tecnico.nome.toUpperCase()} ${verbaleData.tecnico.cognome.toUpperCase()}`, margin, yPosition);
  
  // Add the work director's signature if available
  let finalSignatureFound = false;
  if (firmeData && typeof firmeData === 'object') {
    const tecnicoNomeCompleto = `${verbaleData.tecnico.nome} ${verbaleData.tecnico.cognome}`;
    const possibleKeys = [
      tecnicoNomeCompleto,
      tecnicoNomeCompleto.toUpperCase(),
      `tecnico_${verbaleData.tecnico.id}`,
      tecnicoNomeCompleto.toLowerCase()
    ];
    
    for (const key of possibleKeys) {
      if (firmeData[key]) {
        try {
          doc.addImage(firmeData[key], 'PNG', margin + 80, yPosition - 8, 40, 12);
          finalSignatureFound = true;
          break;
        } catch (error) {
          console.error(`Error adding final signature with key ${key}:`, error);
        }
      }
    }
  }
  
  if (!finalSignatureFound) {
    doc.text('Firma: ____________________________', margin + 80, yPosition);
  }

  // Aggiungi piè di pagina a tutte le pagine
  const totalPages = doc.getNumberOfPages();
  const dataFormattedFooter = new Date(verbaleData.data).toLocaleDateString('it-IT');
  
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    
    // Note a piè di pagina
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 100, 100);
    
    const footerText = `${verbaleData.direzioneLavori.codiceCommessa.toUpperCase()} - ${verbaleData.direzioneLavori.indirizzo.toUpperCase()} - ${verbaleData.direzioneLavori.committente.toUpperCase()} - ${dataFormattedFooter}`;
    doc.text(footerText, margin, doc.internal.pageSize.height - 10);
    
    // Numerazione pagine
    const pageText = `PAGINA ${i} DI ${totalPages}`;
    const pageTextWidth = doc.getTextWidth(pageText);
    doc.text(pageText, doc.internal.pageSize.width - margin - pageTextWidth, doc.internal.pageSize.height - 10);
  }

  // Salva il PDF nel database
  const fileName = `verbale_direzione_${verbaleData.numeroProgressivo.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
  
  const pdfOutput = doc.output('arraybuffer');
  const pdfBuffer = Buffer.from(pdfOutput);
  
  // Salva il PDF nel database
  try {
    await databaseStorage.storePDF(verbaleData.id, pdfBuffer, fileName);
    console.log(`📄 PDF saved to database: ${fileName} (${pdfBuffer.length} bytes)`);
    
    // Restituisce il formato atteso da routes.ts
    return {
      success: true,
      buffer: pdfBuffer,
      filename: fileName,
      path: `/api/files/verbali/${fileName}`
    };
  } catch (error) {
    console.error('Error saving PDF to database:', error);
    return {
      success: false,
      error: `Errore salvataggio PDF: ${error instanceof Error ? error.message : 'Sconosciuto'}`,
      buffer: pdfBuffer, // Restituisce comunque il buffer anche se il salvataggio fallisce
      filename: fileName
    };
  }
}