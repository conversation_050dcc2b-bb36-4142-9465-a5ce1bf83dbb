// Photo Manager per Sistema Foto Database
// Gestisce foto dei verbali con base64 negli array esistenti

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import sharp from 'sharp';

interface PhotoProcessResult {
  success: boolean;
  base64?: string;
  error?: string;
  size?: number;
}

export class PhotoManager {

  /**
   * Processa e converte una foto in base64 per il database
   */
  async processPhotoToBase64(fileBuffer: Buffer, maxWidth: number = 800): Promise<PhotoProcessResult> {
    try {
      // Comprimi e ridimensiona l'immagine
      const processedBuffer = await sharp(fileBuffer)
        .resize(maxWidth, maxWidth, { 
          fit: 'inside', 
          withoutEnlargement: true 
        })
        .jpeg({ 
          quality: 50,
          progressive: true 
        })
        .toBuffer();

      const base64 = `data:image/jpeg;base64,${processedBuffer.toString('base64')}`;
      
      console.log(`📷 Photo processed: ${fileBuffer.length} → ${processedBuffer.length} bytes`);
      
      return {
        success: true,
        base64,
        size: processedBuffer.length
      };
    } catch (error) {
      console.error('Error processing photo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Carica una foto in modo intelligente: base64 o filesystem legacy
   */
  async loadPhotoSmart(photoData: string): Promise<string | null> {
    try {
      // Se è già base64, restituiscilo così com'è
      if (photoData.startsWith('data:image/')) {
        console.log(`📷 Photo already in base64 format`);
        return photoData;
      }

      // Prova a caricare dal filesystem (supporto legacy)
      const fullPath = join(process.cwd(), photoData);
      if (!existsSync(fullPath)) {
        console.error(`📷 Photo file not found: ${fullPath} - requires re-upload`);
        return null;
      }

      // Carica e converti in base64
      const fileBuffer = readFileSync(fullPath);
      const result = await this.processPhotoToBase64(fileBuffer);
      
      if (result.success && result.base64) {
        console.log(`📷 Legacy photo converted: ${fullPath}`);
        return result.base64;
      }

      console.error(`📷 Failed to convert legacy photo: ${fullPath}`);
      return null;
    } catch (error) {
      console.error('Error loading photo:', error);
      return null;
    }
  }

  /**
   * Valida che una stringa sia un base64 di immagine valido
   */
  isValidImageBase64(data: string): boolean {
    return data.startsWith('data:image/') && data.includes('base64,');
  }

  /**
   * Ottieni le dimensioni di un'immagine base64
   */
  getImageInfo(base64: string): { width: number; height: number; size: number } | null {
    try {
      if (!this.isValidImageBase64(base64)) {
        return null;
      }

      const base64Data = base64.split(',')[1];
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Usa sharp per ottenere metadati
      return sharp(buffer).metadata().then(metadata => ({
        width: metadata.width || 0,
        height: metadata.height || 0,
        size: buffer.length
      })).catch(() => null);
    } catch (error) {
      console.error('Error getting image info:', error);
      return null;
    }
  }

  /**
   * Formatta dimensioni file in formato leggibile
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Analizza un array di foto e restituisce statistiche
   */
  async analyzePhotoArray(photoArray: string[]): Promise<{
    total: number;
    base64Count: number;
    pathCount: number;
    missingCount: number;
    totalSize: number;
  }> {
    const stats = {
      total: photoArray.length,
      base64Count: 0,
      pathCount: 0,
      missingCount: 0,
      totalSize: 0
    };

    for (const photo of photoArray) {
      if (this.isValidImageBase64(photo)) {
        stats.base64Count++;
        const base64Data = photo.split(',')[1];
        stats.totalSize += Buffer.from(base64Data, 'base64').length;
      } else {
        // È un percorso file
        stats.pathCount++;
        const fullPath = join(process.cwd(), photo);
        if (!existsSync(fullPath)) {
          stats.missingCount++;
        } else {
          try {
            const fileBuffer = readFileSync(fullPath);
            stats.totalSize += fileBuffer.length;
          } catch (error) {
            stats.missingCount++;
          }
        }
      }
    }

    return stats;
  }
}

// Singleton instance
export const photoManager = new PhotoManager();