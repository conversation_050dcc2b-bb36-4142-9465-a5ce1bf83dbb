import { users, cantieri, tecnici, sopralluoghi, personePresenti, direzioneLavori, verbaliDirezioneLavori, direttoriLavori, direzioneLavoriDirettori, rubricaPersonePresenti, type User, type InsertUser, type Cantiere, type InsertCantiere, type Tecnico, type InsertTecnico, type Sopralluogo, type InsertSopralluogo, type PersonaPresente, type InsertPersonaPresente, type DirezioneLavori, type InsertDirezioneLavori, type VerbaleDirezioneLavori, type InsertVerbaleDirezioneLavori, type DirettoreLavori, type InsertDirettoreLavori, type DirezioneLavoriDirettori, type InsertDirezioneLavoriDirettori, type RubricaPersonaPresente, type InsertRubricaPersonaPresente } from "@shared/schema";
import { db } from "./db";
import { eq, or, ilike, and, sql, desc } from "drizzle-orm";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getAllCantieri(): Promise<Cantiere[]>;
  getCantiere(id: number): Promise<Cantiere | undefined>;
  createCantiere(cantiere: InsertCantiere): Promise<Cantiere>;
  updateCantiere(id: number, cantiere: Partial<InsertCantiere>): Promise<Cantiere | undefined>;
  deleteCantiere(id: number): Promise<boolean>;
  searchCantieri(searchTerm: string): Promise<Cantiere[]>;

  getAllTecnici(): Promise<Tecnico[]>;
  getTecnico(id: number): Promise<Tecnico | undefined>;
  createTecnico(tecnico: InsertTecnico): Promise<Tecnico>;
  updateTecnico(id: number, tecnico: Partial<InsertTecnico>): Promise<Tecnico | undefined>;
  deleteTecnico(id: number): Promise<boolean>;

  getSopralluoghiByCantiere(cantiereId: number): Promise<(Sopralluogo & { tecnico: Tecnico })[]>;
  getSopralluogo(id: number): Promise<Sopralluogo | undefined>;
  getSopralluogoByNumero(numeroSopralluogo: string): Promise<Sopralluogo | undefined>;
  createSopralluogo(sopralluogo: InsertSopralluogo): Promise<Sopralluogo>;
  updateSopralluogo(id: number, sopralluogo: Partial<InsertSopralluogo>): Promise<Sopralluogo | undefined>;
  deleteSopralluogo(id: number): Promise<boolean>;

  getPersonePresentiBycantiere(cantiereId: number): Promise<PersonaPresente[]>;
  createPersonaPresente(persona: InsertPersonaPresente): Promise<PersonaPresente>;
  deletePersonaPresente(id: number): Promise<boolean>;

  getAllDirettoriLavori(): Promise<DirettoreLavori[]>;
  getDirettoreLavori(id: number): Promise<DirettoreLavori | undefined>;
  createDirettoreLavori(direttore: InsertDirettoreLavori): Promise<DirettoreLavori>;
  updateDirettoreLavori(id: number, direttore: Partial<InsertDirettoreLavori>): Promise<DirettoreLavori | undefined>;
  deleteDirettoreLavori(id: number): Promise<boolean>;

  getAllDirezioneLavori(): Promise<DirezioneLavori[]>;
  getDirezioneLavori(id: number): Promise<DirezioneLavori | undefined>;
  createDirezioneLavori(lavoro: InsertDirezioneLavori, direttoriIds: number[]): Promise<DirezioneLavori>;
  updateDirezioneLavori(id: number, lavoro: Partial<InsertDirezioneLavori>): Promise<DirezioneLavori | undefined>;
  deleteDirezioneLavori(id: number): Promise<boolean>;
  searchDirezioneLavori(searchTerm: string): Promise<DirezioneLavori[]>;
  
  getDirettoriByDirezioneLavori(direzioneLavoriId: number): Promise<DirettoreLavori[]>;
  addDirettoreToCommessa(direzioneLavoriId: number, direttoreLavoriId: number): Promise<void>;
  removeDirettoreFromCommessa(direzioneLavoriId: number, direttoreLavoriId: number): Promise<void>;

  getVerbaliByDirezioneLavori(direzioneLavoriId: number): Promise<(VerbaleDirezioneLavori & { tecnico: DirettoreLavori })[]>;
  getVerbaliFinalizatiCountByDirezioneLavori(direzioneLavoriId: number): Promise<number>;
  getVerbaleDirezioneLavori(id: number): Promise<VerbaleDirezioneLavori | undefined>;
  createVerbaleDirezioneLavori(verbale: InsertVerbaleDirezioneLavori): Promise<VerbaleDirezioneLavori>;
  updateVerbaleDirezioneLavori(id: number, verbale: Partial<InsertVerbaleDirezioneLavori>): Promise<VerbaleDirezioneLavori | undefined>;
  deleteVerbaleDirezioneLavori(id: number): Promise<boolean>;

  // Metodi per rubrica persone presenti
  getRubricaPersonePresenti(direzioneLavoriId: number): Promise<RubricaPersonaPresente[]>;
  addToRubricaPersonePresenti(persona: InsertRubricaPersonaPresente): Promise<RubricaPersonaPresente>;
  deleteFromRubricaPersonePresenti(id: number): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getAllCantieri(): Promise<Cantiere[]> {
    return await db.select().from(cantieri).orderBy(cantieri.id);
  }

  async getCantiere(id: number): Promise<Cantiere | undefined> {
    const [cantiere] = await db.select().from(cantieri).where(eq(cantieri.id, id));
    return cantiere || undefined;
  }

  async createCantiere(insertCantiere: InsertCantiere): Promise<Cantiere> {
    const [cantiere] = await db
      .insert(cantieri)
      .values(insertCantiere)
      .returning();
    return cantiere;
  }

  async updateCantiere(id: number, updateData: Partial<InsertCantiere>): Promise<Cantiere | undefined> {
    const [cantiere] = await db
      .update(cantieri)
      .set(updateData)
      .where(eq(cantieri.id, id))
      .returning();
    return cantiere || undefined;
  }

  async deleteCantiere(id: number): Promise<boolean> {
    const result = await db.delete(cantieri).where(eq(cantieri.id, id));
    return (result.rowCount || 0) > 0;
  }

  async searchCantieri(searchTerm: string): Promise<Cantiere[]> {
    if (!searchTerm.trim()) {
      return this.getAllCantieri();
    }

    const term = `%${searchTerm.toLowerCase()}%`;
    return await db
      .select()
      .from(cantieri)
      .where(
        or(
          ilike(cantieri.codiceCommessa, term),
          ilike(cantieri.indirizzo, term),
          ilike(cantieri.committente, term),
          ilike(cantieri.oggetto, term),
          ilike(cantieri.cseNominato, term)
        )
      )
      .orderBy(cantieri.id);
  }

  async getAllTecnici(): Promise<Tecnico[]> {
    return await db.select().from(tecnici).orderBy(tecnici.cognome, tecnici.nome);
  }

  async getTecnico(id: number): Promise<Tecnico | undefined> {
    const [tecnico] = await db.select().from(tecnici).where(eq(tecnici.id, id));
    return tecnico || undefined;
  }

  async createTecnico(insertTecnico: InsertTecnico): Promise<Tecnico> {
    const [tecnico] = await db
      .insert(tecnici)
      .values(insertTecnico)
      .returning();
    return tecnico;
  }

  async updateTecnico(id: number, updateData: Partial<InsertTecnico>): Promise<Tecnico | undefined> {
    const [tecnico] = await db
      .update(tecnici)
      .set(updateData)
      .where(eq(tecnici.id, id))
      .returning();
    return tecnico || undefined;
  }

  async deleteTecnico(id: number): Promise<boolean> {
    const result = await db.delete(tecnici).where(eq(tecnici.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getSopralluoghiByCantiere(cantiereId: number): Promise<(Sopralluogo & { tecnico: Tecnico })[]> {
    const results = await db
      .select({
        sopralluogo: sopralluoghi,
        tecnico: tecnici,
      })
      .from(sopralluoghi)
      .innerJoin(tecnici, eq(sopralluoghi.tecnicoId, tecnici.id))
      .where(eq(sopralluoghi.cantiereId, cantiereId))
      .orderBy(desc(sopralluoghi.data)); // Ordinamento decrescente: più recente in alto

    return results.map(result => ({
      ...result.sopralluogo,
      tecnico: result.tecnico,
    }));
  }

  async getSopralluogo(id: number): Promise<Sopralluogo | undefined> {
    const [sopralluogo] = await db.select().from(sopralluoghi).where(eq(sopralluoghi.id, id));
    return sopralluogo || undefined;
  }

  async getSopralluogoByNumero(numeroSopralluogo: string): Promise<Sopralluogo | undefined> {
    const [sopralluogo] = await db.select().from(sopralluoghi).where(eq(sopralluoghi.numeroSopralluogo, numeroSopralluogo));
    return sopralluogo || undefined;
  }

  async createSopralluogo(insertSopralluogo: InsertSopralluogo): Promise<Sopralluogo> {
    const [sopralluogo] = await db
      .insert(sopralluoghi)
      .values(insertSopralluogo)
      .returning();
    return sopralluogo;
  }

  async updateSopralluogo(id: number, updateData: Partial<InsertSopralluogo>): Promise<Sopralluogo | undefined> {
    const [sopralluogo] = await db
      .update(sopralluoghi)
      .set(updateData)
      .where(eq(sopralluoghi.id, id))
      .returning();
    return sopralluogo || undefined;
  }

  async deleteSopralluogo(id: number): Promise<boolean> {
    const result = await db.delete(sopralluoghi).where(eq(sopralluoghi.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getPersonePresentiBycantiere(cantiereId: number): Promise<PersonaPresente[]> {
    try {
      return await db.select().from(personePresenti).where(eq(personePresenti.cantiereId, cantiereId));
    } catch (error) {
      console.error("Error fetching persone presenti:", error);
      return [];
    }
  }

  async createPersonaPresente(insertPersona: InsertPersonaPresente): Promise<PersonaPresente> {
    const [persona] = await db
      .insert(personePresenti)
      .values(insertPersona)
      .returning();
    return persona;
  }

  async deletePersonaPresente(id: number): Promise<boolean> {
    try {
      const result = await db.delete(personePresenti).where(eq(personePresenti.id, id));
      return result.rowCount !== null && result.rowCount > 0;
    } catch (error) {
      console.error("Error deleting persona presente:", error);
      return false;
    }
  }

  async getAllDirettoriLavori(): Promise<DirettoreLavori[]> {
    return await db.select().from(direttoriLavori).where(eq(direttoriLavori.attivo, "true")).orderBy(direttoriLavori.cognome);
  }

  async getDirettoreLavori(id: number): Promise<DirettoreLavori | undefined> {
    const [direttore] = await db.select().from(direttoriLavori).where(eq(direttoriLavori.id, id));
    return direttore || undefined;
  }

  async createDirettoreLavori(insertDirettore: InsertDirettoreLavori): Promise<DirettoreLavori> {
    const [direttore] = await db
      .insert(direttoriLavori)
      .values(insertDirettore)
      .returning();
    return direttore;
  }

  async updateDirettoreLavori(id: number, updateData: Partial<InsertDirettoreLavori>): Promise<DirettoreLavori | undefined> {
    const [direttore] = await db
      .update(direttoriLavori)
      .set(updateData)
      .where(eq(direttoriLavori.id, id))
      .returning();
    return direttore || undefined;
  }

  async deleteDirettoreLavori(id: number): Promise<boolean> {
    try {
      const result = await db.delete(direttoriLavori).where(eq(direttoriLavori.id, id));
      return result.rowCount !== null && result.rowCount > 0;
    } catch (error) {
      console.error("Error deleting direttore lavori:", error);
      return false;
    }
  }

  async getAllDirezioneLavori(): Promise<DirezioneLavori[]> {
    return await db.select().from(direzioneLavori).orderBy(direzioneLavori.codiceCommessa);
  }

  async getDirezioneLavori(id: number): Promise<DirezioneLavori | undefined> {
    const [lavoro] = await db.select().from(direzioneLavori).where(eq(direzioneLavori.id, id));
    return lavoro || undefined;
  }

  async createDirezioneLavori(insertLavoro: InsertDirezioneLavori, direttoriIds: number[]): Promise<DirezioneLavori> {
    const [lavoro] = await db
      .insert(direzioneLavori)
      .values(insertLavoro)
      .returning();
    
    // Filter out any null/undefined IDs and add directors to the commessa
    const validDirettoriIds = direttoriIds.filter(id => id != null && !isNaN(id));
    console.log("Valid director IDs:", validDirettoriIds);
    
    if (validDirettoriIds.length > 0) {
      const direttoriData = validDirettoriIds.map(direttoreId => ({
        direzioneLavoriId: lavoro.id,
        direttoreLavoriId: direttoreId,
      }));
      
      console.log("Inserting director relationships:", direttoriData);
      await db.insert(direzioneLavoriDirettori).values(direttoriData);
    }
    
    return lavoro;
  }

  async updateDirezioneLavori(id: number, updateData: Partial<InsertDirezioneLavori>): Promise<DirezioneLavori | undefined> {
    const [lavoro] = await db
      .update(direzioneLavori)
      .set(updateData)
      .where(eq(direzioneLavori.id, id))
      .returning();
    return lavoro || undefined;
  }

  async deleteDirezioneLavori(id: number): Promise<boolean> {
    try {
      const result = await db.delete(direzioneLavori).where(eq(direzioneLavori.id, id));
      return result.rowCount !== null && result.rowCount > 0;
    } catch (error) {
      console.error("Error deleting direzione lavori:", error);
      return false;
    }
  }

  async searchDirezioneLavori(searchTerm: string): Promise<DirezioneLavori[]> {
    return await db
      .select()
      .from(direzioneLavori)
      .where(
        or(
          ilike(direzioneLavori.codiceCommessa, `%${searchTerm}%`),
          ilike(direzioneLavori.indirizzo, `%${searchTerm}%`),
          ilike(direzioneLavori.committente, `%${searchTerm}%`),
          ilike(direzioneLavori.oggetto, `%${searchTerm}%`)
        )
      )
      .orderBy(direzioneLavori.codiceCommessa);
  }

  async getDirettoriByDirezioneLavori(direzioneLavoriId: number): Promise<DirettoreLavori[]> {
    const result = await db
      .select({
        id: direttoriLavori.id,
        nome: direttoriLavori.nome,
        cognome: direttoriLavori.cognome,
        qualifica: direttoriLavori.qualifica,
        attivo: direttoriLavori.attivo,
        createdAt: direttoriLavori.createdAt,
      })
      .from(direzioneLavoriDirettori)
      .innerJoin(direttoriLavori, eq(direzioneLavoriDirettori.direttoreLavoriId, direttoriLavori.id))
      .where(eq(direzioneLavoriDirettori.direzioneLavoriId, direzioneLavoriId));
    
    return result;
  }

  async addDirettoreToCommessa(direzioneLavoriId: number, direttoreLavoriId: number): Promise<void> {
    await db.insert(direzioneLavoriDirettori).values({
      direzioneLavoriId,
      direttoreLavoriId,
    });
  }

  async removeDirettoreFromCommessa(direzioneLavoriId: number, direttoreLavoriId: number): Promise<void> {
    await db
      .delete(direzioneLavoriDirettori)
      .where(
        and(
          eq(direzioneLavoriDirettori.direzioneLavoriId, direzioneLavoriId),
          eq(direzioneLavoriDirettori.direttoreLavoriId, direttoreLavoriId)
        )
      );
  }

  async getVerbaliByDirezioneLavori(direzioneLavoriId: number): Promise<(VerbaleDirezioneLavori & { tecnico: DirettoreLavori })[]> {
    const results = await db
      .select({
        verbale: verbaliDirezioneLavori,
        tecnico: direttoriLavori,
      })
      .from(verbaliDirezioneLavori)
      .innerJoin(direttoriLavori, eq(verbaliDirezioneLavori.tecnicoId, direttoriLavori.id))
      .where(eq(verbaliDirezioneLavori.direzioneLavoriId, direzioneLavoriId))
      .orderBy(desc(verbaliDirezioneLavori.data)); // Ordinamento decrescente: più recente in alto

    return results.map(result => ({
      ...result.verbale,
      tecnico: result.tecnico,
    }));
  }

  async getVerbaliFinalizatiCountByDirezioneLavori(direzioneLavoriId: number): Promise<number> {
    const results = await db
      .select({ count: sql<number>`count(*)` })
      .from(verbaliDirezioneLavori)
      .where(
        and(
          eq(verbaliDirezioneLavori.direzioneLavoriId, direzioneLavoriId),
          eq(verbaliDirezioneLavori.stato, 'finalizzato')
        )
      );

    return results[0]?.count || 0;
  }

  async getVerbaleDirezioneLavori(id: number): Promise<VerbaleDirezioneLavori | undefined> {
    const [verbale] = await db.select().from(verbaliDirezioneLavori).where(eq(verbaliDirezioneLavori.id, id));
    return verbale || undefined;
  }

  async createVerbaleDirezioneLavori(insertVerbale: InsertVerbaleDirezioneLavori): Promise<VerbaleDirezioneLavori> {
    const [verbale] = await db
      .insert(verbaliDirezioneLavori)
      .values(insertVerbale)
      .returning();
    return verbale;
  }

  async updateVerbaleDirezioneLavori(id: number, updateData: Partial<InsertVerbaleDirezioneLavori>): Promise<VerbaleDirezioneLavori | undefined> {
    const [verbale] = await db
      .update(verbaliDirezioneLavori)
      .set(updateData)
      .where(eq(verbaliDirezioneLavori.id, id))
      .returning();
    return verbale || undefined;
  }

  async deleteVerbaleDirezioneLavori(id: number): Promise<boolean> {
    try {
      const result = await db.delete(verbaliDirezioneLavori).where(eq(verbaliDirezioneLavori.id, id));
      return result.rowCount !== null && result.rowCount > 0;
    } catch (error) {
      console.error("Error deleting verbale direzione lavori:", error);
      return false;
    }
  }

  // Metodi per rubrica persone presenti
  async getRubricaPersonePresenti(direzioneLavoriId: number): Promise<RubricaPersonaPresente[]> {
    return await db.select().from(rubricaPersonePresenti).where(eq(rubricaPersonePresenti.direzioneLavoriId, direzioneLavoriId));
  }

  async addToRubricaPersonePresenti(persona: InsertRubricaPersonaPresente): Promise<RubricaPersonaPresente> {
    // Controlla se la persona esiste già per evitare duplicati
    const existing = await db.select().from(rubricaPersonePresenti)
      .where(and(
        eq(rubricaPersonePresenti.direzioneLavoriId, persona.direzioneLavoriId),
        eq(rubricaPersonePresenti.nome, persona.nome),
        eq(rubricaPersonePresenti.qualifica, persona.qualifica)
      ));
    
    if (existing.length > 0) {
      // Se esiste, aggiorna i dati
      const [updated] = await db.update(rubricaPersonePresenti)
        .set({
          email: persona.email,
          telefono: persona.telefono
        })
        .where(eq(rubricaPersonePresenti.id, existing[0].id))
        .returning();
      return updated;
    } else {
      // Se non esiste, crea nuovo record
      const [inserted] = await db.insert(rubricaPersonePresenti).values(persona).returning();
      return inserted;
    }
  }

  async deleteFromRubricaPersonePresenti(id: number): Promise<boolean> {
    const result = await db.delete(rubricaPersonePresenti).where(eq(rubricaPersonePresenti.id, id));
    return result.rowCount > 0;
  }
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private cantieri: Map<number, Cantiere>;
  private tecnici: Map<number, Tecnico>;
  private sopralluoghi: Map<number, Sopralluogo>;
  private currentUserId: number;
  private currentCantiereId: number;
  private currentTecnicoId: number;
  private currentSopralluogoId: number;

  constructor() {
    this.users = new Map();
    this.cantieri = new Map();
    this.tecnici = new Map();
    this.sopralluoghi = new Map();
    this.currentUserId = 1;
    this.currentCantiereId = 1;
    this.currentTecnicoId = 1;
    this.currentSopralluogoId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getAllCantieri(): Promise<Cantiere[]> {
    return Array.from(this.cantieri.values()).sort((a, b) => b.id - a.id);
  }

  async getCantiere(id: number): Promise<Cantiere | undefined> {
    return this.cantieri.get(id);
  }

  async createCantiere(insertCantiere: InsertCantiere): Promise<Cantiere> {
    const id = this.currentCantiereId++;
    const cantiere: Cantiere = { ...insertCantiere, id };
    this.cantieri.set(id, cantiere);
    return cantiere;
  }

  async updateCantiere(id: number, updateData: Partial<InsertCantiere>): Promise<Cantiere | undefined> {
    const existingCantiere = this.cantieri.get(id);
    if (!existingCantiere) {
      return undefined;
    }
    
    const updatedCantiere: Cantiere = { ...existingCantiere, ...updateData };
    this.cantieri.set(id, updatedCantiere);
    return updatedCantiere;
  }

  async deleteCantiere(id: number): Promise<boolean> {
    return this.cantieri.delete(id);
  }

  async searchCantieri(searchTerm: string): Promise<Cantiere[]> {
    const allCantieri = await this.getAllCantieri();
    if (!searchTerm.trim()) {
      return allCantieri;
    }

    const term = searchTerm.toLowerCase();
    return allCantieri.filter(cantiere => 
      cantiere.codiceCommessa.toLowerCase().includes(term) ||
      cantiere.indirizzo.toLowerCase().includes(term) ||
      cantiere.committente.toLowerCase().includes(term) ||
      cantiere.oggetto.toLowerCase().includes(term) ||
      cantiere.cseNominato.toLowerCase().includes(term)
    );
  }

  async getAllTecnici(): Promise<Tecnico[]> {
    return Array.from(this.tecnici.values()).sort((a, b) => a.cognome.localeCompare(b.cognome));
  }

  async getTecnico(id: number): Promise<Tecnico | undefined> {
    return this.tecnici.get(id);
  }

  async createTecnico(insertTecnico: InsertTecnico): Promise<Tecnico> {
    const id = this.currentTecnicoId++;
    const tecnico: Tecnico = { ...insertTecnico, id };
    this.tecnici.set(id, tecnico);
    return tecnico;
  }

  async updateTecnico(id: number, updateData: Partial<InsertTecnico>): Promise<Tecnico | undefined> {
    const existingTecnico = this.tecnici.get(id);
    if (!existingTecnico) {
      return undefined;
    }
    
    const updatedTecnico: Tecnico = { ...existingTecnico, ...updateData };
    this.tecnici.set(id, updatedTecnico);
    return updatedTecnico;
  }

  async deleteTecnico(id: number): Promise<boolean> {
    return this.tecnici.delete(id);
  }

  async getSopralluoghiByCantiere(cantiereId: number): Promise<(Sopralluogo & { tecnico: Tecnico })[]> {
    const cantieriSopralluoghi = Array.from(this.sopralluoghi.values())
      .filter(s => s.cantiereId === cantiereId)
      .sort((a, b) => b.data.localeCompare(a.data)); // Ordinamento decrescente: più recente in alto
    
    return cantieriSopralluoghi.map(sopralluogo => {
      const tecnico = this.tecnici.get(sopralluogo.tecnicoId);
      return {
        ...sopralluogo,
        tecnico: tecnico!,
      };
    });
  }

  async getSopralluogo(id: number): Promise<Sopralluogo | undefined> {
    return this.sopralluoghi.get(id);
  }

  async getSopralluogoByNumero(numeroSopralluogo: string): Promise<Sopralluogo | undefined> {
    for (const sopralluogo of this.sopralluoghi.values()) {
      if (sopralluogo.numeroSopralluogo === numeroSopralluogo) {
        return sopralluogo;
      }
    }
    return undefined;
  }

  async createSopralluogo(insertSopralluogo: InsertSopralluogo): Promise<Sopralluogo> {
    const id = this.currentSopralluogoId++;
    const sopralluogo: Sopralluogo = { ...insertSopralluogo, id };
    this.sopralluoghi.set(id, sopralluogo);
    return sopralluogo;
  }

  async updateSopralluogo(id: number, updateData: Partial<InsertSopralluogo>): Promise<Sopralluogo | undefined> {
    const existingSopralluogo = this.sopralluoghi.get(id);
    if (!existingSopralluogo) {
      return undefined;
    }
    
    const updatedSopralluogo: Sopralluogo = { ...existingSopralluogo, ...updateData };
    this.sopralluoghi.set(id, updatedSopralluogo);
    return updatedSopralluogo;
  }

  async deleteSopralluogo(id: number): Promise<boolean> {
    return this.sopralluoghi.delete(id);
  }

  async getPersonePresentiBycantiere(cantiereId: number): Promise<PersonaPresente[]> {
    // MemStorage non supporta persone presenti persistenti
    return [];
  }

  async createPersonaPresente(insertPersona: InsertPersonaPresente): Promise<PersonaPresente> {
    // MemStorage non supporta persone presenti persistenti
    const persona: PersonaPresente = { 
      ...insertPersona, 
      id: Date.now(), 
      createdAt: new Date() 
    };
    return persona;
  }

  async deletePersonaPresente(id: number): Promise<boolean> {
    // MemStorage non supporta persone presenti persistenti
    return true;
  }

  // Metodi stub per rubrica persone presenti
  async getRubricaPersonePresenti(direzioneLavoriId: number): Promise<RubricaPersonaPresente[]> {
    return [];
  }

  async addToRubricaPersonePresenti(persona: InsertRubricaPersonaPresente): Promise<RubricaPersonaPresente> {
    return { ...persona, id: Date.now(), createdAt: new Date() };
  }

  async deleteFromRubricaPersonePresenti(id: number): Promise<boolean> {
    return true;
  }
}

export const storage = new DatabaseStorage();
