// Database Storage Manager per Orbyte Engineering
// Gestisce storage di loghi e PDF direttamente nel database PostgreSQL

import { db } from './db';
import { loghiAziendali, verbaliDirezioneLavori, sopralluoghi } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import * as crypto from 'crypto';

interface LogoUploadResult {
  success: boolean;
  id?: number;
  error?: string;
}

interface LogoDownloadResult {
  success: boolean;
  contenuto?: string;
  contentType?: string;
  error?: string;
}

interface PDFStorageResult {
  success: boolean;
  size?: number;
  error?: string;
}

interface PDFDownloadResult {
  success: boolean;
  contenuto?: Buffer;
  contentType?: string;
  nome?: string;
  error?: string;
}

export class DatabaseStorageManager {
  
  // ===== LOGO OPERATIONS =====
  
  /**
   * Carica un logo nel database
   */
  async uploadLogo(
    nome: string,
    fileBuffer: Buffer,
    contentType: string = 'image/png',
    descrizione?: string
  ): Promise<LogoUploadResult> {
    try {
      // Converti il buffer in base64
      const contenutoBase64 = fileBuffer.toString('base64');
      
      // Calcola dimensioni dell'immagine (approssimative)
      const dimensioni = JSON.stringify({
        originalSize: fileBuffer.length,
        encodedSize: contenutoBase64.length
      });

      // Verifica se esiste già un logo con questo nome
      const existingLogo = await db
        .select()
        .from(loghiAziendali)
        .where(eq(loghiAziendali.nome, nome))
        .limit(1);

      if (existingLogo.length > 0) {
        // Aggiorna il logo esistente
        const [updatedLogo] = await db
          .update(loghiAziendali)
          .set({
            contenuto: contenutoBase64,
            contentType,
            descrizione,
            dimensioni,
            updatedAt: new Date(),
          })
          .where(eq(loghiAziendali.nome, nome))
          .returning({ id: loghiAziendali.id });

        console.log(`✅ Logo aggiornato in database: ${nome} (${this.formatBytes(fileBuffer.length)})`);
        
        return {
          success: true,
          id: updatedLogo.id
        };
      } else {
        // Crea nuovo logo
        const [newLogo] = await db
          .insert(loghiAziendali)
          .values({
            nome,
            contenuto: contenutoBase64,
            contentType,
            descrizione,
            dimensioni,
            attivo: 'true'
          })
          .returning({ id: loghiAziendali.id });

        console.log(`✅ Logo salvato in database: ${nome} (${this.formatBytes(fileBuffer.length)})`);
        
        return {
          success: true,
          id: newLogo.id
        };
      }
    } catch (error) {
      console.error('❌ Errore durante l\'upload del logo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  /**
   * Scarica un logo dal database
   */
  async downloadLogo(nome: string): Promise<LogoDownloadResult> {
    try {
      const [logo] = await db
        .select()
        .from(loghiAziendali)
        .where(and(
          eq(loghiAziendali.nome, nome),
          eq(loghiAziendali.attivo, 'true')
        ))
        .limit(1);

      if (!logo) {
        return {
          success: false,
          error: `Logo non trovato: ${nome}`
        };
      }

      console.log(`📥 Logo scaricato dal database: ${nome}`);

      return {
        success: true,
        contenuto: logo.contenuto,
        contentType: logo.contentType || 'image/png'
      };
    } catch (error) {
      console.error('❌ Errore durante il download del logo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  /**
   * Lista tutti i loghi disponibili
   */
  async listLogos(): Promise<Array<{ nome: string; descrizione?: string; contentType: string; createdAt: Date }>> {
    try {
      const logos = await db
        .select({
          nome: loghiAziendali.nome,
          descrizione: loghiAziendali.descrizione,
          contentType: loghiAziendali.contentType,
          createdAt: loghiAziendali.createdAt
        })
        .from(loghiAziendali)
        .where(eq(loghiAziendali.attivo, 'true'))
        .orderBy(loghiAziendali.nome);

      return logos;
    } catch (error) {
      console.error('❌ Errore durante il listing dei loghi:', error);
      return [];
    }
  }

  /**
   * Elimina un logo
   */
  async deleteLogo(nome: string): Promise<boolean> {
    try {
      // Soft delete: marca come non attivo
      const result = await db
        .update(loghiAziendali)
        .set({ 
          attivo: 'false',
          updatedAt: new Date() 
        })
        .where(eq(loghiAziendali.nome, nome));

      console.log(`🗑️ Logo rimosso: ${nome}`);
      return true;
    } catch (error) {
      console.error('❌ Errore durante l\'eliminazione del logo:', error);
      return false;
    }
  }

  // ===== PDF OPERATIONS =====

  /**
   * Salva un PDF nel database per un verbale
   */
  async storePDF(
    verbaleId: number,
    pdfBuffer: Buffer,
    nomeFile: string
  ): Promise<PDFStorageResult> {
    try {
      // Converti il PDF in base64
      const pdfBase64 = pdfBuffer.toString('base64');

      // Aggiorna il verbale con i dati del PDF
      await db
        .update(verbaliDirezioneLavori)
        .set({
          pdfContenuto: pdfBase64,
          pdfNome: nomeFile,
          pdfSize: pdfBuffer.length,
          pdfGeneratedAt: new Date(),
          pdfPath: `db://${verbaleId}` // Indica che è salvato in database
        })
        .where(eq(verbaliDirezioneLavori.id, verbaleId));

      console.log(`💾 PDF salvato in database per verbale ${verbaleId}: ${nomeFile} (${this.formatBytes(pdfBuffer.length)})`);

      return {
        success: true,
        size: pdfBuffer.length
      };
    } catch (error) {
      console.error('❌ Errore durante il salvataggio del PDF:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  /**
   * Scarica un PDF dal database
   */
  async downloadPDF(verbaleId: number): Promise<PDFDownloadResult> {
    try {
      const [verbale] = await db
        .select({
          pdfContenuto: verbaliDirezioneLavori.pdfContenuto,
          pdfNome: verbaliDirezioneLavori.pdfNome,
          pdfSize: verbaliDirezioneLavori.pdfSize
        })
        .from(verbaliDirezioneLavori)
        .where(eq(verbaliDirezioneLavori.id, verbaleId))
        .limit(1);

      if (!verbale || !verbale.pdfContenuto) {
        return {
          success: false,
          error: `PDF non trovato per verbale ${verbaleId}`
        };
      }

      // Converti da base64 a Buffer
      const pdfBuffer = Buffer.from(verbale.pdfContenuto, 'base64');

      console.log(`📥 PDF scaricato dal database per verbale ${verbaleId}: ${verbale.pdfNome} (${this.formatBytes(pdfBuffer.length)})`);

      return {
        success: true,
        contenuto: pdfBuffer,
        contentType: 'application/pdf',
        nome: verbale.pdfNome || `verbale_${verbaleId}.pdf`
      };
    } catch (error) {
      console.error('❌ Errore durante il download del PDF:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  /**
   * Verifica se un verbale ha un PDF salvato
   */
  async hasPDF(verbaleId: number): Promise<boolean> {
    try {
      const [verbale] = await db
        .select({ pdfContenuto: verbaliDirezioneLavori.pdfContenuto })
        .from(verbaliDirezioneLavori)
        .where(eq(verbaliDirezioneLavori.id, verbaleId))
        .limit(1);

      return !!(verbale?.pdfContenuto);
    } catch (error) {
      console.error('❌ Errore durante la verifica del PDF:', error);
      return false;
    }
  }

  /**
   * Elimina un PDF dal database
   */
  async deletePDF(verbaleId: number): Promise<boolean> {
    try {
      await db
        .update(verbaliDirezioneLavori)
        .set({
          pdfContenuto: null,
          pdfNome: null,
          pdfSize: null,
          pdfGeneratedAt: null,
          pdfPath: null
        })
        .where(eq(verbaliDirezioneLavori.id, verbaleId));

      console.log(`🗑️ PDF rimosso dal database per verbale ${verbaleId}`);
      return true;
    } catch (error) {
      console.error('❌ Errore durante l\'eliminazione del PDF:', error);
      return false;
    }
  }

  // ===== SOPRALLUOGHI PDF METHODS =====
  
  /**
   * Salva un PDF nel database per un sopralluogo
   */
  async storePDFSopralluogo(
    sopralluogoId: number,
    pdfBuffer: Buffer,
    nomeFile: string
  ): Promise<PDFStorageResult> {
    try {
      // Converti il PDF in base64
      const pdfBase64 = pdfBuffer.toString('base64');

      // Aggiorna il sopralluogo con i dati del PDF
      await db
        .update(sopralluoghi)
        .set({
          pdfContenuto: pdfBase64,
          pdfNome: nomeFile,
          pdfSize: pdfBuffer.length,
          pdfGeneratedAt: new Date(),
          verbaleUrl: `/api/files/verbali/${nomeFile}` // Mantieni per compatibilità
        })
        .where(eq(sopralluoghi.id, sopralluogoId));

      console.log(`💾 PDF salvato in database per sopralluogo ${sopralluogoId}: ${nomeFile} (${this.formatBytes(pdfBuffer.length)})`);

      return {
        success: true,
        size: pdfBuffer.length
      };
    } catch (error) {
      console.error('❌ Errore durante il salvataggio del PDF sopralluogo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  /**
   * Scarica un PDF dal database per un sopralluogo
   */
  async downloadPDFSopralluogo(sopralluogoId: number): Promise<PDFDownloadResult> {
    try {
      const [sopralluogo] = await db
        .select({
          pdfContenuto: sopralluoghi.pdfContenuto,
          pdfNome: sopralluoghi.pdfNome,
          pdfSize: sopralluoghi.pdfSize
        })
        .from(sopralluoghi)
        .where(eq(sopralluoghi.id, sopralluogoId));

      if (!sopralluogo || !sopralluogo.pdfContenuto) {
        console.log(`⚠️ PDF non trovato per sopralluogo ${sopralluogoId}`);
        return {
          success: false,
          error: 'PDF non trovato'
        };
      }

      // Converti da base64 a Buffer
      const pdfBuffer = Buffer.from(sopralluogo.pdfContenuto, 'base64');
      
      console.log(`📄 PDF scaricato dal database per sopralluogo ${sopralluogoId}: ${sopralluogo.pdfNome} (${this.formatBytes(sopralluogo.pdfSize || 0)})`);

      return {
        success: true,
        contenuto: pdfBuffer,
        contentType: 'application/pdf',
        nome: sopralluogo.pdfNome || `verbale_${sopralluogoId}.pdf`
      };
    } catch (error) {
      console.error('❌ Errore durante il download del PDF sopralluogo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  /**
   * Elimina un PDF dal database per un sopralluogo
   */
  async deletePDFSopralluogo(sopralluogoId: number): Promise<boolean> {
    try {
      await db
        .update(sopralluoghi)
        .set({
          pdfContenuto: null,
          pdfNome: null,
          pdfSize: null,
          pdfGeneratedAt: null,
          verbaleUrl: null
        })
        .where(eq(sopralluoghi.id, sopralluogoId));

      console.log(`🗑️ PDF rimosso dal database per sopralluogo ${sopralluogoId}`);
      return true;
    } catch (error) {
      console.error('❌ Errore durante l\'eliminazione del PDF sopralluogo:', error);
      return false;
    }
  }

  // ===== UTILITY METHODS =====

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Ottieni statistiche di utilizzo database
   */
  async getStorageStats(): Promise<any> {
    try {
      // Conta loghi attivi
      const logoCount = await db
        .select()
        .from(loghiAziendali)
        .where(eq(loghiAziendali.attivo, 'true'));

      // Conta PDF salvati
      const pdfCount = await db
        .select()
        .from(verbaliDirezioneLavori)
        .where(eq(verbaliDirezioneLavori.pdfContenuto, null)); // Count non-null

      return {
        storageType: 'database-postgresql',
        logoCount: logoCount.length,
        pdfCount: pdfCount.length,
        note: 'Using PostgreSQL database storage with base64 encoding'
      };
    } catch (error) {
      console.error('❌ Errore durante il recupero delle statistiche:', error);
      return {
        storageType: 'database-postgresql',
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }
}

// Singleton instance
export const databaseStorageManager = new DatabaseStorageManager();