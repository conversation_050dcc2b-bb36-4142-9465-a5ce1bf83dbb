// Endpoint di test per Object Storage Manager (versione JS)
// Orbyte Engineering - Sistema Gestione Cantieri

import { Router } from 'express';

const router = Router();

// GET /api/storage/test - Test rapido Object Storage
router.get('/test', async (req, res) => {
  try {
    console.log('🧪 API Test Object Storage richiesto');
    
    // Test environment detection
    const isReplitEnv = !!(
      process.env.REPLIT_APP_NAME ||
      process.env.REPL_ID ||
      process.env.REPL_SLUG
    );
    
    // Test Object Storage SDK
    let objectStorageAvailable = false;
    let clientCreated = false;
    let error = null;
    
    try {
      const { Client } = await import('@replit/object-storage');
      objectStorageAvailable = true;
      
      if (isReplitEnv) {
        const client = new Client();
        clientCreated = true;
      }
    } catch (err) {
      error = err.message;
    }
    
    res.json({
      success: objectStorageAvailable,
      environment: {
        isReplit: isReplitEnv,
        nodeVersion: process.version,
        platform: process.platform
      },
      objectStorage: {
        available: objectStorageAvailable,
        clientCreated: clientCreated,
        error: error
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Errore test storage:', error);
    res.status(500).json({
      error: 'Test storage fallito',
      message: error instanceof Error ? error.message : 'Errore sconosciuto'
    });
  }
});

// GET /api/storage/info - Info ambiente
router.get('/info', async (req, res) => {
  try {
    const envVars = {
      REPLIT_APP_NAME: process.env.REPLIT_APP_NAME || 'undefined',
      REPL_ID: process.env.REPL_ID || 'undefined', 
      REPL_SLUG: process.env.REPL_SLUG || 'undefined',
      NODE_ENV: process.env.NODE_ENV || 'undefined'
    };
    
    const isReplitEnv = !!(envVars.REPLIT_APP_NAME !== 'undefined' || envVars.REPL_ID !== 'undefined');
    
    res.json({
      success: true,
      environment: envVars,
      detected: {
        isReplit: isReplitEnv,
        storageRecommendation: isReplitEnv ? 'Object Storage' : 'Local Filesystem'
      },
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Errore info storage:', error);
    res.status(500).json({
      error: 'Info storage fallito',
      message: error instanceof Error ? error.message : 'Errore sconosciuto'
    });
  }
});

export default router;