# STORAGE.md - Analisi Problemi Storage e Deployment

## 🚨 PROBLEMA CRITICO: Perdita File Durante Deployment

### ❌ Situazione Attuale

**Deployment Replit Production:**
- ✅ **Database PostgreSQL**: Completamente persistente
- ❌ **File system locale**: TUTTO PERSO ad ogni deployment
- ❌ **803MB di file**: CANCELLATI ad ogni rilascio
- ❌ **419 file (foto + PDF + loghi)**: Perdita completa dei dati

### 📊 Analisi Directory Uploads

```
DIMENSIONI TOTALI: 803MB
├── photos/     442MB (340+ foto)
├── verbali/    357MB (62+ PDF verbali)  
├── logos/      4.5MB (11+ loghi)
├── pdfs/       401KB (1 PDF upload)
└── metadata    20KB (backup metadata)

TOTAL FILES: 419 file
TIPO FILES: .jpg, .png, .pdf
```

**Ogni deployment = 803MB di dati PERSI DEFINITIVAMENTE**

## 🔧 Il Sistema di "Backup" Attuale è Inefficace

### File `server/backup.ts` - Limitazioni

```typescript
// ❌ QUESTO NON SALVA I FILE FISICI
export function verifyFileIntegrity() {
  // Conta solo i file ma NON li preserva
  const files = fs.readdirSync(dir).filter(f => !f.startsWith('.'));
  totalFiles += files.length; // Solo statistica
}

export function createBackupMetadata() {
  // Crea JSON con lista file MA i file fisici si perdono
  fs.writeFileSync('uploads/backup-metadata.json', JSON.stringify(backupInfo, null, 2));
}
```

**Funzionalità Reali:**
- ✅ Conta i file esistenti
- ✅ Crea lista JSON con nomi file
- ❌ NON preserva i file fisici
- ❌ NON li ripristina dopo deployment

**File `.gitkeep`:**
- ✅ Mantengono le directory VUOTE
- ❌ NON preservano il contenuto

## 💡 SOLUZIONE: Replit Object Storage

### ✅ Replit Object Storage - Analisi Tecnica

**Caratteristiche:**
- **Cloud Storage**: Basato su Google Cloud Storage
- **Persistente**: Sopravvive ai deployment
- **Scalabile**: Nessun limite filesystem
- **Cross-app**: Condivisibile tra applicazioni
- **SDK ufficiale**: JavaScript/Python support

**Organizzazione Bucket Proposta:**
```
orbyta-cantieri-storage/
├── photos/
│   ├── sopralluoghi/
│   └── verbali/
├── verbali/
│   ├── direzione-lavori/
│   └── sicurezza/
├── logos/
│   └── commesse/
└── temp/
    └── uploads/
```

### 🔄 Piano di Migrazione

**FASE 1: Setup Object Storage**
```bash
# Install Replit Object Storage SDK
npm install @replit/object-storage
```

**FASE 2: Refactor Upload System**
```typescript
// Sostituire multer filesystem con Object Storage
import { ObjectStorage } from '@replit/object-storage';

const storage = new ObjectStorage();

// Upload to cloud instead of local filesystem
await storage.uploadFile('photos/sopralluoghi/', file, filename);
```

**FASE 3: Migration Existing Files**
- Script di migrazione 803MB esistenti
- Aggiornamento path in database
- Test completo funzionalità

**FASE 4: Update API Endpoints**
- `/api/upload-photo` → Object Storage
- `/api/download-pdf` → Object Storage URLs
- Aggiornamento gestione loghi

## 📈 Stima Costi e Benefici

### Costi Object Storage
- **Storage**: ~0.80GB = ~$0.02/mese
- **Transfer**: Dipende da utilizzo
- **Operazioni**: API calls nominali

### Benefici
- ✅ **Persistenza dati** durante deployment
- ✅ **Scalabilità illimitata**
- ✅ **Performance migliori** (CDN Google)
- ✅ **Backup automatico** cloud
- ✅ **Condivisione cross-app**

## ⚠️ Impatto Business

### Scenario Attuale
1. Caricamento documenti e foto di progetto
2. Lavoro su pratiche per settimane/mesi
3. **DEPLOYMENT** → Perdita completa dati
4. **Necessità ricaricamento** di tutto il materiale
5. **Perdita continuità lavorativa**

### Scenario Post-Migrazione
1. Upload automatico su Object Storage
2. File permanentemente sicuri
3. Deploy senza interruzioni
4. **Continuità operativa garantita**

## 🎯 Action Plan

### Priority 1: ASSESSMENT
- ⚠️ **Valutazione impatto** deploy production
- 📋 **Documentazione rischi** attuali

### Priority 2: IMPLEMENTAZIONE Object Storage
1. Setup bucket Replit Object Storage
2. Migrazione sistema upload
3. Test completo funzionalità
4. Migrazione file esistenti

### Priority 3: DEPLOY SICURO
- ✅ Test completo persistenza file
- ✅ Verifica backup automatico
- ✅ Deploy production senza perdite

## 📋 Checklist Implementazione

### Setup Phase
- [ ] Creare bucket Object Storage su Replit
- [ ] Installare `@replit/object-storage` SDK
- [ ] Configurare credentials e access

### Development Phase  
- [ ] Refactor `server/routes.ts` upload endpoints
- [ ] Aggiornare multer configuration
- [ ] Implementare Object Storage API calls
- [ ] Aggiornare PDF generation con cloud URLs

### Migration Phase
- [ ] Script migrazione 803MB file esistenti
- [ ] Update database paths da filesystem a Object Storage
- [ ] Test completo upload/download/view

### Testing Phase
- [ ] Test upload foto durante sopralluoghi
- [ ] Test generazione PDF con foto cloud
- [ ] Test download PDF e loghi
- [ ] Test deployment senza perdite

### Production Phase
- [ ] Deploy con Object Storage attivo
- [ ] Monitoring utilizzo storage
- [ ] Verifica persistenza post-deployment
- [ ] Cleanup file locali obsoleti

## 🔚 CONCLUSIONI

**STATO ATTUALE: Non adatto per ambiente production**
- Perdita garantita di tutti i file ad ogni deploy
- Sistema di backup inefficace
- Rischio operativo elevato

**STATO POST-MIGRAZIONE: Production-ready**
- File permanentemente sicuri nel cloud
- Deploy senza perdite dati
- Scalabilità e performance ottimali
- Continuità operativa garantita

**RACCOMANDAZIONE: Implementare Object Storage prima del deploy production**