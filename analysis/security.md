# Analisi Sicurezza - Sistema Gestione Cantieri
## Orbyta Engineering SRL

**Data:** 17 Gennaio 2025  
**Sistema:** Gestione Cantieri (React + Express + PostgreSQL)  
**Ambiente:** Replit Deployment  

---

## 🚨 PROBLEMI CRITICI

### Livello di Rischio: **🔴 CRITICO**

**Problemi Identificati:**
- Nessuna autenticazione sugli upload
- Accesso pubblico a tutti i file
- Vulnerabilità path traversal
- Validazione file insufficiente
- Perdita dati completa ad ogni deploy

---

## 🔴 **CRITICO - Perdita File Durante Deploy**

**Problema:** Perdita completa di tutti i file ad ogni deployment.

**Situazione Attuale:**
- **Database:** ✅ Persistente (PostgreSQL)
- **File System:** ❌ PERDITA TOTALE ad ogni deploy
- **Dati a Rischio:** 803MB di file (419 file totali)
  - Foto: 442MB (340+ foto cantieri)
  - Verbali: 357MB (62+ PDF ufficiali)
  - Loghi: 4.5MB (11+ loghi aziendali)

**Codice Problematico:**
```typescript
// server/backup.ts - Sistema backup INEFFICACE
export function verifyFileIntegrity() {
  // Conta solo i file ma NON li preserva
  const files = fs.readdirSync(dir).filter(f => !f.startsWith('.'));
  totalFiles += files.length; // Solo statistica
}
```

**Impatto Business:**
- **Perdita dati completa** ad ogni deploy production
- **Interruzione continuità lavorativa**
- **Problemi conformità legale** - perdita documenti ufficiali
- **Danno relazioni clienti**

**CVSS:** 9.8 (Critico)

---

## 🔴 **CRITICO - Nessuna Autenticazione Upload**

**Problema:** Tutti gli endpoint di upload sono pubblici.

**Endpoint Vulnerabili:**
- `POST /api/upload-photo`
- `POST /api/upload-logo`
- `POST /api/upload-pdf`

**Codice Problematico:**
```typescript
// server/routes.ts
app.post('/api/upload-photo', upload.single('photo'), async (req, res) => {
  // NESSUN CONTROLLO AUTENTICAZIONE
  if (!req.file) {
    return res.status(400).json({ message: 'Nessun file caricato' });
  }
```

**Impatto:**
- Chiunque può caricare file
- Upload di malware possibili
- Attacchi DoS via storage

**CVSS:** 9.1 (Critico)

---

## 🔴 **CRITICO - Accesso Pubblico ai File**

**Problema:** Tutti i file sono accessibili pubblicamente.

**Codice Problematico:**
```typescript
// server/routes.ts
app.use('/uploads', express.static(join(process.cwd(), 'uploads')));
```

**Esempi Attacco:**
```
GET /uploads/verbali/verbale_BC074_VDL_020_20250617.pdf
GET /uploads/photos/photo_1752657228412_azz1tlfp8.jpg
```

**Impatto:**
- Documenti sensibili esposti
- Violazioni GDPR
- Informazioni riservate accessibili

**CVSS:** 8.7 (Alto)

---

## 🔴 **CRITICO - Path Traversal**

**Problema:** Nessuna sanitizzazione dei nomi file nel download.

**Codice Problematico:**
```typescript
app.get('/api/download-pdf/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = join(process.cwd(), 'uploads', 'verbali', filename);
  // NESSUNA SANITIZZAZIONE
```

**Vettore Attacco:**
```
GET /api/download-pdf/../../../etc/passwd
GET /api/download-pdf/../../../.env
```

**Impatto:**
- Accesso file di sistema
- Compromissione credenziali database
- Compromissione completa sistema

**CVSS:** 9.3 (Critico)

---

## 🔴 **ALTO - Validazione File Insufficiente**

**Problema:** Validazione basata solo su MIME type (facilmente falsificabile).

**Codice Problematico:**
```typescript
fileFilter: (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(null, false);
  }
}
```

**Debolezze:**
- Nessuna verifica magic numbers
- Nessuna scansione contenuto
- MIME type facilmente bypassabile

**CVSS:** 7.8 (Alto)

---

## 🟡 **PROBLEMI MEDI**

### **Limiti File Eccessivi**
- Immagini: 5MB (troppo permissivo)
- PDF: 10MB (molto alto)
- JSON: 50MB (eccessivo)

### **Nessun Rate Limiting**
- Nessuna protezione contro upload rapidi successivi
- Possibili attacchi DoS

### **Informazioni Sensibili negli Errori**
- Messaggi di errore rivelano informazioni di sistema

---

## 🔴 **SOLUZIONI IMMEDIATE**

### **1. PRIORITÀ MASSIMA - Migrazione Object Storage**

```bash
# Installare Replit Object Storage
npm install @replit/object-storage
```

```typescript
// Sostituire filesystem locale con cloud storage
import { ObjectStorage } from '@replit/object-storage';

const storage = new ObjectStorage();

app.post('/api/upload-photo', requireAuth, upload.single('photo'), async (req, res) => {
  const processedImage = await sharp(req.file.buffer)
    .rotate()
    .resize({ width: 800, height: 800, fit: 'inside' })
    .jpeg({ quality: 50 })
    .toBuffer();
  
  const filename = generateSecureFilename(req.file.originalname);
  await storage.uploadFile('photos/sopralluoghi/', processedImage, filename);
  
  res.json({ filename });
});
```

**Piano Migrazione:**
1. Setup bucket Object Storage su Replit
2. Migrazione 803MB file esistenti
3. Aggiornamento path database
4. Test completo funzionalità
5. Deploy senza perdita dati

### **2. Implementare Autenticazione**

```typescript
const requireAuth = async (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token || !await validateToken(token)) {
    return res.status(401).json({ message: 'Non autorizzato' });
  }
  next();
};

// Applicare a tutti gli endpoint upload
app.post('/api/upload-photo', requireAuth, upload.single('photo'), ...);
```

### **3. Rimuovere Accesso Pubblico**

```typescript
// RIMUOVERE questa riga
// app.use('/uploads', express.static(join(process.cwd(), 'uploads')));

// Sostituire con endpoint protetto
app.get('/api/files/:type/:filename', requireAuth, authorizeFileAccess, serveFile);
```

### **4. Sanitizzare Path File**

```typescript
const sanitizeFilename = (filename) => {
  const sanitized = filename.replace(/[^a-zA-Z0-9._-]/g, '');
  if (sanitized !== filename) {
    throw new Error('Nome file non valido');
  }
  return sanitized;
};
```

### **5. Validazione File Rigorosa**

```typescript
const validateFile = async (file) => {
  // Controllo estensioni permesse
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf'];
  const ext = path.extname(file.originalname).toLowerCase();
  if (!allowedExtensions.includes(ext)) {
    throw new Error('Tipo file non permesso');
  }
  
  // Verifica magic numbers
  const magicNumbers = {
    'jpg': [0xFF, 0xD8, 0xFF],
    'png': [0x89, 0x50, 0x4E, 0x47],
    'pdf': [0x25, 0x50, 0x44, 0x46]
  };
  
  // Implementare verifica magic numbers
};
```

---

## 🟡 **SOLUZIONI A BREVE TERMINE**

### **Rate Limiting**
```typescript
const rateLimit = require('express-rate-limit');

const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minuti
  max: 10, // max 10 upload per IP
  message: 'Troppi upload, riprova più tardi'
});
```

### **Nomi File Sicuri**
```typescript
const crypto = require('crypto');

const generateSecureFilename = (originalName) => {
  const ext = path.extname(originalName);
  const randomName = crypto.randomBytes(32).toString('hex');
  return `${randomName}${ext}`;
};
```

### **Security Headers**
```typescript
const helmet = require('helmet');
app.use(helmet());
```

---

## 📋 **CONFIGURAZIONE SICURA**

### **Variabili Ambiente**
```env
UPLOAD_MAX_SIZE=2097152          # 2MB max
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf
JWT_SECRET=<strong-random-secret>
UPLOAD_RATE_LIMIT=10
```

---

## 🎯 **CONCLUSIONI**

### **Azioni Prioritarie:**
1. **IMMEDIATO:** Migrazione Object Storage (perdita dati)
2. **IMMEDIATO:** Implementare autenticazione upload
3. **IMMEDIATO:** Rimuovere accesso pubblico file
4. **IMMEDIATO:** Correggere path traversal
5. **URGENTE:** Validazione file rigorosa

### **Impatto Business:**
- **Alto rischio** violazione dati
- **Violazioni GDPR**
- **Danno reputazionale**
- **Responsabilità legale**

### **Raccomandazione:**
**SOSPENDERE funzionalità upload** fino all'implementazione dei controlli di sicurezza critici. Il sistema attuale presenta rischi inaccettabili per i dati sensibili di costruzione e le operazioni aziendali.

---

**Classificazione:** RISERVATO  
**Distribuzione:** Team IT Security, Team Sviluppo, Management  
**Prossima Revisione:** 30 giorni dopo implementazione correzioni