# Analisi Critica Sistema Gestione Cantieri Orbyta

**Data:** 17 Luglio 2025  
**Urgenza:** 🔴 CRITICA  
**Sistema:** Applicazione Gestione Cantieri e Direzione Lavori

---

## 🚨 PROBLEMI CRITICI IDENTIFICATI

### 1. **🔴 PERDITA DATI AD OGNI DEPLOYMENT**

**Problema Principale:**
- I file caricati dagli utenti in produzione (foto, verbali, loghi) vengono persi ad ogni nuovo deployment
- Secondo la documentazione Replit: "Avoid saving and relying on data written to a deployed app's filesystem"
- Ogni deployment crea uno snapshot pulito del workspace, sovrascrivendo il container precedente

**Dati a Rischio:**
- Foto dei sopralluoghi di cantiere
- Verbali PDF finalizzati e firmati
- Loghi aziendali personalizzati per commesse
- Documentazione tecnica caricata

**Impatto Operativo:**
- Perdita di continuità lavorativa
- Necessità di ricaricare tutta la documentazione
- Interruzione del flusso di lavoro consolidato

### 2. **🔴 SISTEMA DI "BACKUP" INEFFICACE**

**Limitazioni Attuali:**
```typescript
// File server/backup.ts - NON preserva i file reali
export function verifyFileIntegrity() {
  // Conta solo i file esistenti ma non li salva
  const files = fs.readdirSync(dir).filter(f => !f.startsWith('.'));
  totalFiles += files.length;
}

export function createBackupMetadata() {
  // Crea solo un elenco JSON, i file fisici si perdono comunque
  fs.writeFileSync('uploads/backup-metadata.json', JSON.stringify(backupInfo, null, 2));
}
```

**Problema:** 
- I file `.gitkeep` mantengono solo le directory vuote
- Il sistema conta e elenca i file ma non li preserva fisicamente
- Il metadata backup è solo informativo, non funzionale

### 3. **🔴 SICUREZZA INSUFFICIENTE**

**Accesso Non Autenticato:**
- Endpoint upload aperti: `/api/upload-photo`, `/api/upload-logo`
- Nessuna validazione dell'utente
- Possibilità di upload non autorizzati

**Accesso Pubblico ai File:**
- Tutti i file in `uploads/` sono serviti staticamente
- Documenti sensibili accessibili via URL diretto
- Esempio: `/uploads/verbali/verbale_BC074_VDL_020_20250617.pdf`

**Validazione File Insufficiente:**
- Controllo solo su MIME type (facilmente falsificabile)
- Nessuna scansione contenuto file
- Rischio upload di file dannosi

### 4. **🔴 MODALITÀ OFFLINE FRAMMENTATA**

**Doppio Sistema Conflittuale:**
- Service Worker + IndexedDB per cache generica
- localStorage diretto per verbali specifici
- Possibili inconsistenze e conflitti di sincronizzazione

**Limitazioni Tecniche:**
- localStorage limitato a ~5-10MB
- Gestione foto in base64 inefficiente
- Complessità di debug e manutenzione

---

## 💡 SOLUZIONI PROPOSTE

### **PRIORITÀ 1: Migrazione a Object Storage**

**Implementazione Replit Object Storage:**
```bash
npm install @replit/object-storage
```

**Vantaggi:**
- Persistenza garantita tra deployment
- Scalabilità illimitata
- Integrazione nativa con Replit
- Backup automatico cloud

**Struttura Proposta:**
```
orbyta-cantieri-storage/
├── photos/
│   ├── sopralluoghi/
│   └── verbali/
├── verbali/
│   ├── direzione-lavori/
│   └── sicurezza/
└── logos/
    └── commesse/
```

### **PRIORITÀ 2: Unificazione Sistema Offline**

**Migrazione a IndexedDB Unico:**
- Eliminazione localStorage 
- Gestione unificata di tutti i dati offline
- Capacità superiore (~250MB-2GB)
- Supporto transazioni ACID
- Gestione nativa di oggetti complessi e Blob

### **PRIORITÀ 3: Implementazione Sicurezza**

**Autenticazione Endpoint:**
```typescript
const requireAuth = (req, res, next) => {
  if (!req.session?.user) {
    return res.status(401).json({message: 'Accesso non autorizzato'});
  }
  next();
};

app.post('/api/upload-photo', requireAuth, upload.single('photo'), handleUpload);
```

**Controllo Accesso File:**
```typescript
// Rimuovere accesso statico pubblico
// app.use('/uploads', express.static(...)); // ❌

// Sostituire con endpoint protetto
app.get('/api/file/:id', requireAuth, validateAccess, serveFile); // ✅
```

---

## 📋 PIANO DI IMPLEMENTAZIONE

### **Fase 1: Setup Object Storage (2 giorni)**
- [ ] Configurazione bucket Replit Object Storage
- [ ] Installazione SDK e configurazione credenziali
- [ ] Test upload/download base

### **Fase 2: Migrazione Backend (3 giorni)**
- [ ] Refactor endpoint upload per Object Storage
- [ ] Aggiornamento generazione PDF con URL cloud
- [ ] Migrazione file esistenti dal filesystem locale

### **Fase 3: Unificazione Offline (3 giorni)**
- [ ] Implementazione IndexedDB Manager unificato
- [ ] Migrazione da localStorage a IndexedDB
- [ ] Eliminazione doppio sistema

### **Fase 4: Sicurezza (2 giorni)**
- [ ] Implementazione autenticazione base
- [ ] Controllo accesso ai file
- [ ] Validazione upload migliorata

### **Fase 5: Testing e Deploy (2 giorni)**
- [ ] Test completo funzionalità
- [ ] Verifica persistenza file post-deployment
- [ ] Deploy production sicuro

**TOTALE: 12 giorni lavorativi**

---

## ⚠️ RACCOMANDAZIONI IMMEDIATE

### **NON Fare Deployment Production**
Fino alla risoluzione dei problemi critici, evitare deployment in produzione per prevenire perdite dati.

### **Backup Manuale**
Creare backup manuale dei file critici presenti nel workspace di sviluppo.

### **Comunicazione Stakeholder**
Informare il team delle limitazioni attuali e della timeline di risoluzione.

---

## 📊 PRIORITÀ E RISCHI

| Problema | Rischio | Priorità | Soluzione |
|----------|---------|----------|-----------|
| Perdita file deployment | Alto | 🔴 Critica | Object Storage |
| Sicurezza accesso | Alto | 🔴 Critica | Autenticazione |
| Sistema offline frammentato | Medio | 🟡 Alta | IndexedDB unificato |
| Backup inefficace | Medio | 🟡 Alta | Storage cloud |

---

**Note:** 
- Sistema attualmente NON adatto per ambiente di produzione
- Necessaria implementazione completa delle soluzioni prima del go-live
- Monitoraggio continuo post-implementazione richiesto

**Contatto:** Team Sviluppo  
**Prossimo Aggiornamento:** Al completamento Fase 1