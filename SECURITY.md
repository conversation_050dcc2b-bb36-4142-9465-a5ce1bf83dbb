# SECURITY.md

## Sicurezza del Sistema - Orbyta Engineering SRL

Questo documento descrive le misure di sicurezza implementate nel sistema di gestione cantieri.

## 🔒 Autenticazione e Autorizzazione

### JWT (JSON Web Tokens)
- **Implementazione**: <PERSON><PERSON> gli endpoint sensibili protetti con middleware `authenticateToken`
- **Token Location**: Headers `Authorization: Bearer <token>`
- **Secret**: Configurabile via `JWT_SECRET` environment variable
- **Scadenza**: 24 ore (auto-logout per sicurezza)

### Password Security
- **Hashing**: bcryptjs con salt automatico
- **Storage**: Mai salvate in plaintext nel database
- **Validazione**: Input validation con Zod schemas

### Endpoint Protection
```typescript
// PROTETTI (richiedono JWT):
/api/cantieri/*          // Gestione cantieri
/api/direzione-lavori/*  // Progetti e verbali
/api/tecnici/*           // Dati personali tecnici
/api/sopralluoghi/*      // Verbali sicurezza
/api/files/*             // Download documenti
/api/download-pdf/*      // Download PDF
/api/loghi/*             // Gestione loghi aziendali

// NON PROTETTI (pubblici):
/api/login               // Autenticazione
/api/register            // Registrazione
/api/test-*              // Endpoint di test (solo status)
```

## 🛡️ Protezione da Attacchi

### Rate Limiting
```typescript
// Upload files: 10 richieste/15 minuti per IP
uploadLimiter: 10 req/15min

// API generali: 100 richieste/15 minuti per IP  
generalLimiter: 100 req/15min
```

### Helmet Security Headers
- **XSS Protection**: Previene script injection
- **Content Type Options**: Previene MIME sniffing
- **Frame Options**: Protezione clickjacking
- **HSTS**: Forza connessioni HTTPS
- **Content Security Policy**: Configurato per sicurezza

### File Upload Security
```typescript
// Validazione Magic Numbers (non solo estensioni)
validateFileType(buffer, 'image'|'pdf')

// Nomi file sicuri con crypto random
generateSecureFilename(originalName) 

// Limiti dimensioni
maxFileSize: 10MB per immagini, 50MB per PDF

// Tipi accettati
Images: JPG, PNG, GIF
Documents: PDF only
```

### Input Validation
- **Zod Schemas**: Validazione rigorosa di tutti gli input
- **SQL Injection**: Prevenuta da Drizzle ORM parametrizzato
- **XSS**: Sanitizzazione input e CSP headers

## 🗃️ Protezione Dati nel Database

### Database Security
- **PostgreSQL**: Database enterprise-grade con sicurezza integrata
- **Connection**: Connessione sicura via `DATABASE_URL` crittografata
- **ORM**: Drizzle ORM previene SQL injection automaticamente
- **Backup**: Sistema automatico backup file durante deployment

### Dati Sensibili
```sql
-- Password sempre hashate
users.password_hash (bcrypt + salt)

-- Dati aziendali protetti da autenticazione
cantieri, direzione_lavori, verbali, tecnici

-- File storage sicuro in database
loghi_aziendali.contenuto (base64)
verbali.pdf_contenuto (base64)
```

### Data Minimization
- Solo dati necessari per funzionalità business
- Nessun tracking o analytics invasivo
- Logo e PDF in database (non filesystem esposto)

## 🚀 Deployment Sicuro

### Replit Private Deployment - OBBLIGATORIO
```bash
# ⚠️ PRIVATE DEPLOYMENT RICHIESTO
# Il progetto contiene dati aziendali sensibili di Orbyta Engineering SRL

DEPLOYMENT TYPE: Private Deployment
URL: https://docs.replit.com/cloud-services/deployments/private-deployments

CONFIGURAZIONE RICHIESTA:
1. Replit Core Plan o superiore
2. Private Deployment abilitato
3. Accesso limitato via whitelist IP/utenti
4. NO deployment pubblico
5. Custom domain opzionale per sicurezza aggiuntiva

VANTAGGI PRIVATE DEPLOYMENT:
- URL non pubblico/indicizzabile
- Controllo accessi granulare  
- Rete privata isolata
- Sicurezza enterprise-grade
```

### Environment Variables
```env
# Variabili richieste per sicurezza
DATABASE_URL=postgresql://...     # PostgreSQL Neon connection
JWT_SECRET=secure_random_key      # Cambiare in produzione
NODE_ENV=production               # Per ottimizzazioni sicurezza
```

### Server Configuration
- **Port**: 5000 (configurabile)
- **CORS**: Configurato per domini specifici
- **SSL/TLS**: Gestito da Replit automaticamente
- **Process Management**: Auto-restart su crash

## 🔍 Monitoraggio e Logging

### Security Logging
```typescript
// Eventi loggati per sicurezza
- Failed login attempts
- File upload attempts  
- Rate limit violations
- Authentication failures
- Database connection issues
```

### Error Handling
- **Sensitive Data**: Mai esposti in errori client
- **Stack Traces**: Solo in development mode
- **Generic Messages**: "Errore interno" per client

## 📋 Compliance e Privacy

### Dati Gestiti
- **Dati Aziendali**: Informazioni cantieri, progetti, verbali
- **Dati Personali**: Nome/cognome tecnici (business necessity)
- **File**: Foto cantieri, PDF verbali, loghi aziendali

### Privacy by Design
- **No Tracking**: Nessun analytics o tracking utenti
- **No Cookies**: Solo session management essenziale
- **Data Retention**: Solo dati necessari per business
- **No Third-Party**: Nessun servizio esterno per dati sensibili

### Note Legali
```
⚠️ IMPORTANTE:
Il sistema non implementa cookie policy o privacy policy 
in quanto utilizzato internamente da Orbyta Engineering SRL
per gestione cantieri aziendali.

Per deployment esterno o accesso clienti, valutare:
- Cookie Policy
- Privacy Policy  
- GDPR Compliance
- Terms of Service
```

## 🛠️ Manutenzione Sicurezza

### Updates Regolari
```bash
# Aggiornare dipendenze sicurezza
npm audit fix
npm update

# Verificare vulnerabilità
npm audit
```

### Security Checklist
- [ ] JWT secret cambiato in produzione
- [ ] Database URL sicura e privata
- [ ] Rate limiting configurato
- [ ] File upload limits configurati
- [ ] Helmet headers attivi
- [ ] HTTPS attivo (Replit auto)
- [ ] Repository privato
- [ ] Backup database configurato

---

**Ultimo aggiornamento**: Gennaio 2025  
**Responsabile Sicurezza**: Claude Code Assistant  
**Contatti**: Via issue GitHub per security concerns