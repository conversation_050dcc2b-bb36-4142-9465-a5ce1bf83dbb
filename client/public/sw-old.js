// Service Worker per funzionalità offline
const CACHE_NAME = 'orbyta-cantieri-v1';
const API_CACHE_NAME = 'orbyta-api-v1';

// Risorse da cachare per il funzionamento offline
const STATIC_CACHE_URLS = [
  '/',
  '/manifest.json',
  '/cantieri',
  '/direzione-lavori',
  '/impostazioni'
];

// Pattern API da cachare
const API_PATTERNS = [
  '/api/cantieri',
  '/api/tecnici', 
  '/api/direzione-lavori',
  '/api/direttori-lavori'
];

// Install event - cache delle risorse statiche
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static resources');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - pulizia cache vecchie
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - strategia di cache
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Strategia per API calls
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(event.request));
  }
  // Strategia per risorse statiche
  else if (event.request.method === 'GET') {
    event.respondWith(handleStaticRequest(event.request));
  }
});

// Gestione richieste API con cache intelligente
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  // Per richieste GET, usa cache-first con network fallback
  if (request.method === 'GET') {
    try {
      // Prova prima la rete
      const networkResponse = await fetch(request);
      
      if (networkResponse.ok) {
        // Cache la risposta se successful
        const cache = await caches.open(API_CACHE_NAME);
        cache.put(request, networkResponse.clone());
        return networkResponse;
      }
      
      // Se rete fallisce, usa cache
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        console.log('Serving API from cache (network failed):', url.pathname);
        return cachedResponse;
      }
      
      // Se non c'è cache, ritorna errore rete
      return networkResponse;
    } catch (error) {
      // Errore di rete, usa cache
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        console.log('Serving API from cache (offline):', url.pathname);
        return cachedResponse;
      }
      
      // Nessuna cache disponibile, ritorna errore offline
      return new Response(
        JSON.stringify({ 
          error: 'Offline - Nessun dato in cache disponibile',
          offline: true 
        }),
        {
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  }
  
  // Per richieste POST/PUT/DELETE, prova sempre la rete
  else {
    try {
      console.log('Attempting network request for:', url.pathname, request.method);
      return await fetch(request);
    } catch (error) {
      console.log('Network request failed, going offline for:', url.pathname, error.message);
      
      // Salva sempre l'azione per sync successivo
      await saveOfflineAction(request);
      
      // Crea un ID temporaneo per il verbale offline
      const tempId = Date.now();
      const offlineResponse = {
        id: tempId,
        offline: true,
        queued: true,
        status: 'bozza',
        message: 'Verbale salvato offline - sarà sincronizzato quando torni online'
      };
      
      console.log('Returning offline response:', offlineResponse);
      
      return new Response(
        JSON.stringify(offlineResponse),
        {
          status: 201, // Usa 201 Created per simulare successo
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  }
}

// Gestione risorse statiche
async function handleStaticRequest(request) {
  try {
    // Network first per l'HTML principale
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    
    // Se rete fallisce, usa cache
    const cachedResponse = await caches.match(request);
    return cachedResponse || networkResponse;
  } catch (error) {
    // Offline - usa cache
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Se non c'è cache per la risorsa, ritorna la home page
    if (request.mode === 'navigate') {
      const homeCache = await caches.match('/');
      return homeCache || new Response('Offline - Pagina non disponibile', { status: 503 });
    }
    
    return new Response('Risorsa non disponibile offline', { status: 503 });
  }
}

// Salva azioni offline per sync successivo
async function saveOfflineAction(request) {
  try {
    const action = {
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      body: request.method !== 'GET' ? await request.clone().text() : null,
      timestamp: Date.now()
    };
    
    // Salva in IndexedDB per persistenza
    const db = await openDB();
    const tx = db.transaction(['offline_actions'], 'readwrite');
    await tx.objectStore('offline_actions').add(action);
    await tx.done;
    
    console.log('Offline action saved:', action);
  } catch (error) {
    console.error('Error saving offline action:', error);
  }
}

// Apri IndexedDB per storage offline
function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('OrbyteOfflineDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      // Store per azioni offline
      if (!db.objectStoreNames.contains('offline_actions')) {
        const store = db.createObjectStore('offline_actions', { keyPath: 'id', autoIncrement: true });
        store.createIndex('timestamp', 'timestamp');
      }
    };
  });
}

// Background sync quando torna online
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-offline-actions') {
    event.waitUntil(syncOfflineActions());
  }
});

// Sincronizza azioni offline
async function syncOfflineActions() {
  try {
    const db = await openDB();
    const tx = db.transaction(['offline_actions'], 'readonly');
    const actions = await tx.objectStore('offline_actions').getAll();
    
    for (const action of actions) {
      try {
        const request = new Request(action.url, {
          method: action.method,
          headers: action.headers,
          body: action.body
        });
        
        const response = await fetch(request);
        
        if (response.ok) {
          // Azione sincronizzata con successo, rimuovi dalla queue
          const deleteTx = db.transaction(['offline_actions'], 'readwrite');
          await deleteTx.objectStore('offline_actions').delete(action.id);
          console.log('Offline action synced:', action);
        }
      } catch (error) {
        console.error('Error syncing offline action:', error);
        // Mantieni l'azione nella queue per retry successivo
      }
    }
  } catch (error) {
    console.error('Error during sync:', error);
  }
}