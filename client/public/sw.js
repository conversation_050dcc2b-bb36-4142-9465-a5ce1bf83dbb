// Service Worker Semplificato - Orbyte Engineering
// Gestisce solo cache statica, API cache delegata a IndexedDB

const CACHE_NAME = `orbyte-engineering-cantieri-${new Date().getTime()}`;

// Gestisce messaggi dal client per skip waiting
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
const STATIC_CACHE_URLS = [
  '/',
  '/manifest.json',
  '/cantieri',
  '/direzione-lavori',
  '/impostazioni',
  '/favicon.ico'
];

// Install event - cache delle risorse statiche
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('💾 Caching static resources');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        console.log('✅ Static resources cached');
        self.skipWaiting();
      })
      .catch(error => {
        console.error('❌ Error caching static resources:', error);
      })
  );
});

// Activate event - pulizia cache vecchie
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker activating...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          // Rimuovi tutte le cache vecchie
          if (cacheName !== CACHE_NAME) {
            console.log('🗑️ Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('✅ Cache cleanup completed');
      self.clients.claim();
    })
  );
});

// Fetch event - strategia semplificata
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Ignora richieste non HTTP/HTTPS
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  // Per le API, lascia gestire a IndexedDB (nessuna cache)
  if (url.pathname.startsWith('/api/')) {
    return; // Passa direttamente alla rete
  }
  
  // Per risorse statiche, usa cache con network fallback
  if (event.request.method === 'GET') {
    event.respondWith(handleStaticRequest(event.request));
  }
});

// Gestione richieste statiche con cache-first
async function handleStaticRequest(request) {
  try {
    // Prova prima la cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      console.log('📦 Serving from cache:', request.url);
      return cachedResponse;
    }
    
    // Se non in cache, prova la rete
    const networkResponse = await fetch(request);
    
    // Se successful, cache la risposta per il futuro
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
      console.log('💾 Cached from network:', request.url);
    }
    
    return networkResponse;
    
  } catch (error) {
    console.error('❌ Error serving static request:', request.url, error);
    
    // Fallback per pagine principali se tutto fallisce
    if (request.destination === 'document') {
      try {
        const fallbackResponse = await caches.match('/');
        if (fallbackResponse) {
          return fallbackResponse;
        }
      } catch (fallbackError) {
        console.error('❌ Fallback also failed:', fallbackError);
      }
    }
    
    // Ritorna errore di rete se non c'è fallback
    throw error;
  }
}

// Background Sync per compatibilità (anche se ora delegato a IndexedDB)
self.addEventListener('sync', (event) => {
  console.log('🔄 Background sync triggered:', event.tag);
  
  if (event.tag === 'sync-offline-actions') {
    console.log('📤 Sync delegated to IndexedDB system');
    // IndexedDB gestisce la sincronizzazione
  }
});

// Message handler per comunicazione con main thread
self.addEventListener('message', (event) => {
  console.log('📨 Service Worker message:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_CACHE_INFO') {
    // Ritorna info sulla cache
    caches.keys().then(cacheNames => {
      event.ports[0].postMessage({
        type: 'CACHE_INFO',
        caches: cacheNames,
        current: CACHE_NAME
      });
    });
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    // Pulisci cache su richiesta
    caches.delete(CACHE_NAME).then(success => {
      event.ports[0].postMessage({
        type: 'CACHE_CLEARED',
        success
      });
    });
  }
});

// Notification handler (se supportato)
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 Notification clicked:', event.notification.tag);
  event.notification.close();
  
  // Apri o focalizza l'app
  event.waitUntil(
    clients.matchAll().then(clientList => {
      if (clientList.length > 0) {
        return clientList[0].focus();
      }
      return clients.openWindow('/');
    })
  );
});

console.log('✅ Service Worker loaded - Orbyte Engineering Cantieri v2');