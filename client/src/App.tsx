import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/contexts/AuthContext";
import AuthGuard from "@/components/AuthGuard";
import Navigation from "@/components/navigation";
import PWAUpdatePrompt from "@/components/pwa-update-prompt";
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import Cantieri from "@/pages/cantieri";
import Impostazioni from "@/pages/impostazioni";
import LogoManagement from "@/pages/logo-management";
import CantiereDettaglio from "@/pages/cantiere-dettaglio";
import DirezioneLavori from "@/pages/direzione-lavori";
import DirezioneLavoriDettaglio from "@/pages/direzione-lavori-dettaglio";
import VerbaleDirezioneLavoriCompilazione from "@/pages/verbale-direzione-lavori-compilazione";


function Router() {
  const [location] = useLocation();
  const showNavigation = location !== "/";

  return (
    <>
      {showNavigation && <Navigation />}
      <Switch>
        <Route path="/" component={Home} />
        <Route path="/cantieri" component={Cantieri} />
        <Route path="/cantieri/:id" component={CantiereDettaglio} />
        <Route path="/direzione-lavori" component={DirezioneLavori} />
        <Route path="/direzione-lavori/:id" component={DirezioneLavoriDettaglio} />
        <Route path="/direzione-lavori/:lavoroId/verbali/:verbaleId" component={VerbaleDirezioneLavoriCompilazione} />
        <Route path="/direzione-lavori/:lavoroId/verbali/new" component={VerbaleDirezioneLavoriCompilazione} />
        <Route path="/impostazioni" component={Impostazioni} />
        <Route path="/impostazioni/loghi" component={LogoManagement} />
        <Route component={NotFound} />
      </Switch>
    </>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <AuthGuard>
            <>
              <Router />
              <PWAUpdatePrompt />
            </>
          </AuthGuard>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
