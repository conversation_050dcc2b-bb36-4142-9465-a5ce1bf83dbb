import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { PenTool, X, RotateCcw } from "lucide-react";

interface FirmaDirezioneLavoriModalProps {
  isOpen: boolean;
  onClose: () => void;
  tecnico: { nome: string; cognome: string; } | undefined;
  personePresenti: { nome: string; qualifica: string; }[];
  onFirmeComplete: (firme: Record<string, string>) => void;
}

export default function FirmaDirezioneLavoriModal({ 
  isOpen, 
  onClose, 
  tecnico,
  personePresenti,
  onFirmeComplete
}: FirmaDirezioneLavoriModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [firme, setFirme] = useState<Record<string, string>>({});
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);

  // Crea l'array delle persone da firmare (solo persone presenti, escludendo il tecnico compilatore)
  const tutteLePersone = [
    ...personePresenti,
    ...(tecnico ? [{ nome: `${tecnico.nome} ${tecnico.cognome}`, qualifica: "Direttore Lavori Compilatore" }] : [])
  ];

  const currentPerson = tutteLePersone[currentStep] || null;

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0);
      setFirme({});
    }
  }, [isOpen]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
      }
    }
  }, [currentStep, isOpen]);

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const rect = canvas.getBoundingClientRect();
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;
        let x, y;
        
        if ('touches' in e) {
          x = (e.touches[0].clientX - rect.left) * scaleX;
          y = (e.touches[0].clientY - rect.top) * scaleY;
        } else {
          x = (e.clientX - rect.left) * scaleX;
          y = (e.clientY - rect.top) * scaleY;
        }
        
        ctx.beginPath();
        ctx.moveTo(x, y);
      }
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    
    e.preventDefault();
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const rect = canvas.getBoundingClientRect();
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;
        let x, y;
        
        if ('touches' in e) {
          x = (e.touches[0].clientX - rect.left) * scaleX;
          y = (e.touches[0].clientY - rect.top) * scaleY;
        } else {
          x = (e.clientX - rect.left) * scaleX;
          y = (e.clientY - rect.top) * scaleY;
        }
        
        ctx.lineTo(x, y);
        ctx.stroke();
      }
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const saveSignature = () => {
    const canvas = canvasRef.current;
    if (canvas && currentPerson) {
      const signatureData = canvas.toDataURL('image/png');
      setFirme(prev => ({
        ...prev,
        [currentPerson.nome]: signatureData
      }));

      if (currentStep < tutteLePersone.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        // Tutte le firme sono state raccolte
        const allSignatures = {
          ...firme,
          [currentPerson.nome]: signatureData
        };
        onFirmeComplete(allSignatures);
        handleClose();
      }
    }
  };

  const handleClose = () => {
    setCurrentStep(0);
    setFirme({});
    onClose();
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!currentPerson) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-left">
            Firma Verbale - Step {currentStep + 1} di {tutteLePersone.length}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-1">Firma di: {currentPerson.nome}</h3>
            <p className="text-sm text-gray-600 mb-4">Per favore, firma nel riquadro sottostante utilizzando il mouse o il touch screen.</p>
            
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6">
              <canvas
                ref={canvasRef}
                width={450}
                height={200}
                className="bg-white border border-gray-200 rounded w-full cursor-crosshair touch-none"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={stopDrawing}
                onMouseLeave={stopDrawing}
                onTouchStart={startDrawing}
                onTouchMove={draw}
                onTouchEnd={stopDrawing}
                style={{ touchAction: 'none' }}
              />
            </div>
          </div>

          <div className="flex justify-between items-center pt-4">
            <Button
              variant="outline"
              onClick={clearCanvas}
              className="flex items-center space-x-2"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Cancella Firma</span>
            </Button>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleClose}
              >
                Annulla
              </Button>
              <Button
                onClick={saveSignature}
                className="bg-blue-600 hover:bg-blue-700 text-white flex items-center space-x-2"
              >
                <PenTool className="h-4 w-4" />
                <span>Conferma Firma</span>
              </Button>
            </div>
          </div>

          {/* Progresso firme */}
          <div className="pt-4 border-t">
            <p className="text-sm font-medium mb-3">Progresso firme:</p>
            <div className="space-y-2">
              {tutteLePersone.map((persona, index) => (
                <div key={`${persona.nome}-${index}`} className="flex items-center space-x-3">
                  <div className={`w-4 h-4 rounded-full flex-shrink-0 ${
                    index < currentStep ? 'bg-green-500' : 
                    index === currentStep ? 'bg-blue-500' : 
                    'bg-gray-300'
                  }`} />
                  <span className={`text-sm ${
                    index === currentStep ? 'font-semibold text-blue-600' : 
                    index < currentStep ? 'text-green-600' : 
                    'text-gray-500'
                  }`}>
                    {persona.nome}
                    {index === currentStep && ' - In corso'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}