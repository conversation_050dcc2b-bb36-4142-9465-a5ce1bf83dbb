import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import type { DirezioneLavori, Tecnico, InsertVerbaleDirezioneLavori, VerbaleDirezioneLavori } from "@shared/schema";

interface NuovoVerbaleDirezioneModalProps {
  isOpen: boolean;
  onClose: () => void;
  lavoro: DirezioneLavori | null;
  onDataCollected?: (verbaleData: any) => void;
}

export default function NuovoVerbaleDirezioneModal({ isOpen, onClose, lavoro, onDataCollected }: NuovoVerbaleDirezioneModalProps) {
  const [data, setData] = useState(new Date().toISOString().split('T')[0]);
  const [direttoreId, setDirettoreId] = useState("");
  const [note, setNote] = useState("");
  const [lavorazioniInCorso, setLavorazioniInCorso] = useState("");
  
  const { toast } = useToast();

  const { data: direttori = [], isLoading: isLoadingDirettori } = useQuery({
    queryKey: [`/api/direzione-lavori/${lavoro?.id}/direttori`],
    enabled: isOpen && !!lavoro?.id,
  });

  const { data: verbali = [] } = useQuery({
    queryKey: [`/api/direzione-lavori/${lavoro?.id}/verbali`],
    enabled: isOpen && !!lavoro?.id,
  });

  // No mutation here - just collect data and pass to people present modal

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!direttoreId || !data || !lavorazioniInCorso.trim()) {
      toast({
        title: "Errore",
        description: "Tutti i campi obbligatori devono essere compilati.",
        variant: "destructive",
      });
      return;
    }

    if (!lavoro) return;

    const verbaleData = {
      direzioneLavoriId: lavoro.id,
      tecnicoId: parseInt(direttoreId),
      data,
      numeroProgressivo: generateNumeroSopralluogo(),
      lavorazioniInCorso: lavorazioniInCorso.trim() || null,
      note: note.trim() || null,
      checklist: '[]',
    };

    // Pass data to parent instead of creating verbale immediately
    if (onDataCollected) {
      onDataCollected(verbaleData);
    }
    onClose();
  };

  const resetForm = () => {
    setData(new Date().toISOString().split('T')[0]);
    setDirettoreId("");
    setNote("");
    setLavorazioniInCorso("");
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Genera il numero automaticamente: codiceCommessa-SOP-DL-YYYYMMDD
  const generateNumeroSopralluogo = () => {
    if (!lavoro || !data) return "";
    const formattedDate = data.replace(/-/g, '');
    
    // Get next progressive number for this commessa
    const verbaliCount = verbali.filter(v => v.stato === 'finalizzato').length;
    const nextNumber = verbaliCount + 1;
    
    // Special case for BC074 starting from 020
    const progressiveNumber = lavoro.codiceCommessa === 'BC074' 
      ? String(19 + nextNumber).padStart(3, '0')
      : String(nextNumber).padStart(3, '0');
    
    return `${lavoro.codiceCommessa}-VDL-${progressiveNumber}-${formattedDate}`;
  };

  if (!lavoro) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] p-6">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-semibold">
            Nuovo Sopralluogo - {lavoro.codiceCommessa}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Prima riga: Numero e Data */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="numeroSopralluogo" className="text-sm font-medium">
                Numero Sopralluogo *
              </Label>
              <Input
                id="numeroSopralluogo"
                value={generateNumeroSopralluogo()}
                disabled
                className="bg-gray-50 text-gray-700 border-gray-200"
              />
              <p className="text-xs text-gray-500">Generato automaticamente dalla data</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="data" className="text-sm font-medium">
                Data Sopralluogo *
              </Label>
              <Input
                id="data"
                type="date"
                value={data}
                onChange={(e) => setData(e.target.value)}
                required
                className="border-gray-200"
              />
            </div>
          </div>

          {/* Direttore Compilatore */}
          <div className="space-y-2">
            <Label htmlFor="direttore" className="text-sm font-medium">
              Direttore Compilatore *
            </Label>
            <Select value={direttoreId} onValueChange={setDirettoreId} required>
              <SelectTrigger className="border-gray-200">
                <SelectValue placeholder="Seleziona un direttore" />
              </SelectTrigger>
              <SelectContent>
                {isLoadingDirettori ? (
                  <SelectItem value="loading" disabled>Caricamento...</SelectItem>
                ) : direttori.length === 0 ? (
                  <SelectItem value="empty" disabled>Nessun direttore disponibile</SelectItem>
                ) : (
                  direttori.map((direttore: any) => (
                    <SelectItem key={direttore.id} value={direttore.id.toString()}>
                      {direttore.nome} {direttore.cognome} - {direttore.qualifica}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Note Generali */}
          <div className="space-y-2">
            <Label htmlFor="note" className="text-sm font-medium">
              Note Generali
            </Label>
            <Textarea
              id="note"
              placeholder="Note aggiuntive sul sopralluogo..."
              value={note}
              onChange={(e) => setNote(e.target.value)}
              rows={4}
              className="border-gray-200 resize-none"
            />
          </div>

          {/* Lavorazioni in Corso */}
          <div className="space-y-2">
            <Label htmlFor="lavorazioni" className="text-sm font-medium">
              Lavorazioni in Corso *
            </Label>
            <Textarea
              id="lavorazioni"
              placeholder="Descrivi le lavorazioni attualmente in corso nel cantiere..."
              value={lavorazioniInCorso}
              onChange={(e) => setLavorazioniInCorso(e.target.value)}
              rows={4}
              required
              className="border-gray-200 resize-none"
            />
          </div>

          {/* Bottoni */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleClose}
              className="px-6"
            >
              Annulla
            </Button>
            <Button 
              type="submit" 
              className="bg-blue-600 hover:bg-blue-700 text-white px-6"
            >
              Procedi alle Persone Presenti
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}