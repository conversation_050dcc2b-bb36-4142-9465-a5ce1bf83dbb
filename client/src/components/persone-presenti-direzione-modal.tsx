import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useLocation } from "wouter";
import { Plus, Trash2, X } from "lucide-react";
import { isOnline } from "@/lib/offline-storage/";
import type { DirezioneLavori, VerbaleDirezioneLavori, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RubricaPersonaPresente } from "@shared/schema";

type PersonaPresente = {
  id: string;
  nome: string;
  qualifica: string;
  email?: string;
  telefono?: string;
};

interface PersonePresentiBirezioneLavoriModalProps {
  isOpen: boolean;
  onClose: () => void;
  lavoro: DirezioneLavori | null;
  verbale?: VerbaleDirezioneLavori | null;
  verbaleDataTemp?: any;
  onVerbaleCreated?: (verbaleData: any) => void;
  tecnicoCompilatore?: DirettoreLavori | null;
}

export default function PersonePresentiBirezioneLavoriModal({ 
  isOpen, 
  onClose, 
  lavoro, 
  verbale,
  verbaleDataTemp,
  onVerbaleCreated,
  tecnicoCompilatore
}: PersonePresentiBirezioneLavoriModalProps) {
  const [personePresenti, setPersonePresenti] = useState<PersonaPresente[]>([
    { id: "1", nome: "", qualifica: "", email: "", telefono: "" }
  ]);
  const [personeSelezionate, setPersoneSelezionate] = useState<Set<string>>(new Set());
  
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  // Initialize people present from existing data
  useEffect(() => {
    if (isOpen && verbaleDataTemp?.personePresenti) {
      try {
        const existingPeople = JSON.parse(verbaleDataTemp.personePresenti);
        if (existingPeople.length > 0) {
          setPersonePresenti(existingPeople);
        } else {
          setPersonePresenti([{ id: "1", nome: "", qualifica: "", email: "", telefono: "" }]);
        }
      } catch (error) {
        setPersonePresenti([{ id: "1", nome: "", qualifica: "", email: "", telefono: "" }]);
      }
    } else if (isOpen) {
      setPersonePresenti([{ id: "1", nome: "", qualifica: "", email: "", telefono: "" }]);
    }
  }, [isOpen, verbaleDataTemp?.personePresenti]);

  // Fetch directors for this commessa
  const { data: direttoriCommessa = [] } = useQuery<DirettoreLavori[]>({
    queryKey: [`/api/direzione-lavori/${lavoro?.id}/direttori`],
    enabled: !!lavoro?.id && isOpen,
  });

  // Fetch rubrica persone presenti for this commessa
  const { data: rubricaPersone = [] } = useQuery<RubricaPersonaPresente[]>({
    queryKey: [`/api/direzione-lavori/${lavoro?.id}/rubrica`],
    enabled: !!lavoro?.id && isOpen && isOnline(),
  });

  const saveMutation = useMutation({
    mutationFn: async () => {
      if (!lavoro) return;

      const validPersone = personePresenti.filter(p => p.nome.trim() && p.qualifica.trim());
      
      // For existing verbale (verbale prop exists), just return the people
      if (verbale) {
        return { personePresenti: validPersone };
      }
      
      // For new verbale, save to localStorage (always works offline/online)
      if (verbaleDataTemp) {
        const verbaleData = {
          ...verbaleDataTemp,
          personePresenti: JSON.stringify(validPersone)
        };

        localStorage.setItem(`verbale_temp_${lavoro.id}`, JSON.stringify(verbaleData));
        console.log('Persone presenti salvate in localStorage:', { lavoroId: lavoro.id, persone: validPersone });
        return { verbaleData, personePresenti: validPersone };
      }

      return { personePresenti: validPersone };
    },
    onSuccess: (result) => {
      const deviceOnline = navigator.onLine;
      const hookOnline = isOnline();
      
      console.log('Persone presenti salvate con successo:', { result, online: deviceOnline && hookOnline });
      
      toast({
        title: "Successo",
        description: verbale ? "Persone presenti aggiornate con successo" : 
                     (deviceOnline && hookOnline ? "Dati salvati, procedi alla firma" : "Dati salvati offline, procedi alla firma"),
      });
      
      if (onVerbaleCreated && result) {
        onVerbaleCreated(result.verbaleData || { personePresenti: JSON.stringify(result.personePresenti) });
      }
      
      onClose();
    },
    onError: (error) => {
      console.error("Error saving people:", error);
      toast({
        title: "Errore",
        description: verbale ? "Errore durante l'aggiornamento delle persone presenti" : "Errore durante la creazione del verbale",
        variant: "destructive",
      });
    },
  });

  const addPersona = () => {
    const newId = (personePresenti.length + 1).toString();
    setPersonePresenti([...personePresenti, { id: newId, nome: "", qualifica: "", email: "", telefono: "" }]);
  };

  const removePersona = (id: string) => {
    setPersonePresenti(personePresenti.filter(persona => persona.id !== id));
  };

  const updatePersona = (id: string, field: 'nome' | 'qualifica' | 'email' | 'telefono', value: string) => {
    setPersonePresenti(prevPersone => 
      prevPersone.map(persona => 
        persona.id === id ? { ...persona, [field]: value } : persona
      )
    );
  };

  const addPersonaFromRubrica = (personaRubrica: RubricaPersonaPresente) => {
    const newId = (personePresenti.length + 1).toString();
    setPersonePresenti([...personePresenti, { 
      id: newId, 
      nome: personaRubrica.nome, 
      qualifica: personaRubrica.qualifica,
      email: personaRubrica.email || "",
      telefono: personaRubrica.telefono || ""
    }]);
  };

  const handleSubmit = () => {
    // Non è più obbligatorio inserire persone presenti
    const deviceOnline = navigator.onLine;
    const hookOnline = isOnline();
    
    console.log('Persone presenti - Submit clicked:', { 
      online: deviceOnline && hookOnline, 
      personePresenti: personePresenti.length,
      hasVerbaleDataTemp: !!verbaleDataTemp,
      lavoroId: lavoro?.id
    });
    
    saveMutation.mutate();
  };

  const handleBack = () => {
    onClose();
  };

  if (!lavoro) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center justify-between">
            <div>
              <div className="text-xl font-semibold">
                {verbale ? "Modifica Persone Presenti" : `Nuovo Verbale - ${lavoro.codiceCommessa}`}
              </div>
              <div className="text-base text-blue-600 font-medium mt-1">Persone Presenti al Verbale</div>
              <div className="text-sm text-gray-600 mt-1">
                {verbale 
                  ? "Aggiorna l'elenco delle persone presenti al verbale"
                  : "Inserisci le persone presenti al verbale con nome completo e qualifica"
                }
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-6 w-6 rounded-full hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">
              Persone Presenti al Verbale
            </h3>
            <p className="text-sm text-blue-700">
              Inserisci eventuali persone presenti al verbale con nome completo e qualifica (opzionale)
            </p>
          </div>

          {/* Rubrica persone presenti */}
          {rubricaPersone.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-green-700">
                  Rubrica Persone Presenti - {lavoro.codiceCommessa} ({rubricaPersone.length} persone)
                </CardTitle>
                <p className="text-sm text-green-600 mt-1">
                  Clicca "Aggiungi" per inserire rapidamente persone già utilizzate nei verbali precedenti
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {rubricaPersone.map((persona) => (
                    <div key={persona.id} className="border rounded-lg p-4 bg-green-50 hover:bg-green-100 transition-colors">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div className="flex-1">
                          <h4 className="font-semibold text-green-800 mb-1">{persona.nome}</h4>
                          <p className="text-sm text-green-600 mb-2">{persona.qualifica}</p>
                          <div className="flex flex-wrap gap-4 text-xs text-green-500">
                            {persona.email && (
                              <span className="flex items-center gap-1">
                                <span>📧</span> {persona.email}
                              </span>
                            )}
                            {persona.telefono && (
                              <span className="flex items-center gap-1">
                                <span>📞</span> {persona.telefono}
                              </span>
                            )}
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="lg"
                          onClick={() => addPersonaFromRubrica(persona)}
                          className="bg-green-100 hover:bg-green-200 text-green-800 border-green-300 font-medium min-w-[120px] self-start sm:self-center"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Aggiungi
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Elenco Persone Presenti
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={addPersona}
                  className="ml-2"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Aggiungi Persona
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {personePresenti.map((persona, index) => (
                <div key={persona.id} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 p-4 border border-gray-200 rounded-lg">
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Seleziona o Inserisci Nome
                    </Label>
                    {direttoriCommessa.length > 0 ? (
                      <Select onValueChange={(value) => {
                        const newPersoneSelezionate = new Set(personeSelezionate);
                        if (value === "nuovo") {
                          updatePersona(persona.id, "nome", "");
                          updatePersona(persona.id, "qualifica", "");
                          updatePersona(persona.id, "email", "");
                          updatePersona(persona.id, "telefono", "");
                          newPersoneSelezionate.delete(persona.id);
                        } else {
                          const direttore = direttoriCommessa.find(d => d.id.toString() === value);
                          if (direttore) {
                            updatePersona(persona.id, "nome", `${direttore.nome} ${direttore.cognome}`);
                            updatePersona(persona.id, "qualifica", direttore.qualifica);
                            updatePersona(persona.id, "email", direttore.email || "");
                            updatePersona(persona.id, "telefono", direttore.telefono || "");
                            newPersoneSelezionate.add(persona.id);
                          }
                        }
                        setPersoneSelezionate(newPersoneSelezionate);
                      }}>
                        <SelectTrigger className="text-sm">
                          <SelectValue placeholder="Seleziona persona o inserisci nuovo" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="nuovo">Inserisci nuovo...</SelectItem>
                          {direttoriCommessa
                            .filter(direttore => {
                              // Escludi il tecnico compilatore dal dropdown
                              if (tecnicoCompilatore && direttore.id === tecnicoCompilatore.id) {
                                return false;
                              }
                              // Escludi direttori già selezionati in altre righe
                              return !personePresenti.some(p => 
                                p.id !== persona.id && 
                                p.nome === `${direttore.nome} ${direttore.cognome}` && 
                                p.qualifica === direttore.qualifica
                              );
                            })
                            .map((direttore) => (
                              <SelectItem key={direttore.id} value={direttore.id.toString()}>
                                {direttore.nome} {direttore.cognome} - {direttore.qualifica}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    ) : null}
                    <Input
                      placeholder="Es. Mario Rossi"
                      value={persona.nome}
                      onChange={(e) => updatePersona(persona.id, "nome", e.target.value)}
                      className="text-sm mt-2"
                      disabled={personeSelezionate.has(persona.id)}
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Qualifica
                    </Label>
                    <Input
                      placeholder="Es. Direttore Lavori, Operaio, ecc."
                      value={persona.qualifica}
                      onChange={(e) => updatePersona(persona.id, "qualifica", e.target.value)}
                      className="text-sm"
                      disabled={personeSelezionate.has(persona.id)}
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Email
                    </Label>
                    <Input
                      type="email"
                      placeholder="es. <EMAIL>"
                      value={persona.email || ""}
                      onChange={(e) => updatePersona(persona.id, "email", e.target.value)}
                      className="text-sm"
                      disabled={personeSelezionate.has(persona.id)}
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Telefono
                    </Label>
                    <Input
                      type="tel"
                      placeholder="es. +39 ************"
                      value={persona.telefono || ""}
                      onChange={(e) => updatePersona(persona.id, "telefono", e.target.value)}
                      className="text-sm"
                      disabled={personeSelezionate.has(persona.id)}
                    />
                  </div>
                  <div className="flex items-end gap-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removePersona(persona.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <div className="flex justify-between pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleBack}
            >
              Indietro
            </Button>
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Annulla
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={saveMutation.isPending}
              >
                {saveMutation.isPending ? "Salvataggio..." : 
                 verbale ? "Aggiorna Persone" : "Procedi alla Checklist"}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}