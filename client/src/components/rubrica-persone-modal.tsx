import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Plus, Trash2, X, UserPlus, Users } from "lucide-react";
import { isOnline } from "@/lib/offline-storage";
import type { DirezioneLavori, RubricaPersonaPresente } from "@shared/schema";

interface RubricaPersoneModalProps {
  isOpen: boolean;
  onClose: () => void;
  lavoro: DirezioneLavori;
}

export default function RubricaPersoneModal({ isOpen, onClose, lavoro }: RubricaPersoneModalProps) {
  const [nuovaPersona, setNuovaPersona] = useState({
    nome: "",
    qualifica: "",
    email: "",
    telefono: ""
  });
  
  const { toast } = useToast();

  // Fetch rubrica persone presenti
  const { data: rubricaPersone = [], refetch } = useQuery<RubricaPersonaPresente[]>({
    queryKey: [`/api/direzione-lavori/${lavoro.id}/rubrica`],
    enabled: isOpen && isOnline(),
  });

  // Mutation per aggiungere persona
  const aggiungiPersonaMutation = useMutation({
    mutationFn: async (persona: typeof nuovaPersona) => {
      return await apiRequest("POST", `/api/direzione-lavori/${lavoro.id}/rubrica`, persona);
    },
    onSuccess: () => {
      toast({
        title: "Successo",
        description: "Persona aggiunta alla rubrica",
      });
      setNuovaPersona({ nome: "", qualifica: "", email: "", telefono: "" });
      refetch();
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori", lavoro.id, "rubrica"] });
    },
    onError: (error) => {
      console.error("Error adding persona:", error);
      toast({
        title: "Errore",
        description: "Errore durante l'aggiunta della persona",
        variant: "destructive",
      });
    },
  });

  // Mutation per eliminare persona
  const eliminaPersonaMutation = useMutation({
    mutationFn: async (personaId: number) => {
      return await apiRequest("DELETE", `/api/direzione-lavori/${lavoro.id}/rubrica/${personaId}`, {});
    },
    onSuccess: () => {
      toast({
        title: "Successo",
        description: "Persona eliminata dalla rubrica",
      });
      refetch();
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori", lavoro.id, "rubrica"] });
    },
    onError: (error) => {
      console.error("Error deleting persona:", error);
      toast({
        title: "Errore",
        description: "Errore durante l'eliminazione della persona",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (nuovaPersona.nome.trim() && nuovaPersona.qualifica.trim()) {
      aggiungiPersonaMutation.mutate(nuovaPersona);
    }
  };

  const handleDelete = (personaId: number) => {
    if (confirm("Sei sicuro di voler eliminare questa persona dalla rubrica?")) {
      eliminaPersonaMutation.mutate(personaId);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center justify-between">
            <div>
              <div className="text-xl font-semibold flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Rubrica Persone Presenti - {lavoro.codiceCommessa}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                Gestisci la rubrica delle persone presenti per questa commessa
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-6 w-6 rounded-full hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Form aggiunta persona */}
          <Card>
            <CardHeader>
              <CardTitle className="text-green-700 flex items-center">
                <UserPlus className="h-4 w-4 mr-2" />
                Aggiungi Nuova Persona
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="nome">Nome e Cognome *</Label>
                    <Input
                      id="nome"
                      value={nuovaPersona.nome}
                      onChange={(e) => setNuovaPersona(prev => ({ ...prev, nome: e.target.value }))}
                      placeholder="Nome e cognome completo"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="qualifica">Qualifica *</Label>
                    <Input
                      id="qualifica"
                      value={nuovaPersona.qualifica}
                      onChange={(e) => setNuovaPersona(prev => ({ ...prev, qualifica: e.target.value }))}
                      placeholder="Qualifica professionale"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={nuovaPersona.email}
                      onChange={(e) => setNuovaPersona(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="Indirizzo email"
                    />
                  </div>
                  <div>
                    <Label htmlFor="telefono">Telefono</Label>
                    <Input
                      id="telefono"
                      value={nuovaPersona.telefono}
                      onChange={(e) => setNuovaPersona(prev => ({ ...prev, telefono: e.target.value }))}
                      placeholder="Numero di telefono"
                    />
                  </div>
                </div>
                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={aggiungiPersonaMutation.isPending}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {aggiungiPersonaMutation.isPending ? "Aggiungendo..." : "Aggiungi alla Rubrica"}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Elenco persone in rubrica */}
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-700">
                Persone in Rubrica ({rubricaPersone.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {rubricaPersone.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>Nessuna persona in rubrica</p>
                  <p className="text-sm">Aggiungi persone che partecipano frequentemente ai verbali</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {rubricaPersone.map((persona) => (
                    <div key={persona.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-medium">{persona.nome}</div>
                          <div className="text-sm text-gray-600">{persona.qualifica}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {persona.email && <span className="mr-4">📧 {persona.email}</span>}
                            {persona.telefono && <span>📞 {persona.telefono}</span>}
                          </div>
                        </div>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(persona.id)}
                          disabled={eliminaPersonaMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Elimina
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end pt-4">
          <Button onClick={onClose} variant="outline">
            Chiudi
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}