import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { insertDirettoreLavoriSchema, type DirettoreLavori, type InsertDirettoreLavori } from "@shared/schema";

interface DirettoreLavoriModalProps {
  isOpen: boolean;
  onClose: () => void;
  direttore: DirettoreLavori | null;
  title: string;
}

export default function DirettoreLavoriModal({ isOpen, onClose, direttore, title }: DirettoreLavoriModalProps) {
  const { toast } = useToast();

  const form = useForm<InsertDirettoreLavori>({
    resolver: zodResolver(insertDirettoreLavoriSchema),
    defaultValues: {
      nome: direttore?.nome || "",
      cognome: direttore?.cognome || "",
      qualifica: direttore?.qualifica || "",
      attivo: direttore?.attivo || "true",
    },
  });

  const createMutation = useMutation({
    mutationFn: async (data: InsertDirettoreLavori) => {
      const response = await apiRequest("POST", "/api/direttori-lavori", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/direttori-lavori"] });
      toast({
        title: "Successo",
        description: "Direttore lavori creato con successo.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Errore durante la creazione del direttore lavori.",
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (data: InsertDirettoreLavori) => {
      const response = await apiRequest("PUT", `/api/direttori-lavori/${direttore!.id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/direttori-lavori"] });
      toast({
        title: "Successo",
        description: "Direttore lavori aggiornato con successo.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Errore durante l'aggiornamento del direttore lavori.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: InsertDirettoreLavori) => {
    if (direttore) {
      updateMutation.mutate(data);
    } else {
      createMutation.mutate(data);
    }
  };

  const isPending = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {title}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="nome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nome <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Nome" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cognome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Cognome <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Cognome" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="qualifica"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Qualifica <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="es. Architetto, Ingegnere Strutturale, Ingegnere Impiantistico" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Annulla
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? "Salvataggio..." : direttore ? "Aggiorna" : "Salva"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}