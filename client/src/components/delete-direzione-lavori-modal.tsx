import { useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest } from "@/lib/queryClient";
import type { DirezioneLavori } from "@shared/schema";

interface DeleteDirezioneLavoriModalProps {
  isOpen: boolean;
  onClose: () => void;
  lavoro: DirezioneLavori | null;
}

export default function DeleteDirezioneLavoriModal({ isOpen, onClose, lavoro }: DeleteDirezioneLavoriModalProps) {
  const [, navigate] = useLocation();
  const { toast } = useToast();

  const deleteMutation = useMutation({
    mutationFn: async () => {
      if (!lavoro?.id) throw new Error("ID lavoro non valido");
      return apiRequest("DELETE", `/api/direzione-lavori/${lavoro.id}`);
    },
    onSuccess: () => {
      toast({
        title: "Successo",
        description: "Direzione lavori eliminata con successo.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori"] });
      onClose();
      navigate("/direzione-lavori");
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante l'eliminazione della direzione lavori.",
        variant: "destructive",
      });
    },
  });

  const handleDelete = () => {
    deleteMutation.mutate();
  };

  if (!lavoro) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Conferma Eliminazione
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Sei sicuro di voler eliminare la direzione lavori "{lavoro.codiceCommessa}"?
            <br />
            <br />
            <strong>Questa azione non può essere annullata.</strong> Tutti i verbali associati a questa direzione lavori verranno eliminati permanentemente.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose} disabled={deleteMutation.isPending}>
            Annulla
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? "Eliminazione..." : "Elimina"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}