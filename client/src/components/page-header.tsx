import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigation } from "@/hooks/useNavigation";
import SmartBreadcrumb from "./smart-breadcrumb";

interface PageHeaderProps {
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  showBackButton?: boolean;
  className?: string;
}

export default function PageHeader({
  title,
  subtitle,
  actions,
  showBackButton = true,
  className = ""
}: PageHeaderProps) {
  const { goBack, getCurrentPageTitle, isDetailPage, breadcrumbs } = useNavigation();

  const displayTitle = title || getCurrentPageTitle();
  const hasBreadcrumbs = breadcrumbs.length > 1;

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <div className="max-w-7xl mx-auto px-6 py-4">
        {/* Breadcrumb */}
        <SmartBreadcrumb />

        {/* Header Content */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Mostra pulsante indietro solo se non ci sono breadcrumb e siamo in una detail page */}
            {showBackButton && isDetailPage() && !hasBreadcrumbs && (
              <Button variant="ghost" size="sm" onClick={goBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Indietro
              </Button>
            )}

            <div>
              <h1 className="text-2xl font-bold text-gray-900">{displayTitle}</h1>
              {subtitle && (
                <p className="text-gray-600 mt-1">{subtitle}</p>
              )}
            </div>
          </div>

          {actions && (
            <div className="flex items-center space-x-2">
              {actions}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
