import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { Cantiere } from "@shared/schema";

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  cantiere: Cantiere | null;
}

export default function DeleteConfirmationModal({ isOpen, onClose, cantiere }: DeleteConfirmationModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("DELETE", `/api/cantieri/${cantiere!.id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cantieri"] });
      toast({
        title: "Successo",
        description: "Cantiere eliminato con successo.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Errore durante l'eliminazione del cantiere.",
        variant: "destructive",
      });
    },
  });

  const handleDelete = () => {
    if (cantiere) {
      deleteMutation.mutate();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-start">
            <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Elimina Cantiere
              </h3>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="mt-2">
          <p className="text-sm text-gray-500">
            Sei sicuro di voler eliminare il cantiere <strong>{cantiere?.codiceCommessa}</strong>? 
            Questa azione non può essere annullata.
          </p>
        </div>

        <div className="mt-6 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 sm:space-y-0 space-y-reverse">
          <Button type="button" variant="outline" onClick={onClose}>
            Annulla
          </Button>
          <Button 
            type="button" 
            variant="destructive" 
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? "Eliminando..." : "Elimina"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
