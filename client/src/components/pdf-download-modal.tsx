import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Download, RefreshCw, Database, Clock, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface PDFDownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  verbaleId: number;
  filename: string;
}

interface PDFStatus {
  verbaleId: number;
  numeroProgressivo: string;
  hasPDFInDatabase: boolean;
  options: {
    downloadFromDB: string | null;
    regenerate: string;
  };
}

export default function PDFDownloadModal({ isOpen, onClose, verbaleId, filename }: PDFDownloadModalProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadType, setDownloadType] = useState<string | null>(null);
  const { toast } = useToast();

  // Check PDF status
  const { data: pdfStatus, isLoading } = useQuery<PDFStatus>({
    queryKey: ["/api/verbali", verbaleId, "pdf-status"],
    queryFn: async () => {
      const response = await apiRequest("GET", `/api/verbali/${verbaleId}/pdf-status`);
      if (!response.ok) throw new Error("Failed to fetch PDF status");
      return response.json();
    },
    enabled: isOpen && !!verbaleId,
  });

  const handleDownload = async (action: string) => {
    setIsDownloading(true);
    setDownloadType(action);

    try {
      const token = localStorage.getItem('auth_token');
      // Aggiungi il verbaleId alla query string per aiutare la ricerca del verbale
      const queryParams = new URLSearchParams();
      if (action) queryParams.set('action', action);
      queryParams.set('verbaleId', verbaleId.toString());
      
      const url = `/api/download-pdf/${filename}?${queryParams.toString()}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      // Get PDF source from header
      const pdfSource = response.headers.get('X-PDF-Source') || 'unknown';
      
      const blob = await response.blob();
      
      // Create blob URL and download
      const blobUrl = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(blobUrl);

      toast({
        title: "Download completato",
        description: `PDF scaricato con successo (fonte: ${pdfSource})`,
      });

      onClose();
    } catch (error) {
      console.error('PDF download error:', error);
      toast({
        title: "Errore Download",
        description: error instanceof Error ? error.message : "Impossibile scaricare il PDF. Riprova.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
      setDownloadType(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-xl">Scarica PDF Verbale</CardTitle>
          <Button variant="ghost" onClick={onClose} disabled={isDownloading}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-500">Verifica disponibilità PDF...</p>
            </div>
          ) : pdfStatus ? (
            <>
              {/* PDF Info */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">Informazioni Verbale</h3>
                <p className="text-sm text-gray-600">
                  <strong>Numero:</strong> {pdfStatus.numeroProgressivo}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>File:</strong> {filename}
                </p>
                <div className="mt-2">
                  {pdfStatus.hasPDFInDatabase ? (
                    <Badge className="bg-green-100 text-green-800">PDF disponibile nel database</Badge>
                  ) : (
                    <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
                      PDF non in cache
                    </Badge>
                  )}
                </div>
              </div>

              {/* Download Options */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">Scegli modalità di download:</h3>
                
                {/* Database Download */}
                {pdfStatus.hasPDFInDatabase && (
                  <Card className="border-2 border-green-200 bg-green-50">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-green-100 rounded-lg">
                            <Database className="h-5 w-5 text-green-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-green-900">Scarica da Database</h4>
                            <p className="text-sm text-green-700">
                              Download veloce del PDF salvato (consigliato)
                            </p>
                            <div className="flex items-center space-x-1 mt-1">
                              <Clock className="h-3 w-3 text-green-600" />
                              <span className="text-xs text-green-600">~1 secondo</span>
                            </div>
                          </div>
                        </div>
                        <Button
                          onClick={() => handleDownload('db')}
                          disabled={isDownloading}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {isDownloading && downloadType === 'db' ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Scaricando...
                            </>
                          ) : (
                            <>
                              <Download className="h-4 w-4 mr-2" />
                              Scarica Veloce
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Regenerate Download */}
                <Card className="border-2 border-blue-200 bg-blue-50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <RefreshCw className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-blue-900">Rigenera PDF Aggiornato</h4>
                          <p className="text-sm text-blue-700">
                            Genera un nuovo PDF con i dati più recenti
                          </p>
                          <div className="flex items-center space-x-1 mt-1">
                            <Clock className="h-3 w-3 text-blue-600" />
                            <span className="text-xs text-blue-600">~5-10 secondi</span>
                          </div>
                        </div>
                      </div>
                      <Button
                        onClick={() => handleDownload('regenerate')}
                        disabled={isDownloading}
                        variant="outline"
                        className="border-blue-300 text-blue-700 hover:bg-blue-100"
                      >
                        {isDownloading && downloadType === 'regenerate' ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                            Generando...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Rigenera
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Auto Download */}
                <Card className="border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <Download className="h-5 w-5 text-gray-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Download Automatico</h4>
                          <p className="text-sm text-gray-600">
                            Il sistema sceglie automaticamente la modalità migliore
                          </p>
                          <div className="flex items-center space-x-1 mt-1">
                            <Clock className="h-3 w-3 text-gray-500" />
                            <span className="text-xs text-gray-500">
                              {pdfStatus.hasPDFInDatabase ? '~1 secondo' : '~5-10 secondi'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button
                        onClick={() => handleDownload('')}
                        disabled={isDownloading}
                        variant="outline"
                      >
                        {isDownloading && downloadType === '' ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                            Scaricando...
                          </>
                        ) : (
                          <>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Help Text */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">ℹ️ Quale scegliere?</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li><strong>Database:</strong> Veloce, usa il PDF salvato in precedenza</li>
                  <li><strong>Rigenera:</strong> Sempre aggiornato, include le ultime modifiche</li>
                  <li><strong>Automatico:</strong> Il sistema sceglie la modalità migliore</li>
                </ul>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">Errore nel caricamento delle informazioni PDF</p>
              <Button variant="outline" onClick={onClose} className="mt-4">
                Chiudi
              </Button>
            </div>
          )}
        </CardContent>
      </div>
    </div>
  );
}