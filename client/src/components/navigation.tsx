import { Link, useLocation } from "wouter";
import { HardHat, Briefcase, Settings, Home, LogOut, User, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import OfflineIndicator from "@/components/offline-indicator";

export default function Navigation() {
  const [location] = useLocation();
  const { user, logout } = useAuth();
  const { toast } = useToast();

  const isActive = (path: string) => {
    if (path === "/" && location === "/") return true;
    if (path !== "/" && location.startsWith(path)) return true;
    return false;
  };

  const handleLogout = () => {
    logout();
    toast({
      title: "Logout effettuato",
      description: "Sei stato disconnesso dal sistema",
    });
  };

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center space-x-8">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/">
                <h1 className="text-xl font-bold text-primary hover:text-primary/80 transition-colors cursor-pointer">
                  Orbyta Engineering SRL
                </h1>
              </Link>
            </div>
            
            <div className="flex space-x-4">
              <Link href="/">
                <div className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  isActive("/") && location === "/"
                    ? "text-primary border-b-2 border-primary bg-primary/10"
                    : "text-gray-600 hover:text-primary hover:bg-accent"
                }`}>
                  <Home className="h-4 w-4" />
                  <span>Home</span>
                </div>
              </Link>

              <Link href="/cantieri">
                <div className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  isActive("/cantieri")
                    ? "text-orange-600 border-b-2 border-orange-500 bg-orange-50"
                    : "text-gray-600 hover:text-orange-600 hover:bg-orange-50"
                }`}>
                  <HardHat className="h-4 w-4" />
                  <span>Coordinamento Sicurezza</span>
                </div>
              </Link>

              <Link href="/direzione-lavori">
                <div className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  isActive("/direzione-lavori")
                    ? "text-blue-600 border-b-2 border-blue-500 bg-blue-50"
                    : "text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                }`}>
                  <Briefcase className="h-4 w-4" />
                  <span>Direzione Lavori</span>
                </div>
              </Link>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <OfflineIndicator inline />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2 hover:bg-gray-100">
                  <User className="h-4 w-4" />
                  <span className="font-medium">{user?.username}</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem asChild>
                  <Link href="/impostazioni" className="flex items-center w-full">
                    <Settings className="h-4 w-4 mr-2" />
                    Impostazioni
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="text-red-600 focus:text-red-700 focus:bg-red-50"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Esci
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  );
}