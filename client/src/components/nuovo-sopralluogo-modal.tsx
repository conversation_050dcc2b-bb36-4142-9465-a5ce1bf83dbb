import { useState, useEffect } from "react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import FirmaVerbaleModal from "./firma-verbale-modal";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, apiUpload } from "@/lib/queryClient";
import { Plus, Trash2 } from "lucide-react";
import type { Cantiere, Tecnico, InsertSopralluogo, PersonaPresente } from "@shared/schema";

interface NuovoSopralluogoModalProps {
  isOpen: boolean;
  onClose: () => void;
  cantiere: Cantiere;
  tecnici: Tecnico[];
  sopralluogoInModifica?: any | null;
}

const sopralluogoFormSchema = z.object({
  data: z.string().min(1, "Data obbligatoria"),
  tecnicoId: z.string().min(1, "Tecnico obbligatorio"),
  note: z.string().optional(),
  lavorazioniInCorso: z.string().optional(),
});

type ChecklistItem = {
  id: string;
  categoria: string;
  riferimentoNormativo?: string;
  domanda: string;
  risposta?: "conforme" | "non_conforme" | "non_pertinente";
  note?: string;
  foto?: string[]; // Array di nomi file delle foto caricate
};

type PersonaPresenteLocal = {
  id: string;
  nome: string;
  qualifica: string;
};

type ChecklistCategoria = {
  nome: string;
  riferimentoNormativo: string;
};

export default function NuovoSopralluogoModal({ 
  isOpen, 
  onClose, 
  cantiere, 
  tecnici,
  sopralluogoInModifica 
}: NuovoSopralluogoModalProps) {
  const [currentStep, setCurrentStep] = useState<"info" | "persone" | "checklist">("info");
  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>([]);
  const [personePresenti, setPersonePresenti] = useState<PersonaPresenteLocal[]>([
    { id: "1", nome: "", qualifica: "" } // Prima persona predefinita
  ]);
  const [personeSelezionate, setPersoneSelezionate] = useState<Set<string>>(new Set());
  const [isFirmaModalOpen, setIsFirmaModalOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Query per recuperare le persone presenti salvate per questo cantiere
  const { data: personeSalvate = [] } = useQuery<PersonaPresente[]>({
    queryKey: [`/api/cantieri/${cantiere.id}/persone-presenti`],
    enabled: isOpen,
  });



  const categorieConRiferimenti: ChecklistCategoria[] = [
    {
      nome: "Organizzazione generale del cantiere",
      riferimentoNormativo: "Rif. Sezione II – Disposizioni di carattere generale - Titolo IV – D.lgs. n°81/08 e s.m.i. – in particolare: artt. 108, 109, 110, 114"
    },
    {
      nome: "Scavi", 
      riferimentoNormativo: "Rif. Sezione III – Scavi e Fondazioni - Titolo IV – D.lgs. n°81/08 e s.m.i. – in particolare: artt. 118 e 119"
    },
    {
      nome: "Scale",
      riferimentoNormativo: "Rif. Sezione II – Disposizioni di carattere generale - Titolo IV – D.lgs. n°81/08 e s.m.i. – in particolare: artt. 111, 113"
    },
    {
      nome: "Ponteggi Fissi e mobili",
      riferimentoNormativo: "Rif. Sezioni IV – V - VI – \"Ponteggi in legname ed altre opere provvisionali\"; \"Ponteggi fissi\"; \"Ponteggi movibili\" - Titolo IV – D.lgs. n°81/08 e s.m.i. – ALLEGATI XVIII e XIX"
    },
    {
      nome: "Organizzazione e tenuta del Cantiere",
      riferimentoNormativo: "Attrezzature, mezzi, apparecchi di sollevamento, protezione aperture, passarelle, ecc."
    }
  ];

  const form = useForm<z.infer<typeof sopralluogoFormSchema>>({
    resolver: zodResolver(sopralluogoFormSchema),
    defaultValues: {
      data: sopralluogoInModifica?.data || new Date().toISOString().split('T')[0],
      tecnicoId: sopralluogoInModifica?.tecnicoId?.toString() || "",
      note: sopralluogoInModifica?.note || "",
      lavorazioniInCorso: sopralluogoInModifica?.lavorazioniInCorso || "",
    },
  });

  // Effect to load saved draft data when editing
  useEffect(() => {
    if (sopralluogoInModifica && isOpen) {
      // Reset and load saved form data
      form.reset({
        data: sopralluogoInModifica.data,
        tecnicoId: sopralluogoInModifica.tecnicoId?.toString() || "",
        note: sopralluogoInModifica.note || "",
        lavorazioniInCorso: sopralluogoInModifica.lavorazioniInCorso || "",
      });

      // Load saved checklist data
      if (sopralluogoInModifica.checklistData) {
        try {
          const savedChecklist = JSON.parse(sopralluogoInModifica.checklistData);
          setChecklistItems(savedChecklist);
        } catch (error) {
          console.error("Error parsing saved checklist:", error);
          // Initialize default checklist if parsing fails
          const defaultChecklist = categorieConRiferimenti.flatMap((categoria) => 
            getDefaultChecklistForCategory(categoria.nome)
          );
          setChecklistItems(defaultChecklist);
        }
      } else {
        // Initialize default checklist if no saved data
        const defaultChecklist = categorieConRiferimenti.flatMap((categoria) => 
          getDefaultChecklistForCategory(categoria.nome)
        );
        setChecklistItems(defaultChecklist);
      }

      // Load saved persone presenti data
      if (sopralluogoInModifica.personePresentiData) {
        try {
          const savedPersone = JSON.parse(sopralluogoInModifica.personePresentiData);
          setPersonePresenti(savedPersone.length > 0 ? savedPersone : [{ id: "1", nome: "", qualifica: "" }]);
        } catch (error) {
          console.error("Error parsing saved persone presenti:", error);
          setPersonePresenti([{ id: "1", nome: "", qualifica: "" }]);
        }
      } else {
        setPersonePresenti([{ id: "1", nome: "", qualifica: "" }]);
      }

      // Set current step from saved data
      if (sopralluogoInModifica.currentStep) {
        setCurrentStep(sopralluogoInModifica.currentStep as "info" | "persone" | "checklist");
      } else {
        setCurrentStep("info");
      }
    } else if (isOpen && !sopralluogoInModifica) {
      // Reset for new sopralluogo
      form.reset({
        data: new Date().toISOString().split('T')[0],
        tecnicoId: "",
        note: "",
        lavorazioniInCorso: "",
      });
      setChecklistItems([]);
      setPersonePresenti([{ id: "1", nome: "", qualifica: "" }]);
      setCurrentStep("info");
    }
  }, [sopralluogoInModifica, isOpen, form]);

  // Funzione helper per ottenere la checklist di default per categoria
  const getDefaultChecklistForCategory = (categoria: string): ChecklistItem[] => {
    const checklistMap: Record<string, ChecklistItem[]> = {
      "Organizzazione generale del cantiere": [
        {
          id: "org_1",
          categoria: "Organizzazione generale del cantiere",
          domanda: "Sono garantite le delimitazioni dell'area di cantiere, dei percorsi dei mezzi e dei percorsi pedonali, ecc."
        },
        {
          id: "org_2",
          categoria: "Organizzazione generale del cantiere",
          domanda: "Sono assicurate costantemente la chiusura degli ingressi all'area di cantiere"
        },
        {
          id: "org_3",
          categoria: "Organizzazione generale del cantiere",
          domanda: "È garantito che il transito sotto ponti sospesi, ponti a sbalzo, scale aeree, ecc. sia impedito o adeguatamente protetto"
        },
        {
          id: "org_4",
          categoria: "Organizzazione generale del cantiere",
          domanda: "È assicurata la presenza dell'idonea segnaletica e cartellonistica di cantiere"
        },
        {
          id: "org_5",
          categoria: "Organizzazione generale del cantiere",
          domanda: "È garantito un corretto ed adeguato deposito di materiali, mezzi ed attrezzature"
        },
        {
          id: "org_6",
          categoria: "Organizzazione generale del cantiere",
          domanda: "È assicurata la limitazione nel sollevamento di polveri dovute al passaggio dei mezzi o ad altro, provvedendo, se necessario, mediante bagnatura delle superfici"
        }
      ],
      "Scavi": [
        {
          id: "scavi_1",
          categoria: "Scavi",
          domanda: "Sono garantiti tutti gli accorgimenti necessari al fine di contrastare i rischi di franamento e seppellimento degli operai"
        },
        {
          id: "scavi_2",
          categoria: "Scavi",
          domanda: "È garantito il mantenimento di un angolo di inclinazione della scarpata di scavo come da progetto"
        }
        // Aggiungi altre domande per scavi se necessario
      ],
      "Scale": [
        {
          id: "scale_1",
          categoria: "Scale",
          domanda: "Le scale sono conformi alle normative di sicurezza"
        }
        // Aggiungi altre domande per scale se necessario
      ],
      "Ponteggi Fissi e mobili": [
        {
          id: "ponteggi_1",
          categoria: "Ponteggi Fissi e mobili",
          domanda: "I ponteggi sono stati montati secondo le normative vigenti"
        }
        // Aggiungi altre domande per ponteggi se necessario
      ],
      "Organizzazione e tenuta del Cantiere": [
        {
          id: "tenuta_1",
          categoria: "Organizzazione e tenuta del Cantiere",
          domanda: "Le attrezzature e i mezzi sono in condizioni di sicurezza"
        }
        // Aggiungi altre domande per organizzazione se necessario
      ]
    };

    return checklistMap[categoria] || [];
  };

  // Query per ottenere i sopralluoghi esistenti e calcolare il numero progressivo
  const { data: sopralluoghiEsistenti } = useQuery({
    queryKey: [`/api/cantieri/${cantiere.id}/sopralluoghi`],
    enabled: isOpen
  });

  // Funzione per generare il numero sopralluogo automaticamente
  const generateNumeroSopralluogo = (data: string) => {
    const date = new Date(data);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateString = `${year}${month}${day}`;
    
    // Calcola il numero progressivo basato sui sopralluoghi esistenti
    const sopralluoghiArray = (sopralluoghiEsistenti as any[]) || [];
    const numeroProgressivo = String(sopralluoghiArray.length + 1).padStart(3, '0');
    
    // Formato per sopralluoghi di coordinamento sicurezza: CODICECOMMESSA-CSE-NRPROGRESSIVO-AAAAMMGG
    return `${cantiere.codiceCommessa}-CSE-${numeroProgressivo}-${dateString}`;
  };

  const createSopralluogoMutation = useMutation({
    mutationFn: async (data: InsertSopralluogo & { checklist: ChecklistItem[] }) => {
      const response = await apiRequest("POST", "/api/sopralluoghi", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ["/api/cantieri", cantiere.id, "sopralluoghi"] 
      });
      toast({
        title: "Successo",
        description: "Sopralluogo creato con successo",
      });
      onClose();
      form.reset();
      setCurrentStep("info");
      setChecklistItems([]);
      setPersonePresenti([{ id: "1", nome: "", qualifica: "" }]);
    },
    onError: () => {
      toast({
        title: "Errore",
        description: "Errore durante la creazione del sopralluogo",
        variant: "destructive",
      });
    },
  });

  // Draft saving mutation
  const salvaBozzaMutation = useMutation({
    mutationFn: async (data: InsertSopralluogo & { checklist: ChecklistItem[], personePresenti: PersonaPresenteLocal[] }) => {
      if (sopralluogoInModifica) {
        // Update existing draft
        const response = await apiRequest("PUT", `/api/sopralluoghi/${sopralluogoInModifica.id}`, data);
        return response.json();
      } else {
        // Create new draft
        const response = await apiRequest("POST", "/api/sopralluoghi/salva-bozza", data);
        return response.json();
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ["/api/cantieri", cantiere.id, "sopralluoghi"] 
      });
      toast({
        title: "Bozza salvata",
        description: sopralluogoInModifica ? "La bozza è stata aggiornata con successo" : "La bozza del sopralluogo è stata salvata con successo",
      });
      onClose();
      form.reset();
      setCurrentStep("info");
      setChecklistItems([]);
      setPersonePresenti([{ id: "1", nome: "", qualifica: "" }]);
    },
    onError: () => {
      toast({
        title: "Errore",
        description: "Errore durante il salvataggio della bozza",
        variant: "destructive",
      });
    },
  });

  const createSopralluogoConFirmeMutation = useMutation({
    mutationFn: async (data: InsertSopralluogo & { checklist: ChecklistItem[], firme: Record<string, string> }) => {
      const payload = sopralluogoInModifica 
        ? { ...data, sopralluogoId: sopralluogoInModifica.id }
        : data;
      const response = await apiRequest("POST", "/api/sopralluoghi/con-firme", payload);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ["/api/cantieri", cantiere.id, "sopralluoghi"] 
      });
      toast({
        title: "Successo",
        description: "Sopralluogo finalizzato con successo",
      });
      onClose();
      form.reset();
      setCurrentStep("info");
      setChecklistItems([]);
      setPersonePresenti([{ id: "1", nome: "", qualifica: "" }]);
      setIsFirmaModalOpen(false);
    },
    onError: () => {
      toast({
        title: "Errore",
        description: "Errore durante la finalizzazione del sopralluogo",
        variant: "destructive",
      });
    },
  });

  const handleInfoSubmit = (data: z.infer<typeof sopralluogoFormSchema>) => {
    setCurrentStep("persone");
  };

  const handlePersoneSubmit = async () => {
    // Non è più obbligatorio inserire persone presenti
    const personeValide = personePresenti.filter(persona => 
      persona.nome.trim() !== "" && persona.qualifica.trim() !== ""
    );

    // Salva IMMEDIATAMENTE le nuove persone nel database per questo cantiere
    try {
      await Promise.all(
        personeValide.map(async (persona) => {
          // Controlla se la persona esiste già nel database
          const esisteGia = personeSalvate.some(p => 
            p.nome && persona.nome && 
            p.qualifica && persona.qualifica &&
            p.nome.toLowerCase().trim() === persona.nome.toLowerCase().trim() && 
            p.qualifica.toLowerCase().trim() === persona.qualifica.toLowerCase().trim()
          );
          
          if (!esisteGia) {
            try {
              await apiRequest("POST", `/api/cantieri/${cantiere.id}/persone-presenti`, {
                nome: persona.nome.trim(),
                qualifica: persona.qualifica.trim()
              });
              console.log(`Salvata nuova persona: ${persona.nome} - ${persona.qualifica}`);
            } catch (error) {
              console.error("Errore salvando persona:", error);
            }
          } else {
            console.log(`Persona già esistente: ${persona.nome} - ${persona.qualifica}`);
          }
        })
      );
      
      // Invalida la cache per ricaricare l'elenco persone presenti aggiornato
      queryClient.invalidateQueries({ 
        queryKey: [`/api/cantieri/${cantiere.id}/persone-presenti`] 
      });
      
    } catch (error) {
      console.error("Errore durante il salvataggio delle persone:", error);
      toast({
        title: "Attenzione",
        description: "Alcune persone potrebbero non essere state salvate, ma puoi continuare",
        variant: "destructive",
      });
    }

    const checklistItems: ChecklistItem[] = [
      // Organizzazione generale del cantiere
      {
        id: "org_1",
        categoria: "Organizzazione generale del cantiere",
        domanda: "Sono garantite le delimitazioni dell'area di cantiere, dei percorsi dei mezzi e dei percorsi pedonali, ecc."
      },
      {
        id: "org_2",
        categoria: "Organizzazione generale del cantiere",
        domanda: "Sono assicurate costantemente la chiusura degli ingressi all'area di cantiere"
      },
      {
        id: "org_3",
        categoria: "Organizzazione generale del cantiere",
        domanda: "È garantito che il transito sotto ponti sospesi, ponti a sbalzo, scale aeree, ecc. sia impedito o adeguatamente protetto"
      },
      {
        id: "org_4",
        categoria: "Organizzazione generale del cantiere",
        domanda: "È assicurata la presenza dell'idonea segnaletica e cartellonistica di cantiere"
      },
      {
        id: "org_5",
        categoria: "Organizzazione generale del cantiere",
        domanda: "È garantito un corretto ed adeguato deposito di materiali, mezzi ed attrezzature"
      },
      {
        id: "org_6",
        categoria: "Organizzazione generale del cantiere",
        domanda: "È assicurata la limitazione nel sollevamento di polveri dovute al passaggio dei mezzi o ad altro, provvedendo, se necessario, mediante bagnatura delle superfici"
      },

      // Scavi
      {
        id: "scavi_1",
        categoria: "Scavi",
        domanda: "Sono garantiti tutti gli accorgimenti necessari al fine di contrastare i rischi di franamento e seppellimento degli operai"
      },
      {
        id: "scavi_2",
        categoria: "Scavi",
        domanda: "È garantito il mantenimento di un angolo di inclinazione della scarpata di scavo come da progetto"
      },
      {
        id: "scavi_3",
        categoria: "Scavi",
        domanda: "È garantita la gradonatura dello scavo ad intervalli verticali non superiori a 1.5 m"
      },
      {
        id: "scavi_4",
        categoria: "Scavi",
        domanda: "È sempre garantita la presenza sul perimetro degli scavi di sbancamento e in trincea di idonee protezioni anti-caduta o delimitazioni"
      },
      {
        id: "scavi_5",
        categoria: "Scavi",
        domanda: "È assicurato il mantenimento di idonei parapetti ad una sufficiente distanza di sicurezza dal ciglio di scavo: per scavi di profondità inferiore ai 50 cm, il perimetro dello scavo è delimitato con idonei nastri segnaletici"
      },
      {
        id: "scavi_6",
        categoria: "Scavi",
        domanda: "I parapetti sono conformi e robusti ad eventuali urti; in particolare che la tavola fermapiede (altezza minima 20 cm) è aderente al piano campagna e i correnti (intermedio e superiore) sono adeguatamente immorsati/inchiodati nei montanti per evitare di farli \"lavorare a sfilo\""
      },
      {
        id: "scavi_7",
        categoria: "Scavi",
        domanda: "È assicurata la protezione dei fronti scavo con ricoprimenti protettivi o armature, quando per la particolare natura del terreno o per causa di piogge, infiltrazioni, gelo o disgelo o altri motivi, siano da temere cedimenti, anche localizzati, delle scarpate"
      },
      {
        id: "scavi_8",
        categoria: "Scavi",
        domanda: "È impedito il deposito di materiali presso il ciglio degli scavi"
      },
      {
        id: "scavi_9",
        categoria: "Scavi",
        domanda: "È garantito che gli accessi agli scavi siano idoneamente predisposti e sicuri durante tutto il tempo del loro utilizzo"
      },
      {
        id: "scavi_10",
        categoria: "Scavi",
        domanda: "È assicurato che i lavoratori operanti all'interno degli scavi utilizzino i necessari DPI (casco di protezione) in relazione alla possibilità di distacco dai fronti di massi/ciottoli"
      },
      {
        id: "scavi_11",
        categoria: "Scavi",
        domanda: "È assicurata la delimitazione ed eventuale chiusura delle aree in scavo per le quali non siano ancora stati previsti i necessari accorgimenti di sicurezza"
      },
      {
        id: "scavi_12",
        categoria: "Scavi",
        domanda: "È garantita la protezione dei ferri di ripresa emergenti dal piano di fondazione o utilizzati per la delimitazione in fase di tracciamento"
      },
      {
        id: "scavi_13",
        categoria: "Scavi",
        domanda: "È vietato, in occasione di escavazioni con mezzi meccanici, la presenza di operai nel raggio d'azione dell'escavatore e/o sul ciglio del fronte di attacco"
      },
      {
        id: "scavi_14",
        categoria: "Scavi",
        domanda: "È garantita la bagnatura di fronti e piazzali, qualora le condizioni del terreno e quelle meteo-climatiche lo richiedano"
      },

      // Scale
      {
        id: "scale_1",
        categoria: "Scale",
        domanda: "È garantito l'impiego esclusivo di scale idonee (art. 113 c. 2 e c. 3) e conformi ai requisiti richiesti ed indicati dall'Allegato XX"
      },
      {
        id: "scale_2",
        categoria: "Scale",
        domanda: "Le scale a pioli sono sistemate in modo da assicurare la loro stabilità durante l'impiego e secondo i criteri specifici (supporto stabile, dispositivi antiscivolo, sporgenza sufficiente oltre il livello di accesso, fermo reciproco degli elementi, fissaggio stabile)"
      },
      {
        id: "scale_3",
        categoria: "Scale",
        domanda: "È assicurato ai lavoratori di disporre in qualsiasi momento di un appoggio e di una presa sicuri, anche nel caso di trasporto manuale di pesi"
      },

      // Ponteggi Fissi e mobili
      {
        id: "ponteggi_1",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È garantito, in caso di lavori a quote fino ai 2 metri in cui sia previsto l'uso di ponti su cavalletti, che questi rispettino quanto previsto dall'art. 139 e siano conformi alle indicazioni/prescrizioni del punto 2.2.2 dell'All. XVIII"
      },
      {
        id: "ponteggi_2",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È garantito quanto indicato all'art. 122: nei lavori che sono eseguiti ad un'altezza superiore ai m 2, deve essere assicurato l'impiego di adeguate impalcature o ponteggi o idonee opere provvisionali"
      },
      {
        id: "ponteggi_3",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È garantito scrupolosamente che il montaggio, lo smontaggio e la trasformazione, oltre ad essere eseguiti da lavoratori in possesso di adeguata formazione, avvengano sempre sotto la diretta sorveglianza di un preposto e conformemente alle indicazioni ed agli schemi riportati nel Pi.M.U.S."
      },
      {
        id: "ponteggi_4",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È garantita la presenza e la continuità di idonei parapetti in corrispondenza di tutti i lati prospicienti il vuoto (altezza non inferiore ad 1 m; presenza di tavola fermapiede di altezza non inferiore a 20 cm)"
      },
      {
        id: "ponteggi_5",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È assicurata la presenza di sottoponte di sicurezza a distanza non superiore a 2.5 metri"
      },
      {
        id: "ponteggi_6",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È presente in cantiere tutta la documentazione riguardante l'autorizzazione Ministeriale, le relazioni di calcolo e quanto necessario per l'impiego del ponteggio"
      },
      {
        id: "ponteggi_7",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "Non sono utilizzate tavole di armatura per la costruzione degli impalcati"
      },
      {
        id: "ponteggi_8",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È assicurato in caso di transito nelle immediate vicinanze dell'impalcato, la presenza di idonea mantovana di protezione"
      },
      {
        id: "ponteggi_9",
        categoria: "Ponteggi Fissi e mobili",
        domanda: "È garantita l'assenza di depositi di materiale sugli impalcati e sui ponti di servizio, ad eccezione di quelli temporanei necessari alle lavorazioni"
      },

      // Organizzazione e tenuta del Cantiere
      {
        id: "tenuta_1",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita la conformità di andatoie e passerelle, così come previsto dalla normativa"
      },
      {
        id: "tenuta_2",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita la difesa delle aperture; le aperture lasciate nei solai o nelle piattaforme di lavoro devono essere circondate da normale parapetto e da tavola fermapiede oppure devono essere coperte con tavolato solidamente fissato"
      },
      {
        id: "tenuta_3",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita la protezione dei ferri di ripresa sporgenti dal piano campagna, mediante apposizione degli idonei \"cappucci\" o altra misura di analoga efficacia"
      },
      {
        id: "tenuta_4",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita la protezione dei posti di lavoro ove vengono eseguite operazioni di carattere continuativo e si riscontra il rischio di caduta materiali dall'alto"
      },
      {
        id: "tenuta_5",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita la presenza in cantiere di tutta la documentazione relativa all'uso e manutenzione degli apparecchi di sollevamento (libretto, verifiche periodiche, ecc.)"
      },
      {
        id: "tenuta_6",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita, per quanto concerne gli impianti di cantiere che verranno installati (es./impianto elettrico), l'esecuzione da parte di ditte specializzate e la produzione della relativa \"certificazione di conformità\""
      },
      {
        id: "tenuta_7",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita la priorità delle misure di protezione collettiva rispetto alle misure di protezione individuale; nei lavori in quota ove non è possibile dare attuazione a quanto appena citato si garantisca l'utilizzo di idonei sistemi di protezione individuale anti-caduta"
      },
      {
        id: "tenuta_8",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantito un corretto uso delle attrezzature, nonché la conformità delle stesse"
      },
      {
        id: "tenuta_9",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantito che la movimentazione di carichi e materiali avvenga costantemente in condizioni di sicurezza, in particolare impiegando solo funi e ganci conformi e non usurati"
      },
      {
        id: "tenuta_10",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantito l'adempimento alle specifiche normative in materia di igiene e sicurezza sul lavoro ed in particolare a ciò che prevede il Titolo IV del D.lgs. n.° 81/2008 e s.m.i."
      },
      {
        id: "tenuta_11",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantita sempre l'incolumità di terzi"
      },
      {
        id: "tenuta_12",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È a disposizione presso il cantiere tutta la documentazione di sicurezza (PSC, POS, Pi.M.U.S, libretti, ecc.)"
      },
      {
        id: "tenuta_13",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantito il collegamento di messa a terra e di protezione contro le scariche atmosferiche delle masse metalliche di cantiere che lo necessitino"
      },
      {
        id: "tenuta_14",
        categoria: "Organizzazione e tenuta del Cantiere",
        domanda: "È garantito, nell'esecuzione dei getti, che: l'autobetoniera sia correttamente posizionata e distante da cigli di scavi, che non vi siano operatori nel raggio d'azione dell'autobetoniera e della pompa per il calcestruzzo"
      }
    ].map(item => ({ ...item, risposta: "non_pertinente" as const }));
    setChecklistItems(checklistItems);
    setCurrentStep("checklist");
  };

  const updateChecklistItem = (itemId: string, field: keyof ChecklistItem, value: any) => {
    setChecklistItems(prev => 
      prev.map(item => {
        if (item.id === itemId) {
          const updatedItem = { ...item, [field]: value };
          
          // Se cambia la risposta e non è più "non_conforme", pulisci note e foto
          if (field === "risposta" && value !== "non_conforme") {
            updatedItem.note = "";
            updatedItem.foto = [];
          }
          
          return updatedItem;
        }
        return item;
      })
    );
  };

  const handlePhotoUpload = async (itemId: string, file: File) => {
    try {
      const formData = new FormData();
      formData.append('photo', file);

      const response = await apiUpload('/api/upload-photo', formData);
      const { filename } = await response.json();
      
      // Aggiorna l'item aggiungendo la nuova foto all'array
      setChecklistItems(items => 
        items.map(item => 
          item.id === itemId 
            ? { 
                ...item, 
                foto: item.foto ? [...item.foto, filename] : [filename]
              }
            : item
        )
      );
      
      toast({
        title: "Foto caricata",
        description: "L'immagine è stata caricata con successo",
      });
    } catch (error) {
      toast({
        title: "Errore",
        description: "Errore durante il caricamento della foto",
        variant: "destructive",
      });
    }
  };

  const handleRemovePhoto = (itemId: string, photoIndex: number) => {
    setChecklistItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              foto: item.foto?.filter((_, index) => index !== photoIndex)
            }
          : item
      )
    );
  };

  const addPersona = () => {
    const newId = (personePresenti.length + 1).toString();
    setPersonePresenti([...personePresenti, { id: newId, nome: "", qualifica: "" }]);
  };

  const removePersona = (id: string) => {
    setPersonePresenti(personePresenti.filter(persona => persona.id !== id));
  };

  const updatePersona = (id: string, field: 'nome' | 'qualifica', value: string) => {
    setPersonePresenti(prevPersone => 
      prevPersone.map(persona => 
        persona.id === id ? { ...persona, [field]: value } : persona
      )
    );
  };

  const handleFirmeComplete = async (firme: Record<string, string>) => {
    const formData = form.getValues();
    const numeroSopralluogo = generateNumeroSopralluogo(formData.data);
    
    const personeValide = personePresenti.filter(persona => 
      persona.nome.trim() !== "" && persona.qualifica.trim() !== ""
    );

    // Salva le nuove persone nel database per futuri sopralluoghi
    await Promise.all(
      personeValide.map(async (persona) => {
        const esisteGia = personeSalvate.some(p => 
          p.nome && persona.nome && 
          p.qualifica && persona.qualifica &&
          p.nome.toLowerCase() === persona.nome.toLowerCase() && 
          p.qualifica.toLowerCase() === persona.qualifica.toLowerCase()
        );
        
        if (!esisteGia) {
          try {
            await apiRequest("POST", `/api/cantieri/${cantiere.id}/persone-presenti`, {
              nome: persona.nome,
              qualifica: persona.qualifica
            });
          } catch (error) {
            console.error("Errore salvando persona:", error);
          }
        }
      })
    );
    
    // Invalida la cache per ricaricare l'elenco persone presenti
    queryClient.invalidateQueries({ 
      queryKey: [`/api/cantieri/${cantiere.id}/persone-presenti`] 
    });
    
    const sopralluogoData: InsertSopralluogo & { 
      checklist: ChecklistItem[];
      personePresenti: { nome: string; qualifica: string; id: string; }[];
      firme: Record<string, string>;
    } = {
      numeroSopralluogo: numeroSopralluogo,
      data: formData.data,
      cantiereId: cantiere.id,
      tecnicoId: parseInt(formData.tecnicoId),
      note: formData.note || null,
      lavorazioniInCorso: formData.lavorazioniInCorso || null,
      statoSopralluogo: "Completato",
      verbaleUrl: null,
      checklist: checklistItems,
      personePresenti: personeValide,
      firme: firme,
    };

    createSopralluogoConFirmeMutation.mutate(sopralluogoData);
  };

  const handleSalvaBozza = async () => {
    const formData = form.getValues();
    const numeroSopralluogo = sopralluogoInModifica?.numeroSopralluogo || generateNumeroSopralluogo(formData.data);
    
    // Filtra solo le persone con dati completi
    const personeValide = personePresenti.filter(persona => 
      persona.nome.trim() !== "" && persona.qualifica.trim() !== ""
    );

    // Salva le nuove persone nel database per futuri sopralluoghi
    await Promise.all(
      personeValide.map(async (persona) => {
        const esisteGia = personeSalvate.some(p => 
          p.nome && persona.nome && 
          p.qualifica && persona.qualifica &&
          p.nome.toLowerCase() === persona.nome.toLowerCase() && 
          p.qualifica.toLowerCase() === persona.qualifica.toLowerCase()
        );
        
        if (!esisteGia) {
          try {
            await apiRequest("POST", `/api/cantieri/${cantiere.id}/persone-presenti`, {
              nome: persona.nome,
              qualifica: persona.qualifica
            });
          } catch (error) {
            console.error("Errore salvando persona:", error);
          }
        }
      })
    );
    
    const sopralluogoData: InsertSopralluogo & { 
      checklist: ChecklistItem[];
      personePresenti: PersonaPresenteLocal[];
    } = {
      numeroSopralluogo: numeroSopralluogo,
      data: formData.data,
      cantiereId: cantiere.id,
      tecnicoId: parseInt(formData.tecnicoId),
      note: formData.note || null,
      lavorazioniInCorso: formData.lavorazioniInCorso || null,
      statoSopralluogo: "In corso",
      verbaleUrl: null,
      stato: "bozza",
      checklistData: JSON.stringify(checklistItems),
      personePresentiData: JSON.stringify(personeValide),
      currentStep: currentStep,
      checklist: checklistItems,
      personePresenti: personeValide,
    };

    salvaBozzaMutation.mutate(sopralluogoData);
  };

  const handleFirmaVerbale = () => {
    // Validazione: tutti i punti "non conforme" devono avere un commento
    const nonConformiSenzaNote = checklistItems.filter(
      item => item.risposta === "non_conforme" && (!item.note || item.note.trim() === "")
    );

    if (nonConformiSenzaNote.length > 0) {
      toast({
        title: "Errore",
        description: `${nonConformiSenzaNote.length} punto/i non conforme senza note. Per favore aggiungi i commenti necessari.`,
        variant: "destructive",
      });
      return;
    }

    // Apri il modal per le firme
    setIsFirmaModalOpen(true);
  };

  const handleFinalSubmit = async () => {
    // Validazione: tutti i punti "non conforme" devono avere un commento
    const nonConformiSenzaNote = checklistItems.filter(
      item => item.risposta === "non_conforme" && (!item.note || item.note.trim() === "")
    );

    if (nonConformiSenzaNote.length > 0) {
      toast({
        title: "Validazione fallita",
        description: `Tutti i punti "Non Conforme" devono avere un commento. Mancano ${nonConformiSenzaNote.length} commenti.`,
        variant: "destructive",
      });
      return;
    }

    const formData = form.getValues();
    const numeroSopralluogo = generateNumeroSopralluogo(formData.data);
    
    // Filtra solo le persone con dati completi
    const personeValide = personePresenti.filter(persona => 
      persona.nome.trim() !== "" && persona.qualifica.trim() !== ""
    );

    // Salva le nuove persone nel database per futuri sopralluoghi
    await Promise.all(
      personeValide.map(async (persona) => {
        // Controlla se la persona esiste già nel database
        const esisteGia = personeSalvate.some(p => 
          p.nome.toLowerCase() === persona.nome.toLowerCase() && 
          p.qualifica.toLowerCase() === persona.qualifica.toLowerCase()
        );
        
        if (!esisteGia) {
          try {
            await apiRequest("POST", `/api/cantieri/${cantiere.id}/persone-presenti`, {
              nome: persona.nome,
              qualifica: persona.qualifica
            });
          } catch (error) {
            console.error("Errore salvando persona:", error);
          }
        }
      })
    );
    
    const sopralluogoData: InsertSopralluogo & { 
      checklist: ChecklistItem[];
      personePresenti: { nome: string; qualifica: string; id: string; }[];
    } = {
      numeroSopralluogo: numeroSopralluogo,
      data: formData.data,
      cantiereId: cantiere.id,
      tecnicoId: parseInt(formData.tecnicoId),
      note: formData.note || null,
      statoSopralluogo: "Completato",
      verbaleUrl: null, // Verrà generato il PDF
      checklist: checklistItems,
      personePresenti: personeValide,
    };

    createSopralluogoMutation.mutate(sopralluogoData);
  };

  const getRispostaColor = (risposta: string) => {
    switch (risposta) {
      case "conforme":
        return "text-green-600";
      case "non_conforme":
        return "text-red-600";
      case "non_pertinente":
        return "text-gray-600";
      default:
        return "text-gray-400";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {sopralluogoInModifica ? "Modifica Sopralluogo" : "Nuovo Sopralluogo"} - {cantiere.codiceCommessa}
          </DialogTitle>
        </DialogHeader>
        
        {currentStep === "info" && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleInfoSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <FormLabel>ID Sopralluogo *</FormLabel>
                  <div className="mt-2">
                    <Input 
                      value={sopralluogoInModifica?.numeroSopralluogo || (form.watch("data") ? generateNumeroSopralluogo(form.watch("data")) : `${cantiere.codiceCommessa}-CSE-XXX-AAAAMMGG`)}
                      disabled
                      className="bg-gray-50 text-gray-600"
                    />
                    <p className="text-xs text-gray-500 mt-1">Generato automaticamente dalla data</p>
                  </div>
                </div>
                
                <FormField
                  control={form.control}
                  name="data"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data Sopralluogo *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="tecnicoId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tecnico Incaricato *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleziona un tecnico" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {tecnici.map((tecnico) => (
                          <SelectItem key={tecnico.id} value={tecnico.id.toString()}>
                            {tecnico.nome} {tecnico.cognome} - {tecnico.specializzazione}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Note Generali</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Note aggiuntive sul sopralluogo..."
                        className="resize-none"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lavorazioniInCorso"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lavorazioni in Corso *</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Descrivi le lavorazioni attualmente in corso nel cantiere..."
                        className="resize-none"
                        rows={4}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-2 pt-4">
                <Button type="button" variant="outline" onClick={onClose}>
                  Annulla
                </Button>
                <Button type="submit">
                  Procedi alle Persone Presenti
                </Button>
              </div>
            </form>
          </Form>
        )}

        {currentStep === "persone" && (
          <div className="space-y-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">
                Persone Presenti al Sopralluogo
              </h3>
              <p className="text-sm text-blue-700">
                Inserisci le persone presenti al sopralluogo con nome completo e qualifica
              </p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Elenco Persone Presenti
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={addPersona}
                    className="ml-2"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Aggiungi Persona
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {personePresenti.map((persona, index) => (
                  <div key={persona.id} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-gray-200 rounded-lg">
                    <div>
                      <Label className="text-sm font-medium mb-2 block">
                        Seleziona o Inserisci Nome
                      </Label>
                      {personeSalvate.length > 0 ? (
                        <Select onValueChange={(value) => {
                          const newPersoneSelezionate = new Set(personeSelezionate);
                          if (value === "nuovo") {
                            updatePersona(persona.id, "nome", "");
                            updatePersona(persona.id, "qualifica", "");
                            newPersoneSelezionate.delete(persona.id);
                          } else {
                            const personaSalvata = personeSalvate.find(p => p.id.toString() === value);
                            if (personaSalvata) {
                              updatePersona(persona.id, "nome", personaSalvata.nome);
                              updatePersona(persona.id, "qualifica", personaSalvata.qualifica);
                              newPersoneSelezionate.add(persona.id);
                            }
                          }
                          setPersoneSelezionate(newPersoneSelezionate);
                        }}>
                          <SelectTrigger className="text-sm">
                            <SelectValue placeholder="Seleziona persona o inserisci nuovo" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="nuovo">Inserisci nuovo...</SelectItem>
                            {personeSalvate
                              .filter(personaSalvata => {
                                // Controlla se questa persona è già stata selezionata in un'altra riga
                                return !personePresenti.some(p => 
                                  p.id !== persona.id && 
                                  p.nome === personaSalvata.nome && 
                                  p.qualifica === personaSalvata.qualifica
                                );
                              })
                              .map((personaSalvata) => (
                                <SelectItem key={personaSalvata.id} value={personaSalvata.id.toString()}>
                                  {personaSalvata.nome || 'Nome non disponibile'} - {personaSalvata.qualifica || 'Qualifica non disponibile'}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      ) : null}
                      <Input
                        placeholder="Es. Mario Rossi"
                        value={persona.nome}
                        onChange={(e) => updatePersona(persona.id, "nome", e.target.value)}
                        className="text-sm mt-2"
                        disabled={personeSelezionate.has(persona.id)}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium mb-2 block">
                        Qualifica
                      </Label>
                      <Input
                        placeholder="Es. Direttore Lavori, Operaio, ecc."
                        value={persona.qualifica}
                        onChange={(e) => updatePersona(persona.id, "qualifica", e.target.value)}
                        className="text-sm"
                        disabled={personeSelezionate.has(persona.id)}
                      />
                    </div>
                    <div className="flex items-end gap-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removePersona(persona.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <div className="flex justify-between pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setCurrentStep("info")}
              >
                Indietro
              </Button>
              <div className="flex space-x-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Annulla
                </Button>
                <Button onClick={handlePersoneSubmit}>
                  Procedi alla Checklist
                </Button>
              </div>
            </div>
          </div>
        )}

        {currentStep === "checklist" && (
          <div className="space-y-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">
                Informazioni Sopralluogo
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Numero:</span> {generateNumeroSopralluogo(form.getValues("data"))}
                </div>
                <div>
                  <span className="font-medium">Data:</span> {new Date(form.getValues("data")).toLocaleDateString("it-IT")}
                </div>
                <div>
                  <span className="font-medium">Tecnico:</span> {
                    tecnici.find(t => t.id.toString() === form.getValues("tecnicoId"))?.nome
                  } {
                    tecnici.find(t => t.id.toString() === form.getValues("tecnicoId"))?.cognome
                  }
                </div>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Checklist di Sicurezza</CardTitle>
                <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-800 font-medium mb-1">Legenda:</p>
                  <div className="flex flex-wrap gap-4 text-sm text-blue-700">
                    <span><strong>C</strong> = Conforme</span>
                    <span><strong>NC</strong> = Non Conforme</span>
                    <span><strong>NP</strong> = Non Pertinente</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {checklistItems.length === 0 ? (
                  <div className="text-center py-12 text-gray-500">
                    La checklist è pronta per ricevere gli elementi che mi fornirai.
                  </div>
                ) : (
                  <ScrollArea className="h-96">
                    <Accordion type="multiple" className="w-full">
                      {/* Raggruppamento per categoria con accordion */}
                      {Array.from(new Set(checklistItems.map(item => item.categoria))).map((categoria, index) => {
                        const categoriaInfo = categorieConRiferimenti.find(cat => cat.nome === categoria);
                        const categoriaItems = checklistItems.filter(item => item.categoria === categoria);
                        
                        // Conta le risposte per categoria per mostrare il progresso dettagliato
                        const conformiCount = categoriaItems.filter(item => item.risposta === "conforme").length;
                        const nonConformiCount = categoriaItems.filter(item => item.risposta === "non_conforme").length;
                        const nonPertinentiCount = categoriaItems.filter(item => item.risposta === "non_pertinente").length;
                        
                        return (
                          <AccordionItem key={categoria} value={`categoria-${index}`} className="border rounded-lg mb-2">
                            <AccordionTrigger className="px-4 py-3 hover:no-underline">
                              <div className="flex flex-col items-start text-left w-full">
                                <h4 className="font-semibold text-lg text-gray-800 mb-1">
                                  {categoria}
                                </h4>
                                {categoriaInfo && (
                                  <p className="text-sm text-gray-600 italic mb-2">
                                    {categoriaInfo.riferimentoNormativo}
                                  </p>
                                )}
                                <div className="flex items-center gap-2 flex-wrap">
                                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                    {conformiCount} conformi
                                  </Badge>
                                  <Badge variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">
                                    {nonConformiCount} non conformi
                                  </Badge>
                                  <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700 border-gray-200">
                                    {nonPertinentiCount} non pertinenti
                                  </Badge>
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 pb-4">
                              <div className="space-y-4">
                                {categoriaItems.map(item => (
                                <div key={item.id} className="border rounded-lg p-4 bg-gray-50">
                                  <div className="mb-3">
                                    <Label className="text-sm font-medium">
                                      {item.domanda}
                                    </Label>
                                  </div>
                                  
                                  <div className="flex flex-row space-x-3 mb-4">
                                    <Button
                                      type="button"
                                      variant={item.risposta === "conforme" ? "default" : "outline"}
                                      size="sm"
                                      className={`${
                                        item.risposta === "conforme" 
                                          ? "bg-green-600 hover:bg-green-700 text-white" 
                                          : "border-green-600 text-green-600 hover:bg-green-50"
                                      }`}
                                      onClick={() => updateChecklistItem(item.id, "risposta", "conforme")}
                                    >
                                      Conforme
                                    </Button>
                                    <Button
                                      type="button"
                                      variant={item.risposta === "non_conforme" ? "default" : "outline"}
                                      size="sm"
                                      className={`${
                                        item.risposta === "non_conforme" 
                                          ? "bg-red-600 hover:bg-red-700 text-white" 
                                          : "border-red-600 text-red-600 hover:bg-red-50"
                                      }`}
                                      onClick={() => updateChecklistItem(item.id, "risposta", "non_conforme")}
                                    >
                                      Non Conforme
                                    </Button>
                                    <Button
                                      type="button"
                                      variant={item.risposta === "non_pertinente" ? "default" : "outline"}
                                      size="sm"
                                      className={`${
                                        item.risposta === "non_pertinente" 
                                          ? "bg-gray-600 hover:bg-gray-700 text-white" 
                                          : "border-gray-600 text-gray-600 hover:bg-gray-50"
                                      }`}
                                      onClick={() => updateChecklistItem(item.id, "risposta", "non_pertinente")}
                                    >
                                      Non Pertinente
                                    </Button>
                                  </div>

                                  {/* Note e Foto - Solo per Non Conforme */}
                                  {item.risposta === "non_conforme" && (
                                    <div className="space-y-3 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                      <div>
                                        <Label className="text-sm font-medium text-red-900 mb-2 block">
                                          Commento obbligatorio per Non Conforme
                                        </Label>
                                        <Textarea
                                          placeholder="Descrivi il problema rilevato e le azioni correttive necessarie..."
                                          value={item.note || ""}
                                          onChange={(e) => updateChecklistItem(item.id, "note", e.target.value)}
                                          className="text-sm border-red-300 focus:border-red-500"
                                          rows={3}
                                          required
                                        />
                                      </div>
                                      
                                      <div>
                                        <Label className="text-sm font-medium text-red-900 mb-2 block">
                                          Foto (Opzionale)
                                        </Label>
                                        <div className="space-y-2">
                                          {/* Foto esistenti */}
                                          {item.foto && item.foto.length > 0 && (
                                            <div className="flex flex-wrap gap-2">
                                              {item.foto.map((foto, index) => (
                                                <div key={index} className="relative">
                                                  <Badge variant="secondary" className="text-xs pr-6">
                                                    Foto {index + 1}
                                                  </Badge>
                                                  <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute -top-1 -right-1 h-4 w-4 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white"
                                                    onClick={() => handleRemovePhoto(item.id, index)}
                                                  >
                                                    ×
                                                  </Button>
                                                </div>
                                              ))}
                                            </div>
                                          )}
                                          
                                          {/* Caricamento nuova foto */}
                                          <div className="flex items-center space-x-2">
                                            <Input
                                              id={`photo-upload-${item.id}`}
                                              type="file"
                                              accept="image/*"
                                              onChange={(e) => {
                                                const file = e.target.files?.[0];
                                                if (file) {
                                                  handlePhotoUpload(item.id, file);
                                                  e.target.value = ''; // Reset input per permettere ricaricamento stesso file
                                                }
                                              }}
                                              className="text-sm flex-1"
                                            />
                                            <Button
                                              type="button"
                                              variant="outline"
                                              size="sm"
                                              className="px-3"
                                              onClick={() => {
                                                const input = document.getElementById(`photo-upload-${item.id}`) as HTMLInputElement;
                                                input?.click();
                                              }}
                                            >
                                              +
                                            </Button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                                ))}
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        );
                      })}
                    </Accordion>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-between pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setCurrentStep("persone")}
              >
                Torna Indietro
              </Button>
              <div className="space-x-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Annulla
                </Button>
                <Button 
                  onClick={handleSalvaBozza}
                  disabled={salvaBozzaMutation.isPending}
                  variant="outline"
                  className="border-orange-500 text-orange-600 hover:bg-orange-50"
                >
                  {salvaBozzaMutation.isPending ? "Salvando..." : "Salva"}
                </Button>
                <Button 
                  onClick={handleFirmaVerbale}
                  disabled={createSopralluogoConFirmeMutation.isPending}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {createSopralluogoConFirmeMutation.isPending ? "Firmando..." : "Firma"}
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>

      {/* Modal per le firme */}
      <FirmaVerbaleModal
        isOpen={isFirmaModalOpen}
        onClose={() => setIsFirmaModalOpen(false)}
        tecnico={tecnici.find(t => t.id === parseInt(form.getValues().tecnicoId || "0")) || tecnici[0]}
        personePresenti={personePresenti.filter(p => p.nome.trim() !== "")}
        onFirmeComplete={handleFirmeComplete}
      />
    </Dialog>
  );
}