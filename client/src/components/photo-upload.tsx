import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Camera, Upload, X, Download } from "lucide-react";
import { convertFileToBase64, savePhotoOffline, isOnline } from "@/lib/offline-storage/";
import { useImageUrl } from "@/hooks/useImageUrl";
import { apiUpload } from "@/lib/queryClient";

interface PhotoUploadProps {
  value?: string[];
  onChange: (photos: string[]) => void;
  disabled?: boolean;
  title: string;
  verbaleId?: string; // For offline photo storage
}

export default function PhotoUpload({ 
  value = [], 
  onChange, 
  disabled = false, 
  title,
  verbaleId
}: PhotoUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { getImageUrl } = useImageUrl();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    
    try {
      const newPhotos: string[] = [];
      let failedUploads = 0;
      const deviceOnline = navigator.onLine;
      const hookOnline = isOnline();
      const isOffline = !deviceOnline || !hookOnline;
      
      console.log('Photo upload mode:', { isOffline, deviceOnline, hookOnline });
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Validate file type
        if (!file.type.startsWith('image/')) {
          continue;
        }
        
        if (isOffline) {
          // Offline mode: convert to base64 and save locally
          const base64Data = await convertFileToBase64(file);
          const filename = `offline_${Date.now()}_${i}_${file.name}`;
          
          if (verbaleId) {
            savePhotoOffline(verbaleId, filename, base64Data);
          }
          
          newPhotos.push(filename);
          console.log('Foto salvata offline:', { filename, size: base64Data.length });
        } else {
          // Online mode: upload to server
          const formData = new FormData();
          formData.append('photo', file);
          
          // Upload using centralized apiUpload (includes JWT automatically)
          console.log(`Upload foto ${i + 1}/${files.length} - Using apiUpload`);
          
          try {
            const response = await apiUpload('/api/upload-photo', formData);
            const result = await response.json();
            newPhotos.push(result.filename);
            console.log(`Foto ${i + 1} caricata online:`, result.filename);
          } catch (error) {
            console.error(`Errore upload foto ${i + 1}:`, error);
            if (error.message.includes('401')) {
              // Session error should still break the entire process
              throw new Error('Sessione scaduta. Effettua nuovamente il login.');
            }
            // For other errors, log but continue with next photo
            console.warn(`Foto ${i + 1} (${file.name}) non caricata, continuo con le altre`);
            failedUploads++;
          }
        }
      }
      
      // Update the photos array
      onChange([...value, ...newPhotos]);
      
      // Log summary
      const totalFiles = files.length;
      const successfulUploads = newPhotos.length;
      console.log(`Upload completato: ${successfulUploads}/${totalFiles} foto caricate con successo`);
      
      if (failedUploads > 0) {
        console.warn(`⚠️ ${failedUploads} foto non sono state caricate a causa di errori`);
      }
      
    } catch (error) {
      console.error('Error uploading photos:', error);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removePhoto = (index: number) => {
    const newPhotos = value.filter((_, i) => i !== index);
    onChange(newPhotos);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">{title} - Fotografie</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!disabled && (
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={openFileDialog}
              disabled={isUploading}
            >
              <Camera className="h-4 w-4 mr-1" />
              {isUploading ? 'Caricamento...' : 'Aggiungi Foto'}
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
        )}
        
        {value.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {value.map((photo, index) => (
              <div key={index} className="relative group">
                <img
                  src={getImageUrl('photos', photo)}
                  alt={`Foto ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border border-gray-200"
                />
                {!disabled && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => removePhoto(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="absolute bottom-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = getImageUrl('photos', photo);
                    link.download = photo;
                    link.click();
                  }}
                >
                  <Download className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
        
        {value.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Upload className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Nessuna foto caricata</p>
          </div>
        )}
        
        <p className="text-xs text-gray-500">
          Formati supportati: JPG, PNG, GIF. Massimo 10MB per foto.
        </p>
      </CardContent>
    </Card>
  );
}