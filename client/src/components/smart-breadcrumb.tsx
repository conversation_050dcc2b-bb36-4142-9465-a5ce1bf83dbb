import { Link } from "wouter";
import { ChevronRight, Home, HardHat, Briefcase, Settings, Building, FileText, Image } from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";

const iconMap = {
  Home, HardHat, Briefcase, Settings, Building, FileText, Image
};

export default function SmartBreadcrumb() {
  const { breadcrumbs } = useNavigation();

  if (breadcrumbs.length <= 1) return null;

  return (
    <nav className="flex items-center space-x-1 text-sm text-gray-500 mb-4">
      {breadcrumbs.map((crumb, index) => {
        const Icon = crumb.icon ? iconMap[crumb.icon as keyof typeof iconMap] : null;
        const isLast = index === breadcrumbs.length - 1;

        return (
          <div key={crumb.path} className="flex items-center">
            {index > 0 && <ChevronRight className="h-4 w-4 mx-2" />}
            
            {isLast ? (
              <span className="flex items-center font-medium text-gray-900">
                {Icon && <Icon className="h-4 w-4 mr-1" />}
                {crumb.label}
              </span>
            ) : (
              <Link href={crumb.path}>
                <span className="flex items-center hover:text-gray-700 cursor-pointer transition-colors">
                  {Icon && <Icon className="h-4 w-4 mr-1" />}
                  {crumb.label}
                </span>
              </Link>
            )}
          </div>
        );
      })}
    </nav>
  );
}
