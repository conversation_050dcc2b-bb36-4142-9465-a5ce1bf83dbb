import { CalendarD<PERSON>, Eye, Edit, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useLocation } from "wouter";
import type { DirezioneLavori } from "@shared/schema";

interface DirezioneLavoriTableProps {
  lavori: DirezioneLavori[];
  isLoading: boolean;
  onEdit: (lavoro: DirezioneLavori) => void;
  onDelete: (lavoro: DirezioneLavori) => void;
}

export default function DirezioneLavoriTable({ lavori, isLoading, onEdit, onDelete }: DirezioneLavoriTableProps) {
  const [, setLocation] = useLocation();

  const getStatusColor = (stato: string) => {
    switch (stato) {
      case "In corso":
        return "bg-green-100 text-green-800 border-green-200";
      case "Completato":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "Sospeso":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Non specificata";
    return new Date(dateString).toLocaleDateString("it-IT");
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Codice Commessa</TableHead>
              <TableHead>Indirizzo</TableHead>
              <TableHead>Committente</TableHead>
              <TableHead>Oggetto</TableHead>
              <TableHead>Stato</TableHead>
              <TableHead>Azioni</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 3 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                <TableCell><div className="h-6 bg-gray-200 rounded animate-pulse"></div></TableCell>
                <TableCell><div className="h-8 bg-gray-200 rounded animate-pulse"></div></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
              Codice Commessa
            </TableHead>
            <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
              Indirizzo
            </TableHead>
            <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
              Committente
            </TableHead>
            <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
              Oggetto
            </TableHead>

            <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
              Stato
            </TableHead>
            <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
              Azioni
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {lavori.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-12">
                <div className="text-gray-500">
                  <p className="text-lg font-medium">Nessuna commessa trovata</p>
                  <p className="text-sm mt-2">Aggiungi una nuova commessa per iniziare.</p>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            lavori.map((lavoro) => (
              <TableRow key={lavoro.id} className="hover:bg-gray-50">
                <TableCell className="font-medium">{lavoro.codiceCommessa}</TableCell>
                <TableCell>{lavoro.indirizzo}</TableCell>
                <TableCell>{lavoro.committente}</TableCell>
                <TableCell className="max-w-xs truncate">{lavoro.oggetto}</TableCell>
                <TableCell>
                  <Badge className={getStatusColor(lavoro.stato)} variant="secondary">
                    {lavoro.stato}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-blue-600 hover:text-blue-800"
                      onClick={() => setLocation(`/direzione-lavori/${lavoro.id}`)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-yellow-600 hover:text-yellow-800"
                      onClick={() => onEdit(lavoro)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}