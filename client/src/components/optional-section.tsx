import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Trash2 } from "lucide-react";
import MillimetricDrawing from "./millimetric-drawing";
import PhotoUpload from "./photo-upload";

export interface OptionalSectionItem {
  id: string;
  text: string;
  drawing: string;
  photos: string[];
}

interface OptionalSectionProps {
  title: string;
  isEnabled: boolean;
  onEnabledChange: (enabled: boolean) => void;
  items: OptionalSectionItem[];
  onItemsChange: (items: OptionalSectionItem[]) => void;
  disabled?: boolean;
  textPlaceholder?: string;
  verbaleId?: string; // For offline photo storage
}

export default function OptionalSection({
  title,
  isEnabled,
  onEnabledChange,
  items,
  onItemsChange,
  disabled = false,
  textPlaceholder = "Inserisci note...",
  verbaleId
}: OptionalSectionProps) {

  const addNewItem = () => {
    const newItem: OptionalSectionItem = {
      id: Date.now().toString(),
      text: "",
      drawing: "",
      photos: []
    };
    onItemsChange([...items, newItem]);
  };

  const removeItem = (itemId: string) => {
    onItemsChange(items.filter(item => item.id !== itemId));
  };

  const updateItem = (itemId: string, field: keyof OptionalSectionItem, value: any) => {
    onItemsChange(items.map(item => 
      item.id === itemId ? { ...item, [field]: value } : item
    ));
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Checkbox
              id={`enable-${title.replace(/\s+/g, '-').toLowerCase()}`}
              checked={isEnabled}
              onCheckedChange={onEnabledChange}
              disabled={disabled}
            />
            <Label
              htmlFor={`enable-${title.replace(/\s+/g, '-').toLowerCase()}`}
              className="text-lg font-semibold cursor-pointer"
            >
              {title}
            </Label>
          </div>
          
          {isEnabled && !disabled && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addNewItem}
              className="flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Aggiungi punto</span>
            </Button>
          )}
        </div>
      </CardHeader>
      
      {isEnabled && !disabled && (
        <CardContent className="space-y-6">
          {items.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>Nessun punto aggiunto. Clicca "Aggiungi punto" per iniziare.</p>
            </div>
          )}
          
          {items.map((item, index) => (
            <div key={item.id} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Punto {index + 1}</h4>
                {items.length > 0 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeItem(item.id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
              
              <Tabs defaultValue="text">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="text">Testo</TabsTrigger>
                  <TabsTrigger value="drawing">Disegno</TabsTrigger>
                  <TabsTrigger value="photos">Foto</TabsTrigger>
                </TabsList>
                
                <TabsContent value="text" className="mt-4">
                  <div className="space-y-2">
                    <Label htmlFor={`text-${item.id}`}>
                      Descrizione
                    </Label>
                    <Textarea
                      id={`text-${item.id}`}
                      placeholder={textPlaceholder}
                      value={item.text}
                      onChange={(e) => updateItem(item.id, 'text', e.target.value)}
                      rows={4}
                      className="resize-none"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="drawing" className="mt-4">
                  <div className="space-y-2">
                    <Label>Disegno millimetrato</Label>
                    <MillimetricDrawing
                      value={item.drawing}
                      onChange={(drawing) => updateItem(item.id, 'drawing', drawing)}
                      disabled={disabled}
                      title={`${title} - Punto ${index + 1}`}
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="photos" className="mt-4">
                  <div className="space-y-2">
                    <Label>Foto di supporto</Label>
                    <PhotoUpload
                      value={item.photos}
                      onChange={(photos) => updateItem(item.id, 'photos', photos)}
                      disabled={disabled}
                      title={`${title} - Punto ${index + 1}`}
                      verbaleId={verbaleId}
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          ))}
        </CardContent>
      )}
    </Card>
  );
}