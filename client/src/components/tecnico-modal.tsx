import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { insertTecnicoSchema, type Tecnico, type InsertTecnico } from "@shared/schema";

interface TecnicoModalProps {
  isOpen: boolean;
  onClose: () => void;
  tecnico: Tecnico | null;
  title: string;
}

const formSchema = insertTecnicoSchema.extend({
  telefono: insertTecnicoSchema.shape.telefono.nullable().optional(),
  email: insertTecnicoSchema.shape.email.nullable().optional(),
});

export default function TecnicoModal({ isOpen, onClose, tecnico, title }: TecnicoModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<InsertTecnico>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome: "",
      cognome: "",
      specializzazione: "",
      telefono: null,
      email: null,
      attivo: "true",
    },
  });

  // Update form when tecnico changes (for edit mode)
  useEffect(() => {
    if (tecnico) {
      form.reset({
        nome: tecnico.nome,
        cognome: tecnico.cognome,
        specializzazione: tecnico.specializzazione,
        telefono: tecnico.telefono,
        email: tecnico.email,
        attivo: tecnico.attivo,
      });
    } else {
      form.reset({
        nome: "",
        cognome: "",
        specializzazione: "",
        telefono: null,
        email: null,
        attivo: "true",
      });
    }
  }, [tecnico, form]);

  const createMutation = useMutation({
    mutationFn: async (data: InsertTecnico) => {
      const response = await apiRequest("POST", "/api/tecnici", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/tecnici"] });
      toast({
        title: "Successo",
        description: "Tecnico creato con successo.",
      });
      onClose();
      form.reset();
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Errore durante la creazione del tecnico.",
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (data: InsertTecnico) => {
      const response = await apiRequest("PUT", `/api/tecnici/${tecnico!.id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/tecnici"] });
      toast({
        title: "Successo",
        description: "Tecnico aggiornato con successo.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Errore durante l'aggiornamento del tecnico.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: InsertTecnico) => {
    if (tecnico) {
      updateMutation.mutate(data);
    } else {
      createMutation.mutate(data);
    }
  };

  const isPending = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {title}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="nome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nome <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Nome del tecnico" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cognome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Cognome <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Cognome del tecnico" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="specializzazione"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Specializzazione <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Es. Coordinatore per la sicurezza in esecuzione" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="telefono"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telefono</FormLabel>
                    <FormControl>
                      <Input placeholder="Numero di telefono" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Indirizzo email" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="attivo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stato</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleziona stato" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Attivo</SelectItem>
                      <SelectItem value="false">Inattivo</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 sm:space-y-0 space-y-reverse">
              <Button type="button" variant="outline" onClick={onClose}>
                Annulla
              </Button>
              <Button type="submit" disabled={isPending} className="bg-primary hover:bg-primary/90">
                {isPending ? "Salvando..." : tecnico ? "Aggiorna Tecnico" : "Crea Tecnico"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}