import { useOffline } from '@/hooks/useOffline';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { WifiOff, Wifi, Download, RefreshCw, Clock } from 'lucide-react';

interface OfflineIndicatorProps {
  inline?: boolean;
}

export default function OfflineIndicator({ inline = false }: OfflineIndicatorProps) {
  const { isOnline, isInstallable, hasOfflineActions, installPWA, syncOfflineActions, toggleOfflineMode, isOfflineForced } = useOffline();

  // Determina lo stato di visualizzazione
  const isReallyOnline = navigator.onLine;
  const displayState = isOfflineForced ? 'forced-offline' : (isReallyOnline ? 'online' : 'offline');

  if (inline) {
    // Versione per navbar - cliccabile per toggle
    const getButtonStyle = () => {
      switch (displayState) {
        case 'online':
          return 'text-green-700 hover:bg-green-50';
        case 'offline':
          return 'text-red-700 hover:bg-red-50';
        case 'forced-offline':
          return 'text-orange-700 hover:bg-orange-50 bg-orange-100';
        default:
          return 'text-gray-700 hover:bg-gray-100';
      }
    };

    const getDisplayText = () => {
      switch (displayState) {
        case 'online':
          return { icon: Wifi, text: 'Online' };
        case 'offline':
          return { icon: WifiOff, text: 'Offline' };
        case 'forced-offline':
          return { icon: WifiOff, text: 'Offline Forzato' };
        default:
          return { icon: WifiOff, text: 'Sconosciuto' };
      }
    };

    const { icon: Icon, text } = getDisplayText();

    return (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleOfflineMode}
          className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium transition-colors ${getButtonStyle()}`}
          title={isOfflineForced ? "Clicca per tornare online" : "Clicca per forzare modalità offline"}
        >
          <Icon className="h-3 w-3" />
          <span>{text}</span>
        </Button>

        {/* Indicatore azioni pending */}
        {hasOfflineActions && (
          <Button
            variant="ghost"
            size="sm"
            onClick={syncOfflineActions}
            className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium text-orange-700 hover:bg-orange-50 bg-orange-100"
            title="Clicca per sincronizzare azioni in coda"
          >
            <RefreshCw className="h-3 w-3" />
            <span>Sync</span>
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="fixed top-20 right-4 z-40 space-y-2">
      {/* Status Indicator */}
      <Card className={`cursor-pointer transition-colors ${
        displayState === 'online' ? 'bg-green-50 border-green-200 hover:bg-green-100' :
        displayState === 'offline' ? 'bg-red-50 border-red-200 hover:bg-red-100' :
        'bg-orange-50 border-orange-200 hover:bg-orange-100'
      }`} onClick={toggleOfflineMode}>
        <CardContent className="p-3">
          <div className="flex items-center space-x-2">
            {displayState === 'online' ? (
              <>
                <Wifi className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Online</span>
              </>
            ) : displayState === 'offline' ? (
              <>
                <WifiOff className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">Offline</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">Offline Forzato</span>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* PWA Install Button */}
      {isInstallable && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-3">
            <Button
              onClick={installPWA}
              size="sm"
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Installa App
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Offline Actions Indicator */}
      {hasOfflineActions && (
        <Card className="bg-orange-50 border-orange-200">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">Azioni in coda</span>
              </div>
              {isOnline && (
                <Button
                  onClick={syncOfflineActions}
                  size="sm"
                  variant="outline"
                  className="ml-2"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}