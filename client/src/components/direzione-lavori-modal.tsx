import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { X, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, apiUpload } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { insertDirezioneLavoriSchema, type DirezioneLavori, type InsertDirezioneLavori, type DirettoreLavori } from "@shared/schema";
import { useImageUrl } from "@/hooks/useImageUrl";

interface DirettoreLavoriRow {
  id: string;
  nome: string;
  cognome: string;
  qualifica: string;
  email?: string;
}

interface DirezioneLavoriModalProps {
  isOpen: boolean;
  onClose: () => void;
  lavoro: DirezioneLavori | null;
  title: string;
}

export default function DirezioneLavoriModal({ isOpen, onClose, lavoro, title }: DirezioneLavoriModalProps) {
  const { toast } = useToast();
  const { addTokenToUrl } = useImageUrl();
  const [direttoriRows, setDirettoriRows] = useState<DirettoreLavoriRow[]>([
    { id: "1", nome: "", cognome: "", qualifica: "", email: "" }
  ]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Fetch existing directors when editing
  const { data: existingDirettori = [], refetch: refetchDirettori } = useQuery<DirettoreLavori[]>({
    queryKey: [`/api/direzione-lavori/${lavoro?.id}/direttori`],
    enabled: !!lavoro?.id && isOpen,
  });

  // Initialize only once when modal opens
  useEffect(() => {
    if (isOpen && !isInitialized) {
      if (lavoro && existingDirettori.length > 0) {
        // Editing existing lavoro with directors
        const mappedDirectors = existingDirettori.map((dir: DirettoreLavori, index: number) => ({
          id: (index + 1).toString(),
          nome: dir.nome,
          cognome: dir.cognome,
          qualifica: dir.qualifica,
          email: dir.email || "",
        }));
        setDirettoriRows(mappedDirectors);
      } else {
        // New creation or no existing directors
        setDirettoriRows([{ id: "1", nome: "", cognome: "", qualifica: "", email: "" }]);
      }
      setIsInitialized(true);
    }
  }, [isOpen, lavoro, existingDirettori, isInitialized]);

  // Reset initialization when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsInitialized(false);
    }
  }, [isOpen]);

  const form = useForm<InsertDirezioneLavori>({
    resolver: zodResolver(insertDirezioneLavoriSchema),
    defaultValues: {
      codiceCommessa: "",
      indirizzo: "",
      committente: "",
      oggetto: "",
      contratto: "",
      cig: "",
      dataInizio: null,
      dataFinePrevista: null,
      stato: "In pianificazione",
      logoSinistra: null,
      logoCentrale: null,
      logoDestro: null,
    },
  });

  // Update form values when lavoro changes (for editing)
  useEffect(() => {
    if (lavoro && isOpen) {
      form.reset({
        codiceCommessa: lavoro.codiceCommessa || "",
        indirizzo: lavoro.indirizzo || "",
        committente: lavoro.committente || "",
        oggetto: lavoro.oggetto || "",
        contratto: lavoro.contratto || "",
        cig: lavoro.cig || "",
        dataInizio: lavoro.dataInizio || null,
        dataFinePrevista: lavoro.dataFinePrevista || null,
        stato: lavoro.stato || "In pianificazione",
        logoSinistra: lavoro.logoSinistra || null,
        logoCentrale: lavoro.logoCentrale || null,
        logoDestro: lavoro.logoDestro || null,
      });
    } else if (!lavoro && isOpen) {
      // Reset form for new creation
      form.reset({
        codiceCommessa: "",
        indirizzo: "",
        committente: "",
        oggetto: "",
        contratto: "",
        cig: "",
        dataInizio: null,
        dataFinePrevista: null,
        stato: "In pianificazione",
        logoSinistra: null,
        logoCentrale: null,
        logoDestro: null,
      });
    }
  }, [lavoro, isOpen, form]);

  const createMutation = useMutation({
    mutationFn: async (data: { lavoriData: InsertDirezioneLavori; direttoriData: DirettoreLavoriRow[] }) => {
      const response = await apiRequest("POST", "/api/direzione-lavori", data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori"] });
      toast({
        title: "Commessa creata",
        description: "La commessa è stata creata con successo.",
      });
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante la creazione della commessa.",
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (data: InsertDirezioneLavori) => {
      const response = await apiRequest("PUT", `/api/direzione-lavori/${lavoro!.id}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori"] });
      toast({
        title: "Commessa aggiornata",
        description: "La commessa è stata aggiornata con successo.",
      });
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante l'aggiornamento della commessa.",
        variant: "destructive",
      });
    },
  });

  // Mutation for adding directors to existing commessa
  const addDirettoreMutation = useMutation({
    mutationFn: async (direttore: { nome: string; cognome: string; qualifica: string; email?: string }) => {
      const response = await apiRequest("POST", "/api/direttori-lavori", direttore);
      const newDirettore = await response.json();
      
      const assignResponse = await apiRequest("POST", `/api/direzione-lavori/${lavoro!.id}/direttori`, { direttoreLavoriId: newDirettore.id });
      
      return newDirettore;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori", lavoro?.id, "direttori"] });
      toast({
        title: "Direttore aggiunto",
        description: "Il direttore lavori è stato aggiunto con successo.",
      });
    },
  });

  const onSubmit = (data: InsertDirezioneLavori) => {
    if (lavoro) {
      updateMutation.mutate(data);
    } else {
      createMutation.mutate({ 
        lavoriData: data, 
        direttoriData: direttoriRows 
      });
    }
  };

  const addDirettoreRow = () => {
    const newId = (direttoriRows.length + 1).toString();
    setDirettoriRows([...direttoriRows, { id: newId, nome: "", cognome: "", qualifica: "", email: "" }]);
  };

  const removeDirettoreRow = (id: string) => {
    if (direttoriRows.length > 1) {
      setDirettoriRows(direttoriRows.filter(row => row.id !== id));
    }
  };

  const updateDirettoreRow = (id: string, field: keyof DirettoreLavoriRow, value: string) => {
    setDirettoriRows(prev => {
      return prev.map(row => 
        row.id === id ? { ...row, [field]: value } : row
      );
    });
  };

  const saveDirettori = async () => {
    if (!lavoro) return;
    
    try {
      const validRows = direttoriRows.filter(row => row.nome && row.cognome && row.qualifica);
      
      // Find directors to remove (existing directors not in current rows)
      const directorsToRemove = existingDirettori.filter((existing: DirettoreLavori) => 
        !validRows.some(row => 
          row.nome === existing.nome && 
          row.cognome === existing.cognome && 
          row.qualifica === existing.qualifica &&
          (row.email || "") === (existing.email || "")
        )
      );
      
      // Find directors to add (current rows not in existing directors)
      const directorsToAdd = validRows.filter(row => 
        !existingDirettori.some((existing: DirettoreLavori) => 
          existing.nome === row.nome && 
          existing.cognome === row.cognome && 
          existing.qualifica === row.qualifica &&
          (existing.email || "") === (row.email || "")
        )
      );
      
      // Remove directors
      for (const directorToRemove of directorsToRemove) {
        await apiRequest("DELETE", `/api/direzione-lavori/${lavoro.id}/direttori/${directorToRemove.id}`, {});
      }
      
      // Add new directors
      for (const directorToAdd of directorsToAdd) {
        await addDirettoreMutation.mutateAsync({
          nome: directorToAdd.nome,
          cognome: directorToAdd.cognome,
          qualifica: directorToAdd.qualifica,
          email: directorToAdd.email
        });
      }
      
      // Refetch directors after saving
      await refetchDirettori();
      
      toast({
        title: "Direttori salvati",
        description: "Tutti i direttori sono stati salvati con successo.",
      });
    } catch (error) {
      console.error("Error saving directors:", error);
      toast({
        title: "Errore",
        description: "Errore durante il salvataggio dei direttori.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="codiceCommessa"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Codice Commessa</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Inserisci il codice della commessa" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="stato"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stato</FormLabel>
                    <FormControl>
                      <Select value={field.value || "In pianificazione"} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleziona lo stato" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="In pianificazione">In pianificazione</SelectItem>
                          <SelectItem value="In corso">In corso</SelectItem>
                          <SelectItem value="Sospeso">Sospeso</SelectItem>
                          <SelectItem value="Completato">Completato</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="indirizzo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Indirizzo</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Inserisci l'indirizzo del lavoro" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="committente"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Committente</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Inserisci il nome del committente" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="oggetto"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Oggetto</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Descrivi l'oggetto del lavoro" rows={3} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="contratto"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contratto</FormLabel>
                    <FormControl>
                      <Input 
                        name={field.name}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        value={field.value || ""}
                        placeholder="Numero o riferimento contratto" 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cig"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CIG</FormLabel>
                    <FormControl>
                      <Input 
                        name={field.name}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        value={field.value || ""}
                        placeholder="Codice Identificativo Gara" 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="dataInizio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data Inizio</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dataFinePrevista"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data Fine Prevista</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Loghi Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Loghi per PDF</h3>
              <p className="text-sm text-gray-600">Carica i loghi che appariranno nell'header del PDF del verbale (opzionale)</p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="logoSinistra"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo Sinistra</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const formData = new FormData();
                              formData.append('logo', file);
                              
                              try {
                                const response = await apiUpload('/api/upload-logo', formData);
                                const data = await response.json();
                                field.onChange(data.path);
                              } catch (error) {
                                toast({
                                  title: "Errore",
                                  description: "Errore durante l'upload del logo",
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                        />
                      </FormControl>
                      {field.value && (
                        <div className="mt-2">
                          <img src={addTokenToUrl(field.value)} alt="Logo Sinistra" className="max-h-16 w-auto object-contain" />
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="logoCentrale"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo Centrale</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const formData = new FormData();
                              formData.append('logo', file);
                              
                              try {
                                const response = await apiUpload('/api/upload-logo', formData);
                                const data = await response.json();
                                field.onChange(data.path);
                              } catch (error) {
                                toast({
                                  title: "Errore",
                                  description: "Errore durante l'upload del logo",
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                        />
                      </FormControl>
                      {field.value && (
                        <div className="mt-2">
                          <img src={addTokenToUrl(field.value)} alt="Logo Centrale" className="max-h-16 w-auto object-contain" />
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="logoDestro"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo Destro</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const formData = new FormData();
                              formData.append('logo', file);
                              
                              try {
                                const response = await apiUpload('/api/upload-logo', formData);
                                const data = await response.json();
                                field.onChange(data.path);
                              } catch (error) {
                                toast({
                                  title: "Errore",
                                  description: "Errore durante l'upload del logo",
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                        />
                      </FormControl>
                      {field.value && (
                        <div className="mt-2">
                          <img src={addTokenToUrl(field.value)} alt="Logo Destro" className="max-h-16 w-auto object-contain" />
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Direttori Lavori Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Direttori Lavori</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addDirettoreRow}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Aggiungi Direttore
                </Button>
              </div>

              <div className="space-y-3">
                {direttoriRows.map((row, index) => (
                  <div key={row.id} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-4 border rounded-lg bg-gray-50">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nome
                      </label>
                      <Input
                        value={row.nome}
                        onChange={(e) => updateDirettoreRow(row.id, "nome", e.target.value)}
                        placeholder="Nome"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Cognome
                      </label>
                      <Input
                        value={row.cognome}
                        onChange={(e) => updateDirettoreRow(row.id, "cognome", e.target.value)}
                        placeholder="Cognome"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Qualifica
                      </label>
                      <Input
                        value={row.qualifica}
                        onChange={(e) => updateDirettoreRow(row.id, "qualifica", e.target.value)}
                        placeholder="Inserisci qualifica"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <Input
                        value={row.email || ""}
                        onChange={(e) => updateDirettoreRow(row.id, "email", e.target.value)}
                        placeholder="<EMAIL>"
                        type="email"
                      />
                    </div>
                    
                    <div className="flex items-end">
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => removeDirettoreRow(row.id)}
                        disabled={direttoriRows.length <= 1}
                        className="h-10 w-10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Save Directors button for edit mode */}
              {lavoro && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={saveDirettori}
                  disabled={addDirettoreMutation.isPending}
                  className="w-full"
                >
                  {addDirettoreMutation.isPending ? "Salvataggio direttori..." : "Salva Direttori"}
                </Button>
              )}
            </div>

            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button variant="outline" onClick={onClose} type="button">
                Annulla
              </Button>
              <Button
                type="submit"
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {createMutation.isPending || updateMutation.isPending
                  ? "Salvataggio..."
                  : lavoro
                  ? "Aggiorna"
                  : "Crea Commessa"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}