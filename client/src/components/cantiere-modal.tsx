import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { insertCantiereSchema, type Cantiere, type InsertCantiere, type Tecnico } from "@shared/schema";

interface CantiereModalProps {
  isOpen: boolean;
  onClose: () => void;
  cantiere: Cantiere | null;
  title: string;
}

const formSchema = insertCantiereSchema.extend({
  dataInizio: insertCantiereSchema.shape.dataInizio.nullable().optional(),
  dataFinePrevista: insertCantiereSchema.shape.dataFinePrevista.nullable().optional(),
});

export default function CantiereModal({ isOpen, onClose, cantiere, title }: CantiereModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch active tecnici for CSE selection
  const { data: tecnici = [] } = useQuery<Tecnico[]>({
    queryKey: ["/api/tecnici"],
  });

  // Filter only active tecnici
  const activeTecnici = tecnici.filter(tecnico => tecnico.attivo === "true");

  const form = useForm<InsertCantiere>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      codiceCommessa: "",
      indirizzo: "",
      committente: "",
      oggetto: "",
      cseNominato: "",
      dataInizio: null,
      dataFinePrevista: null,
      stato: "In pianificazione",
    },
  });

  // Update form when cantiere changes (for edit mode)
  useEffect(() => {
    if (cantiere) {
      form.reset({
        codiceCommessa: cantiere.codiceCommessa,
        indirizzo: cantiere.indirizzo,
        committente: cantiere.committente,
        oggetto: cantiere.oggetto,
        cseNominato: cantiere.cseNominato,
        dataInizio: cantiere.dataInizio,
        dataFinePrevista: cantiere.dataFinePrevista,
        stato: cantiere.stato,
      });
    } else {
      form.reset({
        codiceCommessa: "",
        indirizzo: "",
        committente: "",
        oggetto: "",
        cseNominato: "",
        dataInizio: null,
        dataFinePrevista: null,
        stato: "In pianificazione",
      });
    }
  }, [cantiere, form]);

  const createMutation = useMutation({
    mutationFn: async (data: InsertCantiere) => {
      const response = await apiRequest("POST", "/api/cantieri", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cantieri"] });
      toast({
        title: "Successo",
        description: "Cantiere creato con successo.",
      });
      onClose();
      form.reset();
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Errore durante la creazione del cantiere.",
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (data: InsertCantiere) => {
      const response = await apiRequest("PUT", `/api/cantieri/${cantiere!.id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cantieri"] });
      toast({
        title: "Successo",
        description: "Cantiere aggiornato con successo.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Errore",
        description: "Errore durante l'aggiornamento del cantiere.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: InsertCantiere) => {
    if (cantiere) {
      updateMutation.mutate(data);
    } else {
      createMutation.mutate(data);
    }
  };

  const isPending = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="codiceCommessa"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Codice Commessa <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Es. CC-2024-004" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="committente"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Committente <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Nome committente" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="indirizzo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Indirizzo <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Via, Città, Provincia" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="oggetto"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Oggetto <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      rows={3}
                      placeholder="Descrizione dettagliata del cantiere"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cseNominato"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    CSE Nominato <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleziona CSE" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {activeTecnici.length === 0 ? (
                        <SelectItem value="no-tecnici" disabled>
                          Nessun tecnico disponibile
                        </SelectItem>
                      ) : (
                        activeTecnici.map((tecnico) => (
                          <SelectItem 
                            key={tecnico.id} 
                            value={`${tecnico.nome} ${tecnico.cognome}`}
                          >
                            {tecnico.nome} {tecnico.cognome} - {tecnico.specializzazione}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="dataInizio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data Inizio</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dataFinePrevista"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data Fine Prevista</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="stato"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stato</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleziona stato" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="In pianificazione">In pianificazione</SelectItem>
                      <SelectItem value="In corso">In corso</SelectItem>
                      <SelectItem value="Sospeso">Sospeso</SelectItem>
                      <SelectItem value="Completato">Completato</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 sm:space-y-0 space-y-reverse">
              <Button type="button" variant="outline" onClick={onClose}>
                Annulla
              </Button>
              <Button type="submit" disabled={isPending} className="bg-primary hover:bg-primary/90">
                {isPending ? "Salvando..." : cantiere ? "Aggiorna Cantiere" : "Crea Cantiere"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
