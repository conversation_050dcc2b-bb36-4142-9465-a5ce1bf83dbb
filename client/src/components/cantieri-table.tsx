import { <PERSON>, <PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import type { Cantiere } from "@shared/schema";

interface CantieriTableProps {
  cantieri: Cantiere[];
  isLoading: boolean;
  onEdit: (cantiere: Cantiere) => void;
  onDelete: (cantiere: Cantiere) => void;
}

const getStatusColor = (stato: string) => {
  switch (stato) {
    case "In corso":
      return "bg-green-100 text-green-800";
    case "In pianificazione":
      return "bg-blue-100 text-blue-800";
    case "Completato":
      return "bg-gray-100 text-gray-800";
    case "Sospeso":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function CantieriTable({ cantieri, isLoading, onEdit, onDelete }: CantieriTableProps) {
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-12 w-full mb-4" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                Codice Commessa
              </TableHead>
              <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                Indirizzo
              </TableHead>
              <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                Committente
              </TableHead>
              <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                Oggetto
              </TableHead>
              <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                CSE Nominato
              </TableHead>
              <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                Stato
              </TableHead>
              <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                Azioni
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {cantieri.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-12">
                  <div className="text-gray-500">
                    <p className="text-lg font-medium">Nessun cantiere trovato</p>
                    <p className="text-sm mt-2">Aggiungi un nuovo cantiere per iniziare.</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              cantieri.map((cantiere) => (
                <TableRow key={cantiere.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{cantiere.codiceCommessa}</TableCell>
                  <TableCell>{cantiere.indirizzo}</TableCell>
                  <TableCell>{cantiere.committente}</TableCell>
                  <TableCell className="max-w-xs truncate">{cantiere.oggetto}</TableCell>
                  <TableCell>{cantiere.cseNominato}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(cantiere.stato)} variant="secondary">
                      {cantiere.stato}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => window.location.href = `/cantieri/${cantiere.id}`}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="text-yellow-600 hover:text-yellow-800"
                        onClick={() => onEdit(cantiere)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {cantieri.length > 0 && (
        <div className="bg-white px-6 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <Button variant="outline" size="sm">
              Precedente
            </Button>
            <Button variant="outline" size="sm">
              Successivo
            </Button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Mostrando <span className="font-medium">1</span> a{" "}
                <span className="font-medium">{cantieri.length}</span> di{" "}
                <span className="font-medium">{cantieri.length}</span> risultati
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <Button variant="outline" size="sm" className="rounded-l-md">
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" className="bg-primary-50 text-primary border-primary">
                  1
                </Button>
                <Button variant="outline" size="sm" className="rounded-r-md">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
