// Generatore PDF offline per verbali direzione lavori
import { jsPDF } from 'jspdf';

export interface VerbaleData {
  numeroProgressivo: string;
  data: string;
  lavorazioniInCorso: string;
  criticita: string;
  note: string;
  osservazioni: string;
  personePresenti: Array<{ nome: string; qualifica: string; email?: string; telefono?: string }>;
  tecnicoNome: string;
  tecnicoQualifica: string;
  codiceCommessa: string;
  descrizioneCommessa: string;
  controlliDimensionali?: Array<{ text: string; photo?: string; drawing?: string }>;
  nonConformita?: Array<{ text: string; photo?: string; drawing?: string }>;
  indicazioniOperative?: Array<{ text: string; photo?: string; drawing?: string }>;
  firme?: { [key: string]: string };
}

export function generateOfflinePDF(verbaleData: VerbaleData): string {
  const doc = new jsPDF();
  let yPosition = 20;
  
  // Helper function to add text with wrapping and page break support
  const addTextWrapped = (text: string, x: number, y: number, maxWidth: number = 180): number => {
    const lines = doc.splitTextToSize(text, maxWidth);
    let currentY = y;
    const pageHeight = doc.internal.pageSize.height;
    const footerMargin = 30; // Spazio riservato per footer/timestamp

    for (let i = 0; i < lines.length; i++) {
      // Controlla se la riga corrente supererebbe il margine del footer
      if (currentY + 6 > pageHeight - footerMargin) {
        doc.addPage();
        currentY = 20; // Margine top nuova pagina

        // Aggiungi timestamp watermark su nuova pagina
        const timestamp = new Date().toLocaleString('it-IT', {
          timeZone: 'Europe/Rome',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });

        doc.setFontSize(8);
        doc.setTextColor(150, 150, 150);
        doc.text(`Generato: ${timestamp}`, 10, 290, { angle: 90 });
        doc.setTextColor(0, 0, 0); // Reset color
      }

      doc.text(lines[i], x, currentY);
      currentY += 6;
    }

    return currentY;
  };
  
  // Helper function to add new page if needed
  const checkPageBreak = (neededHeight: number = 20): void => {
    if (yPosition + neededHeight > 270) {
      doc.addPage();
      yPosition = 20;
    }
  };
  
  // Title
  doc.setFontSize(16);
  doc.text('VERBALE DIREZIONE LAVORI', 105, yPosition, { align: 'center' });
  yPosition += 15;
  
  // Basic info table
  doc.setFontSize(10);
  doc.rect(10, yPosition, 190, 60);
  
  // Header row
  doc.setFillColor(240, 240, 240);
  doc.rect(10, yPosition, 190, 10, 'F');
  doc.text('INFORMAZIONI GENERALI', 15, yPosition + 6);
  yPosition += 15;
  
  // Data rows
  const infoRows = [
    ['Numero', verbaleData.numeroProgressivo],
    ['Data', verbaleData.data],
    ['Commessa', `${verbaleData.codiceCommessa} - ${verbaleData.descrizioneCommessa}`],
    ['Tecnico', `${verbaleData.tecnicoNome} - ${verbaleData.tecnicoQualifica}`]
  ];
  
  infoRows.forEach(([label, value]) => {
    doc.setFillColor(250, 250, 250);
    doc.rect(10, yPosition, 60, 10, 'F');
    doc.text(label, 15, yPosition + 6);
    doc.text(value, 75, yPosition + 6);
    yPosition += 10;
  });
  
  yPosition += 15;
  
  // Main sections
  const sections = [
    { title: 'LAVORAZIONI IN CORSO', content: verbaleData.lavorazioniInCorso },
    { title: 'CRITICITÀ', content: verbaleData.criticita },
    { title: 'NOTE', content: verbaleData.note }
  ];
  
  sections.forEach(section => {
    checkPageBreak(30);
    doc.setFontSize(12);
    doc.text(section.title, 10, yPosition);
    yPosition += 10;
    
    doc.setFontSize(10);
    if (section.content) {
      yPosition = addTextWrapped(section.content.toUpperCase(), 10, yPosition);
    } else {
      yPosition = addTextWrapped('NON SPECIFICATO', 10, yPosition);
    }
    yPosition += 10;
  });
  
  // Optional sections
  if (verbaleData.controlliDimensionali && verbaleData.controlliDimensionali.length > 0) {
    checkPageBreak(40);
    doc.setFontSize(12);
    doc.text('CONTROLLI DIMENSIONALI E GEOMETRICI', 10, yPosition);
    yPosition += 15;
    
    verbaleData.controlliDimensionali.forEach((controllo, index) => {
      checkPageBreak(30);
      doc.setFontSize(10);
      yPosition = addTextWrapped(`${index + 1}. ${controllo.text.toUpperCase()}`, 10, yPosition);
      yPosition += 10;
      
      // Add photo if available (base64) - smaller size for 2 per page
      if (controllo.photo) {
        try {
          checkPageBreak(50);
          doc.addImage(controllo.photo, 'JPEG', 10, yPosition, 60, 45); // Reduced from 80x60 to 60x45
          yPosition += 50;
        } catch (error) {
          console.error('Error adding photo to PDF:', error);
        }
      }
    });
  }
  
  if (verbaleData.nonConformita && verbaleData.nonConformita.length > 0) {
    doc.addPage();
    yPosition = 20;
    
    doc.setFontSize(12);
    doc.text('EVENTUALI NON CONFORMITÀ RISCONTRATE', 10, yPosition);
    yPosition += 15;
    
    verbaleData.nonConformita.forEach((nonConformita, index) => {
      checkPageBreak(30);
      doc.setFontSize(10);
      yPosition = addTextWrapped(`${index + 1}. ${nonConformita.text.toUpperCase()}`, 10, yPosition);
      yPosition += 10;
      
      if (nonConformita.photo) {
        try {
          checkPageBreak(50);
          doc.addImage(nonConformita.photo, 'JPEG', 10, yPosition, 60, 45); // Reduced size
          yPosition += 50;
        } catch (error) {
          console.error('Error adding photo to PDF:', error);
        }
      }
    });
  }
  
  if (verbaleData.indicazioniOperative && verbaleData.indicazioniOperative.length > 0) {
    doc.addPage();
    yPosition = 20;
    
    doc.setFontSize(12);
    doc.text('INDICAZIONI OPERATIVE FORNITE ALL\'IMPRESA', 10, yPosition);
    yPosition += 15;
    
    verbaleData.indicazioniOperative.forEach((indicazione, index) => {
      checkPageBreak(30);
      doc.setFontSize(10);
      yPosition = addTextWrapped(`${index + 1}. ${indicazione.text.toUpperCase()}`, 10, yPosition);
      yPosition += 10;
      
      if (indicazione.photo) {
        try {
          checkPageBreak(50);
          doc.addImage(indicazione.photo, 'JPEG', 10, yPosition, 60, 45); // Reduced size
          yPosition += 50;
        } catch (error) {
          console.error('Error adding photo to PDF:', error);
        }
      }
    });
  }
  
  // Safety observations
  if (verbaleData.osservazioni) {
    doc.addPage();
    yPosition = 20;
    
    doc.setFontSize(12);
    doc.text('ASPETTI SULLA SICUREZZA', 10, yPosition);
    yPosition += 15;
    
    doc.setFontSize(10);
    yPosition = addTextWrapped(verbaleData.osservazioni.toUpperCase(), 10, yPosition);
    yPosition += 20;
  }
  
  // People present
  if (verbaleData.personePresenti && verbaleData.personePresenti.length > 0) {
    checkPageBreak(40 + verbaleData.personePresenti.length * 8);
    doc.setFontSize(12);
    doc.text('PERSONE PRESENTI', 10, yPosition);
    yPosition += 15;
    
    // Creare tabella per persone presenti
    const tableWidth = 180;
    const colWidths = [50, 60, 35, 35]; // nominativo, qualifica, telefono, email
    const rowHeight = 8;
    
    // Header tabella
    doc.setFontSize(9);
    doc.setFont('helvetica', 'bold');
    doc.setFillColor(220, 220, 220);
    doc.rect(10, yPosition, tableWidth, rowHeight, 'F');
    doc.rect(10, yPosition, tableWidth, rowHeight, 'S');
    
    // Linee verticali header
    let currentX = 10;
    doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
    currentX += colWidths[0];
    doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
    currentX += colWidths[1];
    doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
    currentX += colWidths[2];
    doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
    currentX += colWidths[3];
    doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
    
    // Testi header
    doc.text('NOMINATIVO', 12, yPosition + 5);
    doc.text('QUALIFICA', 12 + colWidths[0], yPosition + 5);
    doc.text('TELEFONO', 12 + colWidths[0] + colWidths[1], yPosition + 5);
    doc.text('EMAIL', 12 + colWidths[0] + colWidths[1] + colWidths[2], yPosition + 5);
    
    yPosition += rowHeight;
    
    // Righe dati
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(8);
    
    verbaleData.personePresenti.forEach(persona => {
      // Bordo riga
      doc.rect(10, yPosition, tableWidth, rowHeight, 'S');
      
      // Linee verticali
      currentX = 10;
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[0];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[1];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[2];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      currentX += colWidths[3];
      doc.line(currentX, yPosition, currentX, yPosition + rowHeight);
      
      // Testi con wrapping
      const nome = persona.nome.toUpperCase();
      const qualifica = persona.qualifica.toUpperCase();
      const telefono = persona.telefono?.trim() || '';
      const email = persona.email?.trim()?.toLowerCase() || '';
      
      // Nominativo - centrato verticalmente
      const nomeLines = doc.splitTextToSize(nome, colWidths[0] - 4);
      const nomeYOffset = (rowHeight - (nomeLines.length * 3)) / 2;
      doc.text(nomeLines, 12, yPosition + 3 + nomeYOffset);
      
      // Qualifica - centrato verticalmente
      const qualificaLines = doc.splitTextToSize(qualifica, colWidths[1] - 4);
      const qualificaYOffset = (rowHeight - (qualificaLines.length * 3)) / 2;
      doc.text(qualificaLines, 12 + colWidths[0], yPosition + 3 + qualificaYOffset);
      
      // Telefono - centrato verticalmente
      const telefonoLines = doc.splitTextToSize(telefono, colWidths[2] - 4);
      const telefonoYOffset = (rowHeight - (telefonoLines.length * 3)) / 2;
      doc.text(telefonoLines, 12 + colWidths[0] + colWidths[1], yPosition + 3 + telefonoYOffset);
      
      // Email - centrato verticalmente
      const emailLines = doc.splitTextToSize(email, colWidths[3] - 4);
      const emailYOffset = (rowHeight - (emailLines.length * 3)) / 2;
      doc.text(emailLines, 12 + colWidths[0] + colWidths[1] + colWidths[2], yPosition + 3 + emailYOffset);
      
      yPosition += rowHeight;
    });
    
    yPosition += 10;
  }
  
  // Signatures section
  checkPageBreak(60);
  doc.setFontSize(12);
  doc.text('FIRME', 10, yPosition);
  yPosition += 15;
  
  if (verbaleData.firme) {
    Object.entries(verbaleData.firme).forEach(([key, signature]) => {
      checkPageBreak(40);
      doc.setFontSize(10);
      doc.text(key.toUpperCase(), 10, yPosition);
      yPosition += 10;
      
      if (signature) {
        try {
          doc.addImage(signature, 'PNG', 10, yPosition, 60, 20);
          yPosition += 25;
        } catch (error) {
          console.error('Error adding signature to PDF:', error);
          yPosition += 25;
        }
      } else {
        yPosition += 25;
      }
    });
  }
  
  // Work director signature
  checkPageBreak(40);
  doc.setFontSize(10);
  doc.text(`IL DIRETTORE LAVORI`, 10, yPosition);
  yPosition += 5;
  doc.text(`${verbaleData.tecnicoNome.toUpperCase()}`, 10, yPosition);
  yPosition += 15;
  
  // Timestamp watermark
  const timestamp = new Date().toLocaleString('it-IT', { 
    timeZone: 'Europe/Rome',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
  
  doc.setFontSize(8);
  doc.setTextColor(150, 150, 150);
  doc.text(`Generato: ${timestamp}`, 10, 290, { angle: 90 });
  
  // Generate PDF as base64
  const pdfBase64 = doc.output('datauristring').split(',')[1];
  
  console.log('PDF generato offline:', {
    numeroProgressivo: verbaleData.numeroProgressivo,
    size: pdfBase64.length,
    timestamp
  });
  
  return pdfBase64;
}