// Gestione storage locale per dati cached offline
// UNIFICATO: usa solo IndexedDBManager per evitare conflitti

// Re-export everything from unified offline system
export * from './offline-storage/';

// Import the unified manager  
import { indexedDBManager } from './offline-storage/indexeddb-manager';

// Utility functions per integration con React Query - usa IndexedDBManager
export const getOfflineData = async (queryKey: string) => {
  try {
    return await indexedDBManager.getCachedApiData(queryKey);
  } catch (error) {
    console.error('Error getting offline data:', error);
    return null;
  }
};

export const setOfflineData = async (queryKey: string, data: any, ttl = 24 * 60 * 60 * 1000) => {
  try {
    await indexedDBManager.setCachedApiData(queryKey, data, ttl);
  } catch (error) {
    console.error('Error setting offline data:', error);
  }
};

export const removeOfflineData = async (queryKey: string) => {
  try {
    await indexedDBManager.removeCachedApiData(queryKey);
  } catch (error) {
    console.error('Error removing offline data:', error);
  }
};

// Re-export tutte le funzioni necessarie dall'adapter
export { 
  isOnline,
  saveVerbaleOffline,
  getOfflineVerbali,
  removeOfflineVerbale,
  syncOfflineVerbali,
  convertFileToBase64,
  savePhotoOffline,
  getOfflinePhotos,
  savePDFOffline,
  getOfflinePDF,
  downloadOfflinePDF
} from './offline-storage/offline-adapter';