// IndexedDB Manager per Orbyta Engineering - Sistema Gestione Cantieri
// Unifica tutto lo storage offline: verbali, foto, PDF, cache API

interface OfflineVerbale {
  id: string;
  lavoroId: number;
  data: any;
  timestamp: number;
  type: 'bozza' | 'finalizzato';
  status: 'pending' | 'syncing' | 'synced' | 'error';
  syncAttempts: number;
  lastSyncAttempt?: number;
  errorMessage?: string;
}

interface OfflinePhoto {
  id: string;
  verbaleId: string;
  filename: string;
  blob: Blob;
  timestamp: number;
  status: 'pending' | 'syncing' | 'synced' | 'error';
  syncAttempts: number;
  lastSyncAttempt?: number;
  errorMessage?: string;
}

interface OfflinePDF {
  id: string;
  verbaleId: string;
  filename: string;
  blob: Blob;
  timestamp: number;
  status: 'pending' | 'syncing' | 'synced' | 'error';
  syncAttempts: number;
  lastSyncAttempt?: number;
  errorMessage?: string;
}

interface ApiCacheRecord {
  url: string;
  data: any;
  timestamp: number;
  expiry: number;
  etag?: string;
}

interface SyncEvent {
  type: 'manual' | 'automatic' | 'app-visible' | 'network-restored';
  timestamp: number;
  itemsSynced: number;
  errors: number;
}

class IndexedDBManager {
  private db: IDBDatabase | null = null;
  private readonly DB_NAME = 'OrbytaEngineeringCantieriDB';
  private readonly DB_VERSION = 1;
  private syncInProgress = false;
  private syncQueue: Set<string> = new Set();
  private maxSyncAttempts = 3;
  private syncRetryDelay = 5000; // 5 seconds
  private eventListeners: Map<string, Function[]> = new Map();

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);
      
      request.onerror = () => {
        console.error('❌ Errore apertura IndexedDB:', request.error);
        reject(request.error);
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB inizializzato:', this.DB_NAME);
        this.setupOfflineDetection();
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        console.log('🔧 Creazione schema IndexedDB...');
        
        // Store verbali offline
        if (!db.objectStoreNames.contains('verbali')) {
          const verbaliStore = db.createObjectStore('verbali', { keyPath: 'id' });
          verbaliStore.createIndex('lavoroId', 'lavoroId', { unique: false });
          verbaliStore.createIndex('status', 'status', { unique: false });
          verbaliStore.createIndex('timestamp', 'timestamp', { unique: false });
          verbaliStore.createIndex('type', 'type', { unique: false });
        }
        
        // Store foto (binary data efficiente)
        if (!db.objectStoreNames.contains('photos')) {
          const photosStore = db.createObjectStore('photos', { keyPath: 'id' });
          photosStore.createIndex('verbaleId', 'verbaleId', { unique: false });
          photosStore.createIndex('status', 'status', { unique: false });
          photosStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
        
        // Store PDF (binary data efficiente)
        if (!db.objectStoreNames.contains('pdfs')) {
          const pdfsStore = db.createObjectStore('pdfs', { keyPath: 'id' });
          pdfsStore.createIndex('verbaleId', 'verbaleId', { unique: false });
          pdfsStore.createIndex('status', 'status', { unique: false });
          pdfsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
        
        // Store cache API (sostituisce Service Worker cache)
        if (!db.objectStoreNames.contains('apiCache')) {
          const apiCacheStore = db.createObjectStore('apiCache', { keyPath: 'url' });
          apiCacheStore.createIndex('expiry', 'expiry', { unique: false });
          apiCacheStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
        
        // Store sync history e metriche
        if (!db.objectStoreNames.contains('syncHistory')) {
          const syncHistoryStore = db.createObjectStore('syncHistory', { keyPath: 'timestamp' });
          syncHistoryStore.createIndex('type', 'type', { unique: false });
        }
      };
    });
  }

  // ===== OFFLINE DETECTION & SYNC MANAGEMENT =====
  
  private setupOfflineDetection(): void {
    // Listener per cambi stato rete
    window.addEventListener('online', () => {
      console.log('🟢 Rete ripristinata - avvio sincronizzazione automatica');
      this.handleNetworkRestored();
    });

    window.addEventListener('offline', () => {
      console.log('🔴 Rete persa - modalità offline attiva');
      this.emit('offline-mode-activated', { timestamp: Date.now() });
    });

    // Listener per visibilità app (tornare dall'app)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.isOnline()) {
        console.log('👀 App tornata in primo piano - check sincronizzazione');
        this.handleAppVisible();
      }
    });

    // Sync periodica ogni 30 secondi se online
    setInterval(() => {
      if (this.isOnline() && !this.syncInProgress) {
        this.syncPendingDataSilent();
      }
    }, 30000);
  }

  private async handleNetworkRestored(): Promise<void> {
    this.emit('network-restored', { timestamp: Date.now() });
    
    // Aspetta un secondo per stabilizzare la connessione
    setTimeout(() => {
      this.syncPendingData('network-restored');
    }, 1000);
  }

  private async handleAppVisible(): Promise<void> {
    if (this.hasUnsyncedData()) {
      this.syncPendingData('app-visible');
    }
  }

  public isOnline(): boolean {
    return navigator.onLine;
  }

  public isOffline(): boolean {
    return !navigator.onLine;
  }

  private async hasUnsyncedData(): Promise<boolean> {
    const transaction = this.db!.transaction(['verbali', 'photos', 'pdfs'], 'readonly');
    
    const verbaliStore = transaction.objectStore('verbali');
    const verbaliIndex = verbaliStore.index('status');
    const pendingVerbali = await this.getAllFromIndex(verbaliIndex, 'pending');
    
    const photosStore = transaction.objectStore('photos');
    const photosIndex = photosStore.index('status');
    const pendingPhotos = await this.getAllFromIndex(photosIndex, 'pending');
    
    const pdfsStore = transaction.objectStore('pdfs');
    const pdfsIndex = pdfsStore.index('status');
    const pendingPdfs = await this.getAllFromIndex(pdfsIndex, 'pending');
    
    return pendingVerbali.length > 0 || pendingPhotos.length > 0 || pendingPdfs.length > 0;
  }

  // ===== VERBALI OPERATIONS =====
  
  async saveVerbale(lavoroId: number, data: any, type: 'bozza' | 'finalizzato' = 'bozza'): Promise<string> {
    const id = `verbale_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Sanitizza i dati come nel sistema esistente
    const sanitizedData = {
      ...data,
      lavorazioniInCorso: data.lavorazioniInCorso || '',
      criticita: data.criticita || '',
      note: data.note || '',
      osservazioni: data.osservazioni || '',
      personePresenti: data.personePresenti || '[]',
      numeroProgressivo: data.numeroProgressivo || '001',
      stato: data.stato || 'bozza',
      controlliDimensionali: data.controlliDimensionali || null,
      nonConformita: data.nonConformita || null,
      indicazioniOperative: data.indicazioniOperative || null,
      firme: data.firme || null
    };
    
    const verbale: OfflineVerbale = {
      id,
      lavoroId,
      data: sanitizedData,
      timestamp: Date.now(),
      type,
      status: 'pending',
      syncAttempts: 0
    };
    
    const transaction = this.db!.transaction(['verbali'], 'readwrite');
    const store = transaction.objectStore('verbali');
    
    // Rimuovi duplicati per stesso lavoroId e tipo
    const lavoroIndex = store.index('lavoroId');
    const existing = await this.getAllFromIndex(lavoroIndex, lavoroId);
    for (const existingVerbale of existing) {
      if (existingVerbale.type === type) {
        console.log(`🗑️ Rimozione duplicato verbale ${existingVerbale.id} per lavoro ${lavoroId}`);
        await store.delete(existingVerbale.id);
      }
    }
    
    await store.add(verbale);
    console.log('💾 Verbale salvato in IndexedDB:', { id, lavoroId, type, offline: this.isOffline() });
    
    // Store navigation info per verbali finalizzati
    if (type === 'finalizzato') {
      await this.storeNavigationInfo(id, `/direzione-lavori/${lavoroId}`);
    }
    
    // Sincronizza immediatamente se online
    if (this.isOnline()) {
      this.queueForSync(id, 'verbale');
    }
    
    this.emit('verbale-saved', { id, lavoroId, type, offline: this.isOffline() });
    return id;
  }

  async getVerbaliByLavoro(lavoroId: number): Promise<OfflineVerbale[]> {
    const transaction = this.db!.transaction(['verbali'], 'readonly');
    const store = transaction.objectStore('verbali');
    const index = store.index('lavoroId');
    return await this.getAllFromIndex(index, lavoroId);
  }

  async getAllPendingVerbali(): Promise<OfflineVerbale[]> {
    const transaction = this.db!.transaction(['verbali'], 'readonly');
    const store = transaction.objectStore('verbali');
    const index = store.index('status');
    return await this.getAllFromIndex(index, 'pending');
  }

  // ===== PHOTO OPERATIONS (BINARY) =====
  
  async savePhoto(verbaleId: string, filename: string, file: File): Promise<string> {
    const id = `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const photoRecord: OfflinePhoto = {
      id,
      verbaleId,
      filename,
      blob: file, // Store come Blob nativo (più efficiente di base64)
      timestamp: Date.now(),
      status: 'pending',
      syncAttempts: 0
    };
    
    const transaction = this.db!.transaction(['photos'], 'readwrite');
    const store = transaction.objectStore('photos');
    await store.add(photoRecord);
    
    console.log('📸 Foto salvata in IndexedDB:', { 
      id, 
      verbaleId, 
      filename, 
      size: this.formatBytes(file.size),
      offline: this.isOffline() 
    });
    
    // Sincronizza immediatamente se online
    if (this.isOnline()) {
      this.queueForSync(id, 'photo');
    }
    
    this.emit('photo-saved', { id, verbaleId, filename, size: file.size });
    return id;
  }

  async getPhotosByVerbale(verbaleId: string): Promise<OfflinePhoto[]> {
    const transaction = this.db!.transaction(['photos'], 'readonly');
    const store = transaction.objectStore('photos');
    const index = store.index('verbaleId');
    return await this.getAllFromIndex(index, verbaleId);
  }

  async getPhotoBlob(photoId: string): Promise<Blob | null> {
    const transaction = this.db!.transaction(['photos'], 'readonly');
    const store = transaction.objectStore('photos');
    const photo = await this.getFromStore(store, photoId);
    return photo ? photo.blob : null;
  }

  // ===== PDF OPERATIONS (BINARY) =====
  
  async savePDF(verbaleId: string, filename: string, pdfBlob: Blob): Promise<string> {
    const id = `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const pdfRecord: OfflinePDF = {
      id,
      verbaleId,
      filename,
      blob: pdfBlob,
      timestamp: Date.now(),
      status: 'pending',
      syncAttempts: 0
    };
    
    const transaction = this.db!.transaction(['pdfs'], 'readwrite');
    const store = transaction.objectStore('pdfs');
    await store.add(pdfRecord);
    
    console.log('📄 PDF salvato in IndexedDB:', { 
      id, 
      verbaleId, 
      filename, 
      size: this.formatBytes(pdfBlob.size),
      offline: this.isOffline() 
    });
    
    // Sincronizza immediatamente se online
    if (this.isOnline()) {
      this.queueForSync(id, 'pdf');
    }
    
    this.emit('pdf-saved', { id, verbaleId, filename, size: pdfBlob.size });
    return id;
  }

  async getPDFBlob(verbaleId: string): Promise<Blob | null> {
    const transaction = this.db!.transaction(['pdfs'], 'readonly');
    const store = transaction.objectStore('pdfs');
    const index = store.index('verbaleId');
    const pdfs = await this.getAllFromIndex(index, verbaleId);
    return pdfs.length > 0 ? pdfs[0].blob : null;
  }

  async downloadOfflinePDF(verbaleId: string, filename: string): Promise<void> {
    const pdfBlob = await this.getPDFBlob(verbaleId);
    if (!pdfBlob) {
      console.error('❌ PDF non trovato per verbale:', verbaleId);
      return;
    }
    
    try {
      // Crea URL blob e apri in nuova tab
      const url = URL.createObjectURL(pdfBlob);
      window.open(url, '_blank');
      
      // Cleanup dopo 1 secondo
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);
      
      console.log('📖 PDF offline aperto:', filename);
    } catch (error) {
      console.error('❌ Errore apertura PDF offline:', error);
    }
  }

  // ===== API CACHE OPERATIONS =====
  
  async cacheApiResponse(url: string, data: any, ttlMinutes: number = 60, etag?: string): Promise<void> {
    const cacheRecord: ApiCacheRecord = {
      url,
      data,
      timestamp: Date.now(),
      expiry: Date.now() + (ttlMinutes * 60 * 1000),
      etag
    };
    
    const transaction = this.db!.transaction(['apiCache'], 'readwrite');
    const store = transaction.objectStore('apiCache');
    await store.put(cacheRecord);
    
    console.log('💾 API response cached:', { url, ttl: ttlMinutes, size: JSON.stringify(data).length });
  }

  async getCachedApiResponse(url: string): Promise<any | null> {
    const transaction = this.db!.transaction(['apiCache'], 'readonly');
    const store = transaction.objectStore('apiCache');
    const record = await this.getFromStore(store, url);
    
    if (!record) {
      return null;
    }
    
    if (record.expiry < Date.now()) {
      console.log('⏰ Cache API scaduta:', url);
      // Rimuovi cache scaduta
      const deleteTransaction = this.db!.transaction(['apiCache'], 'readwrite');
      const deleteStore = deleteTransaction.objectStore('apiCache');
      await deleteStore.delete(url);
      return null;
    }
    
    console.log('💾 API response from cache:', url);
    return record.data;
  }

  // ===== SYNC OPERATIONS =====
  
  private queueForSync(itemId: string, type: 'verbale' | 'photo' | 'pdf'): void {
    this.syncQueue.add(`${type}:${itemId}`);
    
    // Avvia sync dopo breve delay per batch operations
    setTimeout(() => {
      if (this.isOnline() && !this.syncInProgress) {
        this.processSyncQueue();
      }
    }, 1000);
  }

  private async processSyncQueue(): Promise<void> {
    if (this.syncQueue.size === 0 || this.syncInProgress) {
      return;
    }
    
    console.log(`🔄 Processando queue sincronizzazione: ${this.syncQueue.size} elementi`);
    
    const queue = Array.from(this.syncQueue);
    this.syncQueue.clear();
    
    for (const item of queue) {
      const [type, id] = item.split(':');
      try {
        switch (type) {
          case 'verbale':
            await this.syncSingleVerbale(id);
            break;
          case 'photo':
            await this.syncSinglePhoto(id);
            break;
          case 'pdf':
            await this.syncSinglePDF(id);
            break;
        }
      } catch (error) {
        console.error(`❌ Errore sync ${type} ${id}:`, error);
      }
    }
  }

  async syncPendingData(triggerType: 'manual' | 'automatic' | 'app-visible' | 'network-restored' = 'manual'): Promise<void> {
    if (this.syncInProgress) {
      console.log('⏸️ Sincronizzazione già in corso...');
      return;
    }
    
    if (this.isOffline()) {
      console.log('🔴 Impossibile sincronizzare: modalità offline');
      return;
    }
    
    this.syncInProgress = true;
    const startTime = Date.now();
    let itemsSynced = 0;
    let errors = 0;
    
    console.log(`🔄 Avvio sincronizzazione completa (trigger: ${triggerType})...`);
    this.emit('sync-started', { triggerType, timestamp: startTime });
    
    try {
      // Sync verbali
      const verbaliSynced = await this.syncAllVerbali();
      itemsSynced += verbaliSynced;
      
      // Sync photos
      const photosSynced = await this.syncAllPhotos();
      itemsSynced += photosSynced;
      
      // Sync PDFs
      const pdfsSynced = await this.syncAllPDFs();
      itemsSynced += pdfsSynced;
      
      // Record sync event
      const syncEvent: SyncEvent = {
        type: triggerType,
        timestamp: startTime,
        itemsSynced,
        errors
      };
      
      await this.recordSyncEvent(syncEvent);
      
      const duration = Date.now() - startTime;
      console.log(`✅ Sincronizzazione completata: ${itemsSynced} elementi in ${duration}ms`);
      
      this.emit('sync-completed', { 
        itemsSynced, 
        errors, 
        duration, 
        triggerType 
      });
      
    } catch (error) {
      console.error('❌ Errore durante sincronizzazione:', error);
      errors++;
      this.emit('sync-error', { error, triggerType });
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncPendingDataSilent(): Promise<void> {
    // Sync silenzioso senza log verbosi
    if (this.syncInProgress || this.isOffline()) return;
    
    const hasUnsynced = await this.hasUnsyncedData();
    if (hasUnsynced) {
      await this.syncPendingData('automatic');
    }
  }

  private async syncAllVerbali(): Promise<number> {
    const pendingVerbali = await this.getAllPendingVerbali();
    let synced = 0;
    
    for (const verbale of pendingVerbali) {
      if (await this.syncSingleVerbale(verbale.id)) {
        synced++;
      }
    }
    
    return synced;
  }

  private async syncSingleVerbale(verbaleId: string): Promise<boolean> {
    const transaction = this.db!.transaction(['verbali'], 'readwrite');
    const store = transaction.objectStore('verbali');
    const verbale = await this.getFromStore(store, verbaleId);
    
    if (!verbale || verbale.status === 'synced') {
      return false;
    }
    
    // Controlla tentativi massimi
    if (verbale.syncAttempts >= this.maxSyncAttempts) {
      console.log(`⚠️ Verbale ${verbaleId} ha superato i tentativi massimi (${this.maxSyncAttempts})`);
      return false;
    }
    
    try {
      // Mark as syncing
      verbale.status = 'syncing';
      verbale.syncAttempts++;
      verbale.lastSyncAttempt = Date.now();
      await store.put(verbale);
      
      const endpoint = verbale.type === 'finalizzato' 
        ? `/api/direzione-lavori/${verbale.lavoroId}/verbali/con-firme`
        : `/api/direzione-lavori/${verbale.lavoroId}/verbali`;
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(verbale.data),
        credentials: 'include'
      });
      
      if (response.ok) {
        verbale.status = 'synced';
        verbale.errorMessage = undefined;
        await store.put(verbale);
        
        console.log('✅ Verbale sincronizzato:', verbaleId);
        this.emit('verbale-synced', { id: verbaleId, lavoroId: verbale.lavoroId });
        return true;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
    } catch (error) {
      // Mark as error
      verbale.status = 'error';
      verbale.errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto';
      await store.put(verbale);
      
      console.error('❌ Errore sync verbale:', verbaleId, error);
      return false;
    }
  }

  private async syncAllPhotos(): Promise<number> {
    const transaction = this.db!.transaction(['photos'], 'readonly');
    const store = transaction.objectStore('photos');
    const index = store.index('status');
    const pendingPhotos = await this.getAllFromIndex(index, 'pending');
    
    let synced = 0;
    for (const photo of pendingPhotos) {
      if (await this.syncSinglePhoto(photo.id)) {
        synced++;
      }
    }
    
    return synced;
  }

  private async syncSinglePhoto(photoId: string): Promise<boolean> {
    const transaction = this.db!.transaction(['photos'], 'readwrite');
    const store = transaction.objectStore('photos');
    const photo = await this.getFromStore(store, photoId);
    
    if (!photo || photo.status === 'synced') {
      return false;
    }
    
    if (photo.syncAttempts >= this.maxSyncAttempts) {
      return false;
    }
    
    try {
      photo.status = 'syncing';
      photo.syncAttempts++;
      photo.lastSyncAttempt = Date.now();
      await store.put(photo);
      
      // Upload foto
      const formData = new FormData();
      formData.append('photo', photo.blob, photo.filename);
      
      const response = await fetch('/api/upload-photo', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });
      
      if (response.ok) {
        photo.status = 'synced';
        photo.errorMessage = undefined;
        await store.put(photo);
        
        console.log('✅ Foto sincronizzata:', photoId);
        return true;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
    } catch (error) {
      photo.status = 'error';
      photo.errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto';
      await store.put(photo);
      
      console.error('❌ Errore sync foto:', photoId, error);
      return false;
    }
  }

  private async syncAllPDFs(): Promise<number> {
    const transaction = this.db!.transaction(['pdfs'], 'readonly');
    const store = transaction.objectStore('pdfs');
    const index = store.index('status');
    const pendingPdfs = await this.getAllFromIndex(index, 'pending');
    
    let synced = 0;
    for (const pdf of pendingPdfs) {
      if (await this.syncSinglePDF(pdf.id)) {
        synced++;
      }
    }
    
    return synced;
  }

  private async syncSinglePDF(pdfId: string): Promise<boolean> {
    const transaction = this.db!.transaction(['pdfs'], 'readwrite');
    const store = transaction.objectStore('pdfs');
    const pdf = await this.getFromStore(store, pdfId);
    
    if (!pdf || pdf.status === 'synced') {
      return false;
    }
    
    if (pdf.syncAttempts >= this.maxSyncAttempts) {
      return false;
    }
    
    try {
      pdf.status = 'syncing';
      pdf.syncAttempts++;
      pdf.lastSyncAttempt = Date.now();
      await store.put(pdf);
      
      // Upload PDF
      const formData = new FormData();
      formData.append('pdf', pdf.blob, pdf.filename);
      
      const response = await fetch('/api/upload-pdf', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });
      
      if (response.ok) {
        pdf.status = 'synced';
        pdf.errorMessage = undefined;
        await store.put(pdf);
        
        console.log('✅ PDF sincronizzato:', pdfId);
        return true;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
    } catch (error) {
      pdf.status = 'error';
      pdf.errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto';
      await store.put(pdf);
      
      console.error('❌ Errore sync PDF:', pdfId, error);
      return false;
    }
  }

  // ===== NAVIGATION & UTILITY =====
  
  private async storeNavigationInfo(verbaleId: string, targetPath: string): Promise<void> {
    const navigationData = {
      shouldNavigate: true,
      targetPath,
      timestamp: Date.now()
    };
    
    // Store in api cache con chiave speciale
    await this.cacheApiResponse(`navigation_${verbaleId}`, navigationData, 60 * 24); // 24 ore
  }

  async getNavigationInfo(verbaleId: string): Promise<any> {
    return await this.getCachedApiResponse(`navigation_${verbaleId}`);
  }

  private async recordSyncEvent(event: SyncEvent): Promise<void> {
    const transaction = this.db!.transaction(['syncHistory'], 'readwrite');
    const store = transaction.objectStore('syncHistory');
    await store.add(event);
  }

  // ===== CLEANUP & MAINTENANCE =====
  
  async cleanup(): Promise<void> {
    console.log('🧹 Avvio pulizia IndexedDB...');
    
    // Rimuovi cache API scadute
    await this.cleanupExpiredCache();
    
    // Rimuovi sync events vecchi (> 30 giorni)
    await this.cleanupOldSyncEvents();
    
    // Rimuovi elementi sincronizzati vecchi (> 7 giorni)
    await this.cleanupOldSyncedItems();
    
    console.log('✅ Pulizia IndexedDB completata');
  }

  private async cleanupExpiredCache(): Promise<void> {
    const transaction = this.db!.transaction(['apiCache'], 'readwrite');
    const store = transaction.objectStore('apiCache');
    const index = store.index('expiry');
    
    const range = IDBKeyRange.upperBound(Date.now());
    const expiredEntries = await this.getAllFromIndex(index, range);
    
    for (const entry of expiredEntries) {
      await store.delete(entry.url);
    }
    
    console.log(`🗑️ Rimosse ${expiredEntries.length} cache API scadute`);
  }

  private async cleanupOldSyncEvents(): Promise<void> {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const transaction = this.db!.transaction(['syncHistory'], 'readwrite');
    const store = transaction.objectStore('syncHistory');
    
    const range = IDBKeyRange.upperBound(thirtyDaysAgo);
    const oldEvents = await this.getAllFromRange(store, range);
    
    for (const event of oldEvents) {
      await store.delete(event.timestamp);
    }
    
    console.log(`🗑️ Rimossi ${oldEvents.length} eventi sync vecchi`);
  }

  private async cleanupOldSyncedItems(): Promise<void> {
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    // Cleanup verbali sincronizzati
    const verbaliTransaction = this.db!.transaction(['verbali'], 'readwrite');
    const verbaliStore = verbaliTransaction.objectStore('verbali');
    const verbaliIndex = verbaliStore.index('status');
    const syncedVerbali = await this.getAllFromIndex(verbaliIndex, 'synced');
    
    let removedVerbali = 0;
    for (const verbale of syncedVerbali) {
      if (verbale.timestamp < sevenDaysAgo) {
        await verbaliStore.delete(verbale.id);
        removedVerbali++;
      }
    }
    
    console.log(`🗑️ Rimossi ${removedVerbali} verbali sincronizzati vecchi`);
  }

  // ===== EVENT SYSTEM =====
  
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Errore in event listener ${event}:`, error);
        }
      });
    }
  }

  // ===== UTILITY METHODS =====
  
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private async getAllFromIndex(index: IDBIndex, key: any): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const request = index.getAll(key);
      request.onsuccess = () => resolve(request.result || []);
      request.onerror = () => reject(request.error);
    });
  }

  private async getAllFromRange(store: IDBObjectStore, range: IDBKeyRange): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const request = store.getAll(range);
      request.onsuccess = () => resolve(request.result || []);
      request.onerror = () => reject(request.error);
    });
  }

  private async getFromStore(store: IDBObjectStore, key: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // ===== MIGRATION FROM LOCALSTORAGE =====
  
  async migrateFromLocalStorage(): Promise<void> {
    console.log('🔄 Migrazione da localStorage a IndexedDB...');
    
    try {
      // Import funzioni esistenti localStorage se disponibili
      const existingVerbali = this.getExistingOfflineVerbali();
      let migratedCount = 0;
      
      for (const verbale of existingVerbali) {
        await this.saveVerbale(verbale.lavoroId, verbale.data, verbale.type);
        migratedCount++;
      }
      
      // Migra foto se presenti in localStorage
      // TODO: Implementare migrazione foto da base64 a Blob
      
      // Pulisci localStorage dopo migrazione
      this.clearLegacyLocalStorage();
      
      console.log(`✅ Migrazione completata: ${migratedCount} elementi migrati`);
      
    } catch (error) {
      console.error('❌ Errore durante migrazione:', error);
    }
  }

  private getExistingOfflineVerbali(): any[] {
    try {
      const stored = localStorage.getItem('offline_verbali');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  private clearLegacyLocalStorage(): void {
    // Rimuovi chiavi localStorage legacy
    const keysToRemove = [
      'offline_verbali',
      'orbyta_offline_verbali' // potrebbe essere chiamato così
    ];
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    // Rimuovi tutte le chiavi che iniziano con offline_
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('offline_')) {
        localStorage.removeItem(key);
      }
    });
  }

  // ===== STATS & DEBUGGING =====
  
  async getStorageStats(): Promise<any> {
    const stats = {
      verbali: {
        total: 0,
        pending: 0,
        synced: 0,
        error: 0
      },
      photos: {
        total: 0,
        pending: 0,
        synced: 0,
        error: 0,
        totalSize: 0
      },
      pdfs: {
        total: 0,
        pending: 0,
        synced: 0,
        error: 0,
        totalSize: 0
      },
      apiCache: {
        total: 0,
        totalSize: 0
      }
    };
    
    // Conta verbali
    const verbaliTransaction = this.db!.transaction(['verbali'], 'readonly');
    const verbaliStore = verbaliTransaction.objectStore('verbali');
    const allVerbali = await this.getAllFromRange(verbaliStore, IDBKeyRange.lowerBound(0));
    
    stats.verbali.total = allVerbali.length;
    allVerbali.forEach(v => {
      stats.verbali[v.status]++;
    });
    
    // Conta foto
    const photosTransaction = this.db!.transaction(['photos'], 'readonly');
    const photosStore = photosTransaction.objectStore('photos');
    const allPhotos = await this.getAllFromRange(photosStore, IDBKeyRange.lowerBound(0));
    
    stats.photos.total = allPhotos.length;
    allPhotos.forEach(p => {
      stats.photos[p.status]++;
      stats.photos.totalSize += p.blob.size;
    });
    
    // Conta PDF
    const pdfsTransaction = this.db!.transaction(['pdfs'], 'readonly');
    const pdfsStore = pdfsTransaction.objectStore('pdfs');
    const allPdfs = await this.getAllFromRange(pdfsStore, IDBKeyRange.lowerBound(0));
    
    stats.pdfs.total = allPdfs.length;
    allPdfs.forEach(p => {
      stats.pdfs[p.status]++;
      stats.pdfs.totalSize += p.blob.size;
    });
    
    return stats;
  }

  async logStorageStats(): Promise<void> {
    const stats = await this.getStorageStats();
    console.table(stats);
  }

  // ===== COMPATIBILITY METHODS FOR QUERY CLIENT =====
  
  async getCachedApiData(queryKey: string): Promise<any | null> {
    return this.getCachedApiResponse(queryKey);
  }

  async setCachedApiData(queryKey: string, data: any, ttl = 24 * 60 * 60 * 1000): Promise<void> {
    const ttlMinutes = Math.floor(ttl / (1000 * 60));
    return this.cacheApiResponse(queryKey, data, ttlMinutes);
  }

  async removeCachedApiData(queryKey: string): Promise<void> {
    const transaction = this.db!.transaction(['apiCache'], 'readwrite');
    const store = transaction.objectStore('apiCache');
    await store.delete(queryKey);
  }
}

// Singleton instance
export const indexedDBManager = new IndexedDBManager();

// Auto-init when module loads
indexedDBManager.init().catch(console.error);