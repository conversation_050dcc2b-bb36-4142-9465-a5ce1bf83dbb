// Adapter per mantenere compatibilità con le funzioni esistenti
// Orbyta Engineering - Sistema Gestione Cantieri

import { indexedDBManager } from './indexeddb-manager';

// ===== VERBALI FUNCTIONS =====

export async function saveVerbaleOffline(lavoroId: number, data: any, type: 'bozza' | 'finalizzato' = 'bozza'): Promise<string> {
  return await indexedDBManager.saveVerbale(lavoroId, data, type);
}

export async function getOfflineVerbali(): Promise<any[]> {
  // Get all pending verbali across all projects
  return await indexedDBManager.getAllPendingVerbali();
}

export async function getOfflineVerbaliByLavoro(lavoroId: number): Promise<any[]> {
  return await indexedDBManager.getVerbaliByLavoro(lavoroId);
}

export async function syncOfflineVerbali(): Promise<void> {
  return await indexedDBManager.syncPendingData('manual');
}

// ===== NETWORK FUNCTIONS =====

export function isOnline(): boolean {
  return indexedDBManager.isOnline();
}

export function isOffline(): boolean {
  return indexedDBManager.isOffline();
}

// ===== PHOTO FUNCTIONS =====

export async function convertFileToBase64(file: File): Promise<string> {
  // Questa funzione ora non serve più, ma la manteniamo per compatibilità
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

export async function savePhotoOffline(verbaleId: string, filename: string, file: File): Promise<string> {
  return await indexedDBManager.savePhoto(verbaleId, filename, file);
}

export async function savePhotoOfflineFromBase64(verbaleId: string, filename: string, base64Data: string): Promise<string> {
  // Convert base64 back to File for IndexedDB
  const blob = base64ToBlob(base64Data);
  const file = new File([blob], filename, { type: blob.type });
  return await indexedDBManager.savePhoto(verbaleId, filename, file);
}

export async function getOfflinePhotos(verbaleId: string): Promise<{ [key: string]: string }> {
  // Get photos as base64 for backward compatibility
  const photos = await indexedDBManager.getPhotosByVerbale(verbaleId);
  const result: { [key: string]: string } = {};
  
  for (const photo of photos) {
    // Convert blob back to base64 for compatibility
    const base64 = await blobToBase64(photo.blob);
    result[photo.filename] = base64;
  }
  
  return result;
}

export async function getOfflinePhotosAsBlobs(verbaleId: string): Promise<{ [key: string]: Blob }> {
  // New function that returns blobs directly (more efficient)
  const photos = await indexedDBManager.getPhotosByVerbale(verbaleId);
  const result: { [key: string]: Blob } = {};
  
  for (const photo of photos) {
    result[photo.filename] = photo.blob;
  }
  
  return result;
}

export async function deleteOfflinePhotos(verbaleId: string): Promise<void> {
  // TODO: Implement delete photos in IndexedDBManager
  console.log('deleteOfflinePhotos not yet implemented for:', verbaleId);
}

// ===== PDF FUNCTIONS =====

export async function savePDFOffline(verbaleId: string, pdfBase64: string): Promise<string> {
  // Convert base64 to blob
  const blob = base64ToBlob(pdfBase64, 'application/pdf');
  const filename = `verbale_${verbaleId}.pdf`;
  return await indexedDBManager.savePDF(verbaleId, filename, blob);
}

export async function savePDFOfflineFromBlob(verbaleId: string, filename: string, pdfBlob: Blob): Promise<string> {
  return await indexedDBManager.savePDF(verbaleId, filename, pdfBlob);
}

export async function getOfflinePDF(verbaleId: string): Promise<string | null> {
  // Get PDF as base64 for backward compatibility
  const blob = await indexedDBManager.getPDFBlob(verbaleId);
  if (!blob) return null;
  
  return await blobToBase64(blob);
}

export async function getOfflinePDFBlob(verbaleId: string): Promise<Blob | null> {
  // New function that returns blob directly (more efficient)
  return await indexedDBManager.getPDFBlob(verbaleId);
}

export async function downloadOfflinePDF(verbaleId: string, filename: string): Promise<void> {
  return await indexedDBManager.downloadOfflinePDF(verbaleId, filename);
}

export async function deleteOfflinePDF(verbaleId: string): Promise<void> {
  // TODO: Implement delete PDF in IndexedDBManager
  console.log('deleteOfflinePDF not yet implemented for:', verbaleId);
}

// ===== NAVIGATION FUNCTIONS =====

export async function getNavigationInfo(verbaleId: string): Promise<any> {
  return await indexedDBManager.getNavigationInfo(verbaleId);
}

// ===== CLEANUP FUNCTIONS =====

export async function removeOfflineVerbale(offlineId: string): Promise<void> {
  // TODO: Implement remove single verbale in IndexedDBManager
  console.log('removeOfflineVerbale not yet implemented for:', offlineId);
}

export async function clearAllOfflineVerbali(): Promise<void> {
  // TODO: Implement clear all in IndexedDBManager
  console.log('clearAllOfflineVerbali not yet implemented');
}

// ===== UTILITY FUNCTIONS =====

function base64ToBlob(base64: string, contentType: string = ''): Blob {
  // Remove data URL prefix if present
  const base64Data = base64.includes(',') ? base64.split(',')[1] : base64;
  
  // Extract content type from data URL if not provided
  if (!contentType && base64.includes('data:')) {
    const match = base64.match(/data:([^;]+)/);
    if (match) {
      contentType = match[1];
    }
  }
  
  try {
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
    
  } catch (error) {
    console.error('Error converting base64 to blob:', error);
    throw error;
  }
}

async function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

// ===== STORAGE STATS =====

export async function getStorageStats(): Promise<any> {
  return await indexedDBManager.getStorageStats();
}

export async function logStorageStats(): Promise<void> {
  return await indexedDBManager.logStorageStats();
}

// ===== EVENT SYSTEM =====

export function onOfflineEvent(event: string, callback: Function): void {
  indexedDBManager.on(event, callback);
}

// Auto-sync setup
export function setupAutoSync(): void {
  // Setup automatic sync when network is restored
  indexedDBManager.on('network-restored', () => {
    console.log('🟢 Network restored - triggering auto-sync');
  });
  
  indexedDBManager.on('sync-completed', (data: any) => {
    console.log('✅ Sync completed:', data);
  });
  
  indexedDBManager.on('sync-error', (data: any) => {
    console.error('❌ Sync error:', data);
  });
}