// Entry point per il sistema offline unificato
// Orbyta Engineering - Sistema Gestione Cantieri

import { indexedDBManager } from './indexeddb-manager';
import { setupAutoSync } from './offline-adapter';

// Auto-initialize IndexedDB and setup auto-sync
let initialized = false;

export async function initializeOfflineSystem(): Promise<void> {
  if (initialized) return;
  
  try {
    console.log('🔧 Inizializzazione sistema offline IndexedDB...');
    
    // Initialize IndexedDB
    await indexedDBManager.init();
    
    // Setup auto-sync events
    setupAutoSync();
    
    // Log initial stats
    setTimeout(async () => {
      const stats = await indexedDBManager.getStorageStats();
      console.log('📊 Stats sistema offline:', stats);
    }, 1000);
    
    initialized = true;
    console.log('✅ Sistema offline inizializzato correttamente');
    
  } catch (error) {
    console.error('❌ Errore inizializzazione sistema offline:', error);
    throw error;
  }
}

// Auto-initialize when module loads
initializeOfflineSystem().catch(console.error);

// Re-export everything from adapter for convenience
export * from './offline-adapter';
export { indexedDBManager };