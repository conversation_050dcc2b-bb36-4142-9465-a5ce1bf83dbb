import { QueryClient, QueryFunction } from "@tanstack/react-query";
import { getOfflineData, setOfflineData } from "./offline-storage";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    // Check if it's an offline response from Service Worker
    try {
      const responseBody = await res.text();
      const parsedBody = JSON.parse(responseBody);
      
      // If it's marked as offline but has a queued flag, don't throw an error
      if (parsedBody.offline && parsedBody.queued) {
        // Reset the response stream by creating a new response
        return new Response(responseBody, {
          status: res.status,
          statusText: res.statusText,
          headers: res.headers
        });
      }
      
      // Otherwise, throw the error as normal
      throw new Error(`${res.status}: ${responseBody || res.statusText}`);
    } catch (parseError) {
      // If we can't parse the response, throw the original error
      // Don't try to read the body again, it was already consumed
      throw new Error(`${res.status}: ${res.statusText}`);
    }
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  try {
    // Controlla se la modalità offline è forzata
    const isOfflineForced = localStorage.getItem('orbyta-offline-forced') === 'true';

    if (isOfflineForced) {
      console.log('🔴 Modalità offline forzata - simulando errore di rete per:', url);
      // Simula un errore di rete per forzare il comportamento offline
      throw new Error('Failed to fetch - Offline mode forced');
    }

    const token = localStorage.getItem('auth_token');
    const headers: Record<string, string> = {};

    if (data) {
      headers["Content-Type"] = "application/json";
    }

    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const res = await fetch(url, {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined,
      credentials: "include",
    });

    // Check for offline responses before throwing
    if (!res.ok) {
      try {
        const responseText = await res.clone().text();
        const responseBody = JSON.parse(responseText);
        
        // If it's an offline response, return it without throwing
        if (responseBody.offline) {
          return res;
        }
      } catch (parseError) {
        // If we can't parse, continue with normal error handling
      }
    }

    await throwIfResNotOk(res);
    return res;
  } catch (error) {
    // Check if it's a network error (offline mode)
    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      // Return a mock offline response
      return new Response(
        JSON.stringify({ 
          offline: true, 
          queued: true,
          id: Date.now(),
          message: 'Request queued for offline sync'
        }), 
        { 
          status: 201,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    throw error;
  }
}

// Specialized function for file uploads with FormData + JWT auth
export async function apiUpload(
  url: string,
  formData: FormData,
): Promise<Response> {
  try {
    // Controlla se la modalità offline è forzata
    const isOfflineForced = localStorage.getItem('orbyta-offline-forced') === 'true';

    if (isOfflineForced) {
      console.log('🔴 Modalità offline forzata - bloccando upload per:', url);
      // Simula un errore di rete per forzare il comportamento offline
      throw new Error('Failed to fetch - Offline mode forced');
    }

    const token = localStorage.getItem('auth_token');
    console.log('🔑 apiUpload - Token present:', !!token);
    console.log('🔑 apiUpload - Token length:', token?.length);

    const headers: Record<string, string> = {};
    
    // Add JWT token if available
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
      console.log('🔑 apiUpload - Added Authorization header');
    } else {
      console.error('🔑 apiUpload - NO TOKEN FOUND!');
    }
    
    // Note: Don't set Content-Type for FormData, let browser set it with boundary
    const res = await fetch(url, {
      method: 'POST',
      headers,
      body: formData,
      credentials: "include",
    });

    await throwIfResNotOk(res);
    return res;
  } catch (error: any) {
    // Handle offline mode like apiRequest
    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      return new Response(
        JSON.stringify({ 
          offline: true, 
          error: 'Upload failed - device offline',
          queued: false 
        }),
        { status: 503, statusText: 'Service Unavailable' }
      );
    }
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const queryKeyStr = Array.isArray(queryKey) ? queryKey.join('-') : String(queryKey[0]);

    try {
      // Controlla se la modalità offline è forzata
      const isOfflineForced = localStorage.getItem('orbyta-offline-forced') === 'true';

      if (isOfflineForced) {
        console.log('🔴 Modalità offline forzata - usando cache per:', queryKeyStr);
        // Prova a usare i dati in cache
        const cachedData = await getOfflineData(queryKeyStr);
        if (cachedData) {
          return cachedData;
        }
        // Se non ci sono dati in cache, simula errore di rete
        throw new Error('Failed to fetch - Offline mode forced');
      }

      const token = localStorage.getItem('auth_token');
      const headers: Record<string, string> = {};

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      const res = await fetch(queryKey[0] as string, {
        credentials: "include",
        headers,
      });

      if (unauthorizedBehavior === "returnNull" && res.status === 401) {
        return null;
      }

      await throwIfResNotOk(res);
      const data = await res.json();
      
      // Cache successful response for offline use
      await setOfflineData(queryKeyStr, data);
      
      return data;
    } catch (error) {
      // If network request fails, try to get cached data
      const cachedData = await getOfflineData(queryKeyStr);
      if (cachedData) {
        console.log('Using cached data for:', queryKeyStr);
        return cachedData;
      }
      
      // If no cached data, throw the original error
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
