import { apiRequest } from "./api";

export async function downloadPDF(url: string, filename?: string) {
  try {
    // Estrai il token
    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('Token di autenticazione mancante');
    }

    // Fai la richiesta con il token nell'header
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Errore download: ${response.statusText}`);
    }

    // Converti la risposta in blob
    const blob = await response.blob();
    
    // Crea un URL temporaneo per il blob
    const blobUrl = URL.createObjectURL(blob);
    
    // Crea un link temporaneo e clicca per scaricare
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = filename || 'verbale.pdf';
    document.body.appendChild(link);
    link.click();
    
    // Pulisci
    document.body.removeChild(link);
    URL.revokeObjectURL(blobUrl);
    
    return { success: true };
  } catch (error) {
    console.error('Errore download PDF:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Errore sconosciuto' 
    };
  }
}

export function getDownloadUrl(path: string): string {
  const token = localStorage.getItem('auth_token');
  if (!token) {
    throw new Error('Token di autenticazione mancante');
  }
  
  // Aggiungi il token come query parameter
  const separator = path.includes('?') ? '&' : '?';
  return `${path}${separator}token=${encodeURIComponent(token)}`;
}