// Sistema offline per verbali usando localStorage
export interface OfflineVerbale {
  id: string;
  lavoroId: number;
  data: any;
  timestamp: number;
  type: 'bozza' | 'finalizzato';
  photos?: { [key: string]: string }; // base64 encoded photos
  pdfData?: string; // base64 encoded PDF
}

const OFFLINE_VERBALI_KEY = 'offline_verbali';

export function saveVerbaleOffline(lavoroId: number, data: any, type: 'bozza' | 'finalizzato' = 'bozza'): string {
  const offlineId = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Assicurati che i dati essenziali siano sempre presenti, includendo le foto
  const sanitizedData = {
    ...data,
    lavorazioniInCorso: data.lavorazioniInCorso || '',
    criticita: data.criticita || '',
    note: data.note || '',
    osservazioni: data.osservazioni || '',
    personePresenti: data.personePresenti || '[]',
    numeroProgressivo: data.numeroProgressivo || '001',
    stato: data.stato || 'bozza',
    // Includi foto e altri dati delle sezioni opzionali
    controlliDimensionali: data.controlliDimensionali || null,
    nonConformita: data.nonConformita || null,
    indicazioniOperative: data.indicazioniOperative || null,
    firme: data.firme || null
  };
  
  const offlineVerbale: OfflineVerbale = {
    id: offlineId,
    lavoroId,
    data: sanitizedData,
    timestamp: Date.now(),
    type
  };
  
  const existingVerbali = getOfflineVerbali();
  
  // Rimuovi duplicati per lo stesso lavoroId e tipo (evita verbali multipli)
  const duplicates = existingVerbali.filter(v => v.lavoroId === lavoroId && v.type === type);
  const filteredVerbali = existingVerbali.filter(v => !(v.lavoroId === lavoroId && v.type === type));
  
  console.log(`Rimuovi ${duplicates.length} duplicati per lavoroId ${lavoroId} tipo ${type}`);
  
  filteredVerbali.push(offlineVerbale);
  
  localStorage.setItem(OFFLINE_VERBALI_KEY, JSON.stringify(filteredVerbali));
  console.log('Verbale salvato offline (duplicati rimossi):', offlineVerbale);
  
  // Store navigation info for offline verbali finalizzati
  if (type === 'finalizzato') {
    const navigationData = {
      shouldNavigate: true,
      targetPath: `/direzione-lavori/${lavoroId}`,
      timestamp: Date.now()
    };
    localStorage.setItem(`offline_nav_${offlineId}`, JSON.stringify(navigationData));
    console.log('Navigation data stored for offline verbale:', navigationData);
  }
  
  return offlineId;
}

export function getOfflineVerbali(): OfflineVerbale[] {
  try {
    const stored = localStorage.getItem(OFFLINE_VERBALI_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Errore nel recupero verbali offline:', error);
    return [];
  }
}

export function getOfflineVerbaliByLavoro(lavoroId: number): OfflineVerbale[] {
  return getOfflineVerbali().filter(v => v.lavoroId === lavoroId);
}

export function removeOfflineVerbale(offlineId: string): void {
  const verbali = getOfflineVerbali().filter(v => v.id !== offlineId);
  localStorage.setItem(OFFLINE_VERBALI_KEY, JSON.stringify(verbali));
}

export function clearAllOfflineVerbali(): void {
  localStorage.removeItem(OFFLINE_VERBALI_KEY);
}

export async function syncOfflineVerbali(): Promise<void> {
  const offlineVerbali = getOfflineVerbali();
  
  if (offlineVerbali.length === 0) {
    return;
  }
  
  console.log(`Sincronizzando ${offlineVerbali.length} verbali offline...`);
  
  for (const verbale of offlineVerbali) {
    try {
      const endpoint = verbale.type === 'finalizzato' 
        ? `/api/direzione-lavori/${verbale.lavoroId}/verbali/con-firme`
        : `/api/direzione-lavori/${verbale.lavoroId}/verbali`;
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(verbale.data),
        credentials: 'include'
      });
      
      if (response.ok) {
        removeOfflineVerbale(verbale.id);
        console.log('Verbale sincronizzato:', verbale.id);
      } else {
        console.error('Errore sincronizzazione verbale:', verbale.id, response.statusText);
      }
    } catch (error) {
      console.error('Errore rete durante sincronizzazione:', verbale.id, error);
      // Non rimuovere il verbale se c'è ancora un errore di rete
      break;
    }
  }
}

export function isOnline(): boolean {
  return navigator.onLine;
}

// Funzioni per gestire le foto offline
export function convertFileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

export function savePhotoOffline(verbaleId: string, filename: string, base64Data: string): void {
  const photosKey = `offline_photos_${verbaleId}`;
  const existingPhotos = getOfflinePhotos(verbaleId);
  existingPhotos[filename] = base64Data;
  localStorage.setItem(photosKey, JSON.stringify(existingPhotos));
  console.log('Foto salvata offline:', { verbaleId, filename, size: base64Data.length });
}

export function getOfflinePhotos(verbaleId: string): { [key: string]: string } {
  try {
    const photosKey = `offline_photos_${verbaleId}`;
    const stored = localStorage.getItem(photosKey);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('Errore nel recupero foto offline:', error);
    return {};
  }
}

export function deleteOfflinePhotos(verbaleId: string): void {
  const photosKey = `offline_photos_${verbaleId}`;
  localStorage.removeItem(photosKey);
  console.log('Foto offline rimosse per verbale:', verbaleId);
}

// Funzioni per gestire PDF offline
export function savePDFOffline(verbaleId: string, pdfData: string): void {
  const pdfKey = `offline_pdf_${verbaleId}`;
  localStorage.setItem(pdfKey, pdfData);
  console.log('PDF salvato offline per verbale:', verbaleId);
}

export function getOfflinePDF(verbaleId: string): string | null {
  try {
    const pdfKey = `offline_pdf_${verbaleId}`;
    return localStorage.getItem(pdfKey);
  } catch (error) {
    console.error('Errore nel recupero PDF offline:', error);
    return null;
  }
}

export function deleteOfflinePDF(verbaleId: string): void {
  const pdfKey = `offline_pdf_${verbaleId}`;
  localStorage.removeItem(pdfKey);
  console.log('PDF offline rimosso per verbale:', verbaleId);
}

export function downloadOfflinePDF(verbaleId: string, filename: string): void {
  const pdfData = getOfflinePDF(verbaleId);
  if (!pdfData) {
    console.error('PDF non trovato per verbale:', verbaleId);
    return;
  }
  
  try {
    // Convert base64 to blob
    const byteCharacters = atob(pdfData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'application/pdf' });
    
    // Create blob URL and open in new tab instead of downloading
    const url = URL.createObjectURL(blob);
    window.open(url, '_blank');
    
    // Clean up the blob URL after a short delay
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);
    
    console.log('PDF aperto offline:', filename);
  } catch (error) {
    console.error('Errore nell\'apertura PDF offline:', error);
  }
}