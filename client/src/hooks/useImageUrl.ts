import { useAuth } from '@/contexts/AuthContext';

export const useImageUrl = () => {
  const { token } = useAuth();
  
  const getImageUrl = (category: string, filename: string): string => {
    if (!token) {
      return '';
    }
    
    return `/api/files/${category}/${filename}?token=${encodeURIComponent(token)}`;
  };
  
  // Per URL che sono già completi (come quelli restituiti dal server)
  const addTokenToUrl = (url: string): string => {
    if (!token || !url) {
      return url;
    }
    
    // Se l'URL è già un API endpoint, aggiungi il token
    if (url.startsWith('/api/files/')) {
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}token=${encodeURIComponent(token)}`;
    }
    
    return url;
  };
  
  return { getImageUrl, addTokenToUrl };
};