import { useLocation } from "wouter";
import { useEffect, useState } from "react";

interface NavigationState {
  currentPath: string;
  previousPath: string | null;
  breadcrumbs: BreadcrumbItem[];
}

interface BreadcrumbItem {
  label: string;
  path: string;
  icon?: string;
}

// Mappa dei percorsi per generare breadcrumb intelligenti
const pathMap: Record<string, { label: string; icon?: string; parent?: string }> = {
  "/": { label: "Dashboard", icon: "Home" },
  "/cantieri": { label: "Coordinamento Sicurezza", icon: "HardHat", parent: "/" },
  "/direzione-lavori": { label: "Direzione Lavori", icon: "Briefcase", parent: "/" },
  "/impostazioni": { label: "Impostazioni", icon: "Settings", parent: "/" },
  "/impostazioni/loghi": { label: "Gestione Loghi", icon: "Image", parent: "/impostazioni" },
};

// Funzione per generare label dinamiche per percorsi con parametri
const generateDynamicLabel = (path: string, params: Record<string, string>): string => {
  if (path.includes("/cantieri/") && params.id) {
    return `Cantiere ${params.id}`;
  }
  if (path.includes("/direzione-lavori/") && !path.includes("/verbali")) {
    return `Lavoro ${params.id || params.lavoroId}`;
  }
  if (path.includes("/verbali/new")) {
    return "Nuovo Verbale";
  }
  if (path.includes("/verbali/") && params.verbaleId) {
    return `Verbale ${params.verbaleId}`;
  }
  return "Dettaglio";
};

// Funzione per estrarre parametri dal percorso
const extractParams = (path: string): Record<string, string> => {
  const params: Record<string, string> = {};
  
  // Estrai ID cantiere
  const cantiereMatch = path.match(/\/cantieri\/(\d+)/);
  if (cantiereMatch) params.id = cantiereMatch[1];
  
  // Estrai ID lavoro
  const lavoroMatch = path.match(/\/direzione-lavori\/(\d+)/);
  if (lavoroMatch) params.lavoroId = lavoroMatch[1];
  
  // Estrai ID verbale
  const verbaleMatch = path.match(/\/verbali\/(\w+)/);
  if (verbaleMatch) params.verbaleId = verbaleMatch[1];
  
  return params;
};

// Funzione per determinare il percorso parent
const getParentPath = (currentPath: string): string => {
  // Regole specifiche per percorsi dinamici
  if (currentPath.match(/\/cantieri\/\d+$/)) {
    return "/cantieri";
  }
  if (currentPath.match(/\/direzione-lavori\/\d+$/)) {
    return "/direzione-lavori";
  }
  if (currentPath.match(/\/direzione-lavori\/\d+\/verbali\//)) {
    const lavoroMatch = currentPath.match(/\/direzione-lavori\/(\d+)/);
    return lavoroMatch ? `/direzione-lavori/${lavoroMatch[1]}` : "/direzione-lavori";
  }
  if (currentPath.startsWith("/impostazioni/")) {
    return "/impostazioni";
  }
  
  // Fallback: rimuovi l'ultimo segmento
  const segments = currentPath.split("/").filter(Boolean);
  if (segments.length <= 1) return "/";
  return "/" + segments.slice(0, -1).join("/");
};

// Funzione per generare breadcrumbs
const generateBreadcrumbs = (currentPath: string): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [];
  const params = extractParams(currentPath);
  
  // Sempre inizia con Dashboard se non siamo già lì
  if (currentPath !== "/") {
    breadcrumbs.push({ label: "Dashboard", path: "/", icon: "Home" });
  }
  
  // Aggiungi breadcrumb per percorsi principali
  if (currentPath.startsWith("/cantieri")) {
    breadcrumbs.push({ label: "Coordinamento Sicurezza", path: "/cantieri", icon: "HardHat" });
    
    if (currentPath.match(/\/cantieri\/\d+/)) {
      breadcrumbs.push({
        label: generateDynamicLabel(currentPath, params),
        path: currentPath,
        icon: "Building"
      });
    }
  }
  
  if (currentPath.startsWith("/direzione-lavori")) {
    breadcrumbs.push({ label: "Direzione Lavori", path: "/direzione-lavori", icon: "Briefcase" });
    
    const lavoroMatch = currentPath.match(/\/direzione-lavori\/(\d+)/);
    if (lavoroMatch) {
      const lavoroPath = `/direzione-lavori/${lavoroMatch[1]}`;
      breadcrumbs.push({
        label: `Lavoro ${lavoroMatch[1]}`,
        path: lavoroPath,
        icon: "Building"
      });
      
      if (currentPath.includes("/verbali/")) {
        breadcrumbs.push({
          label: generateDynamicLabel(currentPath, params),
          path: currentPath,
          icon: "FileText"
        });
      }
    }
  }
  
  if (currentPath.startsWith("/impostazioni")) {
    breadcrumbs.push({ label: "Impostazioni", path: "/impostazioni", icon: "Settings" });
    
    if (currentPath === "/impostazioni/loghi") {
      breadcrumbs.push({
        label: "Gestione Loghi",
        path: "/impostazioni/loghi",
        icon: "Image"
      });
    }
  }
  
  return breadcrumbs;
};

export function useNavigation() {
  const [location, setLocation] = useLocation();
  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentPath: location,
    previousPath: null,
    breadcrumbs: generateBreadcrumbs(location)
  });

  // Aggiorna stato quando cambia la location
  useEffect(() => {
    setNavigationState(prev => ({
      currentPath: location,
      previousPath: prev.currentPath !== location ? prev.currentPath : prev.previousPath,
      breadcrumbs: generateBreadcrumbs(location)
    }));
  }, [location]);

  // Funzione per navigazione intelligente indietro
  const goBack = () => {
    const parentPath = getParentPath(location);
    setLocation(parentPath);
  };

  // Funzione per navigazione a percorso specifico
  const navigateTo = (path: string) => {
    setLocation(path);
  };

  // Funzione per ottenere il titolo della pagina corrente
  const getCurrentPageTitle = (): string => {
    const breadcrumbs = navigationState.breadcrumbs;
    return breadcrumbs.length > 0 ? breadcrumbs[breadcrumbs.length - 1].label : "Dashboard";
  };

  // Funzione per verificare se siamo in una pagina di dettaglio
  const isDetailPage = (): boolean => {
    return location.includes("/cantieri/") ||
           location.includes("/direzione-lavori/") ||
           location.includes("/verbali/") ||
           location.startsWith("/impostazioni");
  };

  return {
    currentPath: navigationState.currentPath,
    previousPath: navigationState.previousPath,
    breadcrumbs: navigationState.breadcrumbs,
    goBack,
    navigateTo,
    getCurrentPageTitle,
    isDetailPage,
    parentPath: getParentPath(location)
  };
}
