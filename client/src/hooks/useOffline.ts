import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface OfflineState {
  isOnline: boolean;
  isInstallable: boolean;
  hasOfflineActions: boolean;
  installPWA: () => void;
  syncOfflineActions: () => Promise<void>;
  toggleOfflineMode: () => void;
  isOfflineForced: boolean;
}

export function useOffline(): OfflineState {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isOfflineForced, setIsOfflineForced] = useState(() => {
    // Ripristina stato offline forzato da localStorage
    if (typeof window !== 'undefined') {
      return localStorage.getItem('orbyta-offline-forced') === 'true';
    }
    return false;
  });
  const [isInstallable, setIsInstallable] = useState(false);
  const [hasOfflineActions, setHasOfflineActions] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const { toast } = useToast();

  // L'app è considerata online solo se la connessione è attiva E non è forzata offline
  const effectiveIsOnline = isOnline && !isOfflineForced;

  useEffect(() => {
    // Monitor online/offline status
    const handleOnline = () => {
      setIsOnline(true);
      toast({
        title: "Connessione ripristinata",
        description: "L'applicazione è nuovamente online. Sincronizzazione in corso...",
      });
      // Delay sync to allow connection to stabilize
      setTimeout(async () => {
        try {
          await syncOfflineActions();
          // Also sync our new offline verbali system
          const { syncOfflineVerbali } = await import('@/lib/offline-storage');
          await syncOfflineVerbali();
        } catch (error) {
          console.error('Error during sync:', error);
        }
      }, 1000);
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast({
        title: "Modalità offline",
        description: "I verbali saranno salvati e sincronizzati quando torni online",
        variant: "destructive",
      });
    };

    // Monitor PWA install availability
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Check for pending offline actions
    checkOfflineActions();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, [toast]);

  const installPWA = async () => {
    if (!deferredPrompt) return;

    try {
      deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        toast({
          title: "App installata",
          description: "L'applicazione è stata installata sul dispositivo",
        });
      }

      setDeferredPrompt(null);
      setIsInstallable(false);
    } catch (error) {
      console.error('Error installing PWA:', error);
    }
  };

  const syncOfflineActions = async () => {
    if (!isOnline) return;

    try {
      // Trigger service worker sync solo se disponibile e supportato
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        const registration = await navigator.serviceWorker.ready;
        if (registration.active) {
          try {
            await registration.sync.register('sync-offline-actions');
            console.log('Background sync triggered successfully');
          } catch (syncError) {
            console.warn('Background sync not available:', syncError);
            // Fallback: sync manualmente senza background sync
            await manualSync();
          }
        }
      } else {
        // Fallback: sync manualmente
        await manualSync();
      }

      // Check if actions were synced
      setTimeout(() => {
        checkOfflineActions();
      }, 1000);
    } catch (error) {
      console.error('Error syncing offline actions:', error);
    }
  };

  const manualSync = async () => {
    // Sincronizzazione intelligente con gestione errori e retry
    try {
      const db = await openDB();
      const tx = db.transaction(['offline_actions'], 'readwrite');
      const actions = await tx.objectStore('offline_actions').getAll();

      // Ordina azioni per timestamp (cronologico)
      const sortedActions = actions.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));

      console.log(`🔄 Avvio sincronizzazione di ${sortedActions.length} azioni offline`);

      let successCount = 0;
      let failureCount = 0;

      for (const action of sortedActions) {
        try {
          // Controlla se non siamo tornati in modalità offline forzata
          const isStillOfflineForced = localStorage.getItem('orbyta-offline-forced') === 'true';
          if (isStillOfflineForced) {
            console.log('🔴 Sincronizzazione interrotta - modalità offline forzata riattivata');
            break;
          }

          console.log(`📤 Sincronizzando azione ${action.id}: ${action.method} ${action.url}`);

          const response = await fetch(action.url, {
            method: action.method,
            headers: action.headers,
            body: action.body
          });

          if (response.ok) {
            await tx.objectStore('offline_actions').delete(action.id);
            successCount++;
            console.log(`✅ Azione ${action.id} sincronizzata con successo`);
          } else {
            // Incrementa retry count
            const retryCount = (action.retryCount || 0) + 1;
            const maxRetries = 3;

            if (retryCount >= maxRetries) {
              console.error(`❌ Azione ${action.id} fallita dopo ${maxRetries} tentativi - rimozione`);
              await tx.objectStore('offline_actions').delete(action.id);
              failureCount++;
            } else {
              // Aggiorna retry count
              await tx.objectStore('offline_actions').put({
                ...action,
                retryCount,
                lastRetry: Date.now()
              });
              console.warn(`⚠️ Azione ${action.id} fallita (tentativo ${retryCount}/${maxRetries}) - riproverò`);
            }
          }

          // Piccola pausa tra le azioni per non sovraccaricare
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.error(`❌ Errore sincronizzazione azione ${action.id}:`, error);
          failureCount++;
        }
      }

      // Mostra risultato sincronizzazione
      if (successCount > 0 || failureCount > 0) {
        toast({
          title: "Sincronizzazione completata",
          description: `✅ ${successCount} azioni sincronizzate${failureCount > 0 ? `, ❌ ${failureCount} fallite` : ''}`,
          variant: failureCount > 0 ? "destructive" : "default",
        });
      }

    } catch (error) {
      console.error('❌ Errore generale sincronizzazione:', error);
      toast({
        title: "Errore sincronizzazione",
        description: "Impossibile sincronizzare le azioni offline. Riprova più tardi.",
        variant: "destructive",
      });
    }
  };

  const checkOfflineActions = async () => {
    try {
      const db = await openDB();
      const tx = db.transaction(['offline_actions'], 'readonly');
      const actions = await tx.objectStore('offline_actions').getAll();
      setHasOfflineActions(actions.length > 0);
    } catch (error) {
      console.error('Error checking offline actions:', error);
    }
  };

  // Funzione per toggle modalità offline forzata
  const toggleOfflineMode = () => {
    const newOfflineState = !isOfflineForced;
    setIsOfflineForced(newOfflineState);

    // Salva stato in localStorage per persistenza
    if (typeof window !== 'undefined') {
      localStorage.setItem('orbyta-offline-forced', newOfflineState.toString());
    }

    // Mostra toast informativo
    toast({
      title: newOfflineState ? "Modalità Offline Attivata" : "Modalità Online Ripristinata",
      description: newOfflineState
        ? "L'app funzionerà offline. I dati saranno sincronizzati quando torni online."
        : "L'app tornerà a sincronizzare i dati automaticamente.",
      variant: newOfflineState ? "destructive" : "default",
    });

    // Se torniamo online e c'è connessione, avvia sync
    if (!newOfflineState && isOnline) {
      setTimeout(async () => {
        try {
          await syncOfflineActions();
        } catch (error) {
          console.error('Error during sync after toggle:', error);
        }
      }, 1000);
    }
  };

  return {
    isOnline: effectiveIsOnline, // Usa lo stato effettivo (considerando offline forzato)
    isInstallable,
    hasOfflineActions,
    installPWA,
    syncOfflineActions,
    toggleOfflineMode,
    isOfflineForced,
  };
}

// Helper function to open IndexedDB
function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('OrbyteOfflineDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      if (!db.objectStoreNames.contains('offline_actions')) {
        const store = db.createObjectStore('offline_actions', { keyPath: 'id', autoIncrement: true });
        store.createIndex('timestamp', 'timestamp');
      }
    };
  });
}