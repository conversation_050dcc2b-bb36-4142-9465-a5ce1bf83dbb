import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getQueryFn } from "@/lib/queryClient";
import { Briefcase, Plus, Search, Filter, Bell } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import DirezioneLavoriTable from "@/components/direzione-lavori-table";
import DirezioneLavoriModal from "@/components/direzione-lavori-modal";
import DeleteDirezioneLavoriModal from "@/components/delete-direzione-lavori-modal";
import type { DirezioneLavori } from "@shared/schema";

export default function DirezioneLavori() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [committenteFilter, setCommittenteFilter] = useState("all");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedLavoro, setSelectedLavoro] = useState<DirezioneLavori | null>(null);

  const { data: lavori = [], isLoading, refetch } = useQuery<DirezioneLavori[]>({
    queryKey: [
      searchTerm 
        ? `/api/direzione-lavori?search=${encodeURIComponent(searchTerm)}`
        : "/api/direzione-lavori", 
      searchTerm
    ],
    queryFn: getQueryFn({ on401: "throw" }),
  });

  // Filter lavori based on status and committente
  const filteredLavori = lavori.filter(lavoro => {
    if (statusFilter && statusFilter !== "all" && lavoro.stato !== statusFilter) return false;
    if (committenteFilter && committenteFilter !== "all" && lavoro.committente !== committenteFilter) return false;
    return true;
  });

  // Get unique committenti for filter dropdown (filter out empty values)
  const uniqueCommittenti = Array.from(new Set(lavori.map(l => l.committente).filter(c => c && c.trim() !== "")));

  const handleEdit = (lavoro: DirezioneLavori) => {
    setSelectedLavoro(lavoro);
    setIsEditModalOpen(true);
  };

  const handleDelete = (lavoro: DirezioneLavori) => {
    setSelectedLavoro(lavoro);
    setIsDeleteModalOpen(true);
  };

  const handleModalClose = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
    setSelectedLavoro(null);
    refetch();
  };

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Direzione Lavori</h1>
              <p className="mt-2 text-gray-600">Visualizza e gestisci tutti i lavori in direzione</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button onClick={() => setIsAddModalOpen(true)} className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                Nuova Commessa
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Cerca per codice commessa, indirizzo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Stato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tutti gli stati</SelectItem>
                <SelectItem value="In pianificazione">In pianificazione</SelectItem>
                <SelectItem value="In corso">In corso</SelectItem>
                <SelectItem value="Completato">Completato</SelectItem>
                <SelectItem value="Sospeso">Sospeso</SelectItem>
              </SelectContent>
            </Select>

            <Select value={committenteFilter} onValueChange={setCommittenteFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Committente" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tutti i committenti</SelectItem>
                {uniqueCommittenti.map(committente => (
                  <SelectItem key={committente} value={committente}>
                    {committente}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" className="flex items-center">
              <Filter className="mr-2 h-4 w-4" />
              Filtri avanzati
            </Button>
          </div>
        </div>

        {/* Direzione Lavori Table */}
        <DirezioneLavoriTable 
          lavori={filteredLavori}
          isLoading={isLoading}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>
      
      {/* Modals */}
      <DirezioneLavoriModal
        isOpen={isAddModalOpen}
        onClose={handleModalClose}
        lavoro={null}
        title="Aggiungi Nuovo Lavoro"
      />
      <DirezioneLavoriModal
        isOpen={isEditModalOpen}
        onClose={handleModalClose}
        lavoro={selectedLavoro}
        title="Modifica Lavoro"
      />
      <DeleteDirezioneLavoriModal
        isOpen={isDeleteModalOpen}
        onClose={handleModalClose}
        lavoro={selectedLavoro}
      />
    </div>
  );
}