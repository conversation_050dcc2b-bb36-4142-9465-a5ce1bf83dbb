import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useRoute, Link, useLocation } from "wouter";
import { ArrowLeft, Save, FileText, Calendar, User, MapPin, PenTool, Users } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { saveVerbaleOffline, isOnline, syncOfflineVerbali, convertFileToBase64, savePhotoOffline, getOfflinePhotos, savePDFOffline, getOfflinePDF, downloadOfflinePDF } from "@/lib/offline-storage";
import { generateOfflinePDF } from "@/lib/offline-pdf-generator";
import type { DirezioneLavori, VerbaleDirezioneLavori, DirettoreLavori } from "@shared/schema";
import FirmaDirezioneLavoriModal from "@/components/firma-direzione-lavori-modal";
import PersonePresentiBirezioneLavoriModal from "@/components/persone-presenti-direzione-modal";
import OptionalSection, { OptionalSectionItem } from "@/components/optional-section";
import PageHeader from "@/components/page-header";

type VerbaleWithData = VerbaleDirezioneLavori & {
  tecnico: DirettoreLavori;
  direzioneLavori: DirezioneLavori;
};

type PersonaPresente = {
  id: string;
  nome: string;
  qualifica: string;
};

export default function VerbaleDirezioneLavoriCompilazione() {
  const [match, params] = useRoute("/direzione-lavori/:lavoroId/verbali/:verbaleId");
  const lavoroId = params?.lavoroId ? parseInt(params.lavoroId) : null;
  const verbaleId = params?.verbaleId === "new" ? null : params?.verbaleId ? parseInt(params.verbaleId) : null;
  const isNewVerbale = params?.verbaleId === "new";
  
  const [note, setNote] = useState("");
  const [lavorazioniInCorso, setLavorazioniInCorso] = useState("");
  const [criticita, setCriticita] = useState("");
  const [osservazioni, setOsservazioni] = useState("");
  const [personePresenti, setPersonePresenti] = useState<PersonaPresente[]>([]);
  const [isFirmaModalOpen, setIsFirmaModalOpen] = useState(false);
  const [isPersonePresenteModalOpen, setIsPersonePresenteModalOpen] = useState(false);
  const [numeroProgressivoManuale, setNumeroProgressivoManuale] = useState("");
  
  // Optional sections state - now using multi-item structure
  const [controlliDimensionaliEnabled, setControlliDimensionaliEnabled] = useState(false);
  const [controlliDimensionaliItems, setControlliDimensionaliItems] = useState<OptionalSectionItem[]>([]);
  
  const [nonConformitaEnabled, setNonConformitaEnabled] = useState(false);
  const [nonConformitaItems, setNonConformitaItems] = useState<OptionalSectionItem[]>([]);
  
  const [indicazioniOperativeEnabled, setIndicazioniOperativeEnabled] = useState(false);
  const [indicazioniOperativeItems, setIndicazioniOperativeItems] = useState<OptionalSectionItem[]>([]);
  
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  
  // Check for offline navigation on component mount
  useEffect(() => {
    const checkOfflineNavigation = () => {
      // Look for any offline navigation data in localStorage
      const keys = Object.keys(localStorage);
      const navKeys = keys.filter(key => key.startsWith('offline_nav_'));
      
      navKeys.forEach(key => {
        try {
          const navData = JSON.parse(localStorage.getItem(key) || '{}');
          if (navData.shouldNavigate && navData.targetPath) {
            const timeDiff = Date.now() - navData.timestamp;
            // Only navigate if the navigation data is recent (within 10 seconds)
            if (timeDiff < 10000) {
              console.log('Found recent offline navigation data, executing:', navData);
              localStorage.removeItem(key); // Clean up
              setLocation(navData.targetPath);
            }
          }
        } catch (error) {
          console.error('Error processing offline navigation data:', error);
        }
      });
    };
    
    // Check immediately and also after a short delay
    checkOfflineNavigation();
    setTimeout(checkOfflineNavigation, 500);
  }, [setLocation]);

  const { data: verbale, isLoading } = useQuery<VerbaleWithData>({
    queryKey: [`/api/direzione-lavori/${lavoroId}/verbali/${verbaleId}`],
    enabled: !!lavoroId && !!verbaleId && !isNewVerbale,
  });

  // Fetch direzione lavori data for new verbali
  const { data: direzioneLavori } = useQuery({
    queryKey: [`/api/direzione-lavori/${lavoroId}`],
    enabled: !!lavoroId && isNewVerbale,
  });

  // Get tecnico ID from URL params or localStorage for new verbali
  const getTecnicoForNewVerbale = () => {
    if (!isNewVerbale) return null;
    
    // First try to get from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const tecnicoIdFromUrl = urlParams.get('tecnicoId');
    if (tecnicoIdFromUrl) {
      return { id: parseInt(tecnicoIdFromUrl) };
    }
    
    // Fallback to localStorage
    const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
    if (tempData) {
      try {
        const data = JSON.parse(tempData);
        return data.tecnicoId ? { id: data.tecnicoId } : null;
      } catch (error) {
        return null;
      }
    }
    return null;
  };

  const tecnicoInfo = getTecnicoForNewVerbale();
  
  // Fetch the actual direttore lavori details for new verbali
  const { data: tecnicoData } = useQuery({
    queryKey: [`/api/direttori-lavori/${tecnicoInfo?.id}`],
    enabled: !!tecnicoInfo?.id && isNewVerbale,
  });

  // Debug logging
  console.log('tecnicoInfo:', tecnicoInfo);
  console.log('tecnicoData:', tecnicoData);
  
  // Debug localStorage data
  if (isNewVerbale && lavoroId) {
    const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
    console.log('localStorage temp data:', tempData ? JSON.parse(tempData) : null);
  }

  // Fetch all directors for the direzione lavori to populate people present
  const { data: direttoriData } = useQuery({
    queryKey: [`/api/direzione-lavori/${lavoroId}/direttori`],
    enabled: !!lavoroId && isNewVerbale,
  });

  // Fetch existing verbali to calculate next progressive number for new verbali
  const { data: existingVerbali } = useQuery({
    queryKey: [`/api/direzione-lavori/${lavoroId}/verbali`],
    enabled: !!lavoroId && isNewVerbale,
  });

  // Calculate next progressive number for new verbali
  const getNextProgressiveNumber = () => {
    if (!isNewVerbale || !direzioneLavori || !existingVerbali) return null;
    
    // Count only finalized verbali for THIS specific commessa
    const finalizedCount = existingVerbali.filter((v: any) => v.stato === 'finalizzato').length;
    
    console.log('Progressive number calculation:', {
      codiceCommessa: direzioneLavori.codiceCommessa,
      totalVerbali: existingVerbali.length,
      finalizedCount,
      existingVerbali: existingVerbali.map((v: any) => ({ id: v.id, stato: v.stato, numeroProgressivo: v.numeroProgressivo }))
    });
    
    // Special case for BC074 - start from 20, other commesse start from 1
    let progressiveNumber;
    if (direzioneLavori.codiceCommessa === 'BC074') {
      progressiveNumber = String(finalizedCount + 20).padStart(3, '0');
    } else {
      progressiveNumber = String(finalizedCount + 1).padStart(3, '0');
    }
    
    // Generate full numero progressivo
    const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const fullNumber = `${direzioneLavori.codiceCommessa}-VDL-${progressiveNumber}-${today}`;
    
    console.log('Generated progressive number:', fullNumber);
    return fullNumber;
  };

  const nextNumeroProgressivo = getNextProgressiveNumber();

  // Check if verbale is signed (has pdfPath or firme)
  const isVerbaleSignato = Boolean(verbale?.pdfPath || verbale?.firme);

  // Helper function for aggressive cache invalidation (iPad Safari specific)
  const invalidateVerbaliCache = async (delay: number = 0) => {
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Multiple invalidation strategies for robustness
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori", lavoroId, "verbali"] }),
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori", lavoroId] }),
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori"] }),
      queryClient.refetchQueries({ queryKey: ["/api/direzione-lavori", lavoroId, "verbali"] })
    ]);

    console.log('🔄 Cache invalidated for verbali');
  };

  // Initialize data from temporary storage or existing verbale
  useEffect(() => {
    if (isNewVerbale) {
      // Get temporary data from localStorage for new verbali
      const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
      if (tempData) {
        try {
          const data = JSON.parse(tempData);
          setLavorazioniInCorso(data.lavorazioniInCorso || "");
          setNote(data.note || "");
          setCriticita(data.criticita || "");
          setOsservazioni(data.osservazioni || "");
          
          // Parse persone presenti
          const persone = JSON.parse(data.personePresenti || "[]");
          setPersonePresenti(persone);
          
          // Initialize optional sections with multi-item structure
          const controlliItems = data.controlliDimensionali ? JSON.parse(data.controlliDimensionali) : [];
          setControlliDimensionaliEnabled(controlliItems.length > 0);
          setControlliDimensionaliItems(controlliItems);
          
          const nonConformitaItems = data.nonConformita ? JSON.parse(data.nonConformita) : [];
          setNonConformitaEnabled(nonConformitaItems.length > 0);
          setNonConformitaItems(nonConformitaItems);
          
          const indicazioniItems = data.indicazioniOperative ? JSON.parse(data.indicazioniOperative) : [];
          setIndicazioniOperativeEnabled(indicazioniItems.length > 0);
          setIndicazioniOperativeItems(indicazioniItems);
          
          // Set numero progressivo from data if available
          if (data.numeroProgressivo) {
            setNumeroProgressivoManuale(data.numeroProgressivo);
          }
        } catch (error) {
          console.error("Error parsing temp data:", error);
        }
      }
    } else if (verbale) {
      // Initialize state from existing verbale data
      setNote(verbale.note || "");
      setLavorazioniInCorso(verbale.lavorazioniInCorso || "");
      setCriticita(verbale.criticita || "");
      setOsservazioni(verbale.osservazioni || "");
      
      // Parse persone presenti from JSON string
      try {
        const persone = JSON.parse(verbale.personePresenti || "[]");
        setPersonePresenti(persone);
      } catch (error) {
        console.error("Error parsing persone presenti:", error);
        setPersonePresenti([]);
      }
      
      // Initialize optional sections from existing verbale with multi-item structure
      const controlliItems = verbale.controlliDimensionali ? JSON.parse(verbale.controlliDimensionali) : [];
      setControlliDimensionaliEnabled(controlliItems.length > 0);
      setControlliDimensionaliItems(controlliItems);
      
      const nonConformitaItems = verbale.nonConformita ? JSON.parse(verbale.nonConformita) : [];
      setNonConformitaEnabled(nonConformitaItems.length > 0);
      setNonConformitaItems(nonConformitaItems);
      
      const indicazioniItems = verbale.indicazioniOperative ? JSON.parse(verbale.indicazioniOperative) : [];
      setIndicazioniOperativeEnabled(indicazioniItems.length > 0);
      setIndicazioniOperativeItems(indicazioniItems);
    }
  }, [verbale, isNewVerbale, lavoroId]);

  // Initialize people present from direzione lavori directors for new verbali
  useEffect(() => {
    if (isNewVerbale && direttoriData && Array.isArray(direttoriData) && direttoriData.length > 0 && tecnicoInfo) {
      // Only initialize if no temporary data exists
      const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
      if (!tempData) {
        // Exclude the current tecnico (compiler) from the people present list
        const direttoriAsPersone = direttoriData
          .filter((direttore: any) => direttore.id !== tecnicoInfo.id)
          .map((direttore: any) => ({
            id: `direttore_${direttore.id}`,
            nome: `${direttore.nome} ${direttore.cognome}`,
            qualifica: "Direttore Lavori"
          }));
        
        console.log('Inizializzazione persone presenti:', direttoriAsPersone);
        setPersonePresenti(direttoriAsPersone);
      }
    }
  }, [isNewVerbale, direttoriData, tecnicoInfo, lavoroId]);

  // Initialize numero progressivo for new verbali when data is available
  useEffect(() => {
    if (isNewVerbale && nextNumeroProgressivo && !numeroProgressivoManuale) {
      setNumeroProgressivoManuale(nextNumeroProgressivo);
    }
  }, [isNewVerbale, nextNumeroProgressivo, numeroProgressivoManuale]);

  // Auto-save to localStorage for new verbali
  useEffect(() => {
    if (isNewVerbale && lavoroId) {
      // Get existing data to preserve tecnicoId and other important fields
      const existingData = localStorage.getItem(`verbale_temp_${lavoroId}`);
      let baseData = {};
      if (existingData) {
        try {
          baseData = JSON.parse(existingData);
        } catch (error) {
          console.error("Error parsing existing temp data:", error);
        }
      }

      const data = {
        ...baseData, // Preserve existing data like tecnicoId
        lavorazioniInCorso,
        note,
        criticita,
        osservazioni,
        numeroProgressivo: numeroProgressivoManuale,
        personePresenti: JSON.stringify(personePresenti),
        controlliDimensionali: controlliDimensionaliEnabled ? JSON.stringify(controlliDimensionaliItems) : null,
        nonConformita: nonConformitaEnabled ? JSON.stringify(nonConformitaItems) : null,
        indicazioniOperative: indicazioniOperativeEnabled ? JSON.stringify(indicazioniOperativeItems) : null
      };
      localStorage.setItem(`verbale_temp_${lavoroId}`, JSON.stringify(data));
    }
  }, [
    isNewVerbale, 
    lavoroId, 
    lavorazioniInCorso, 
    note, 
    criticita, 
    osservazioni, 
    numeroProgressivoManuale,
    personePresenti,
    controlliDimensionaliEnabled,
    controlliDimensionaliItems,
    nonConformitaEnabled,
    nonConformitaItems,
    indicazioniOperativeEnabled,
    indicazioniOperativeItems
  ]);

  const saveMutation = useMutation({
    mutationFn: async () => {
      const data = {
        note: note.trim(),
        lavorazioniInCorso: lavorazioniInCorso.trim(),
        criticita: criticita.trim(),
        osservazioni: osservazioni.trim(),
        personePresenti: JSON.stringify(personePresenti),
        numeroProgressivo: numeroProgressivoManuale, // Include manual progressive number
        stato: 'bozza', // Salva sempre come bozza quando si clicca "Salva"
        // Optional sections data - now using multi-item structure
        controlliDimensionali: controlliDimensionaliEnabled ? JSON.stringify(controlliDimensionaliItems) : null,
        nonConformita: nonConformitaEnabled ? JSON.stringify(nonConformitaItems) : null,
        indicazioniOperative: indicazioniOperativeEnabled ? JSON.stringify(indicazioniOperativeItems) : null
      };
      
      // Check if online before attempting network request
      if (!isOnline()) {
        console.log('Offline mode detected, saving to localStorage');
        
        if (isNewVerbale) {
          const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
          if (!tempData) {
            throw new Error("Dati temporanei del verbale non trovati");
          }
          
          const parsedTempData = JSON.parse(tempData);
          const verbaleData = {
            ...parsedTempData,
            ...data,
            stato: 'bozza'
          };
          
          const offlineId = await saveVerbaleOffline(lavoroId!, verbaleData, 'bozza');
          return { id: offlineId, offline: true, queued: true };
        } else {
          // For existing verbali, save update offline
          const offlineId = await saveVerbaleOffline(lavoroId!, { ...data, id: verbaleId }, 'bozza');
          return { id: offlineId, offline: true, queued: true };
        }
      }
      
      // Online mode - try network request
      try {
        if (isNewVerbale) {
          // Create new verbale as draft from localStorage temp data
          const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
          if (!tempData) {
            throw new Error("Dati temporanei del verbale non trovati");
          }
          
          const parsedTempData = JSON.parse(tempData);
          const verbaleData = {
            ...parsedTempData,
            ...data,
            stato: 'bozza'
          };
          
          const response = await apiRequest("POST", `/api/direzione-lavori/${lavoroId}/verbali`, verbaleData);
          const result = await response.json();
          
          // Clear temp data after creating the verbale
          localStorage.removeItem(`verbale_temp_${lavoroId}`);
          
          return result;
        } else {
          // Update existing verbale maintaining its status or setting as draft
          const response = await apiRequest("PUT", `/api/direzione-lavori/${lavoroId}/verbali/${verbaleId}`, data);
          return await response.json();
        }
      } catch (error) {
        console.log('Network error, falling back to offline mode:', error);
        
        // Network failed, save offline as fallback
        if (isNewVerbale) {
          const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
          const parsedTempData = tempData ? JSON.parse(tempData) : {};
          const verbaleData = {
            ...parsedTempData,
            ...data,
            stato: 'bozza'
          };
          
          const offlineId = await saveVerbaleOffline(lavoroId!, verbaleData, 'bozza');
          return { id: offlineId, offline: true, queued: true };
        } else {
          const offlineId = await saveVerbaleOffline(lavoroId!, { ...data, id: verbaleId }, 'bozza');
          return { id: offlineId, offline: true, queued: true };
        }
      }
    },
    onSuccess: async (response) => {
      // Check if the response indicates offline mode
      if (response?.offline) {
        toast({
          title: "Verbale salvato offline",
          description: "Il verbale sarà sincronizzato quando torni online.",
          variant: "default",
        });
        // Keep the user on the current page in offline mode
        await invalidateVerbaliCache();
      } else {
        toast({
          title: "Successo",
          description: "Verbale salvato in bozza con successo.",
        });

        // Aggressive cache invalidation for iPad Safari
        await invalidateVerbaliCache();

        if (isNewVerbale && response) {
          // Navigate to the newly created verbale only if online
          // Add delay for iPad Safari to ensure cache is updated
          const isIPad = /iPad|iPhone|iPod/.test(navigator.userAgent);
          if (isIPad) {
            setTimeout(() => {
              setLocation(`/direzione-lavori/${lavoroId}/verbali/${response.id}`);
            }, 1000);
          } else {
            setLocation(`/direzione-lavori/${lavoroId}/verbali/${response.id}`);
          }
        }
      }
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante il salvataggio del verbale.",
        variant: "destructive",
      });
    },
  });

  const createVerbaleConFirmeMutation = useMutation({
    mutationFn: async (firme: Record<string, string>) => {
      let currentData = {
        lavorazioniInCorso,
        criticita,
        note,
        osservazioni,
        personePresenti,
      };

      // For new verbali, get the latest data from localStorage
      if (isNewVerbale) {
        const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
        if (tempData) {
          try {
            const parsed = JSON.parse(tempData);
            currentData = {
              lavorazioniInCorso: parsed.lavorazioniInCorso || '',
              criticita: parsed.criticita || '',
              note: parsed.note || '',
              osservazioni: parsed.osservazioni || '',
              personePresenti: JSON.parse(parsed.personePresenti || '[]'),
            };
            // Also get numero progressivo from temp data
            if (parsed.numeroProgressivo) {
              setNumeroProgressivoManuale(parsed.numeroProgressivo);
            }
          } catch (error) {
            console.error("Error parsing temp data for verbale creation:", error);
          }
        }
      }

      // For new verbali, get tecnicoId from temp data
      let tecnicoId = null;
      if (isNewVerbale) {
        const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
        if (tempData) {
          const parsedTempData = JSON.parse(tempData);
          tecnicoId = parsedTempData.tecnicoId;
        }
      } else if (verbale) {
        tecnicoId = verbale.tecnicoId;
      }

      // Get numero progressivo from temp data for new verbali
      let numeroProgressivo = numeroProgressivoManuale;
      if (isNewVerbale) {
        const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
        if (tempData) {
          try {
            const parsed = JSON.parse(tempData);
            if (parsed.numeroProgressivo) {
              numeroProgressivo = parsed.numeroProgressivo;
            }
          } catch (error) {
            console.error("Error parsing numero progressivo from temp data:", error);
          }
        }
      }

      // Extract photos from items
      const extractPhotos = (items: any[]) => {
        const allPhotos: string[] = [];
        items.forEach(item => {
          if (item.photos && Array.isArray(item.photos)) {
            allPhotos.push(...item.photos.filter(p => p && p !== null));
          }
        });
        return allPhotos.length > 0 ? allPhotos : [];
      };

      const verbaleData = {
        tecnicoId: tecnicoId, // Include the selected director ID
        numeroProgressivo: numeroProgressivo, // Include the manual progressive number
        lavorazioniInCorso: currentData.lavorazioniInCorso,
        criticita: currentData.criticita || null,
        note: currentData.note || null,
        osservazioni: currentData.osservazioni || null,
        personePresenti: JSON.stringify(currentData.personePresenti),
        firme: firme,
        stato: 'finalizzato', // Finalizza il verbale quando viene firmato
        verbaleId: isNewVerbale ? null : verbaleId, // Include verbale ID if updating existing
        // Optional sections data
        controlliDimensionali: controlliDimensionaliEnabled ? JSON.stringify(controlliDimensionaliItems) : null,
        nonConformita: nonConformitaEnabled ? JSON.stringify(nonConformitaItems) : null,
        indicazioniOperative: indicazioniOperativeEnabled ? JSON.stringify(indicazioniOperativeItems) : null,
        // Photo arrays
        controlliDimensionaliFoto: controlliDimensionaliEnabled ? extractPhotos(controlliDimensionaliItems) : [],
        nonConformitaFoto: nonConformitaEnabled ? extractPhotos(nonConformitaItems) : [],
        indicazioniOperativeFoto: indicazioniOperativeEnabled ? extractPhotos(indicazioniOperativeItems) : []
      };
      
      // Aggiungi data corrente al verbale (formato YYYY-MM-DD)
      verbaleData.data = new Date().toISOString().split('T')[0];
      
      // Check if online before attempting network request
      const deviceOnline = navigator.onLine;
      const hookOnline = isOnline();
      console.log('Online status check:', { deviceOnline, hookOnline, userAgent: navigator.userAgent });
      
      if (!deviceOnline || !hookOnline) {
        console.log('Offline mode detected for finalized verbale, saving to localStorage');
        const offlineId = await saveVerbaleOffline(lavoroId!, verbaleData, 'finalizzato');
        return { id: offlineId, offline: true, queued: true };
      }
      
      try {
        const response = await apiRequest("POST", `/api/direzione-lavori/${lavoroId}/verbali/con-firme`, verbaleData);
        return response.json();
      } catch (error) {
        console.log('Network error for finalized verbale, falling back to offline mode:', error);
        // Don't save again here - it's already saved above if offline
        throw error; // Let the onError handler manage this
      }
    },
    onSuccess: async (response) => {
      console.log('Firma verbale response:', response);
      // Check if the response indicates offline mode
      if (response?.offline) {
        console.log('Processing offline response, navigating to:', `/direzione-lavori/${lavoroId}`);
        toast({
          title: "Verbale salvato offline",
          description: "Il verbale firmato sarà sincronizzato quando torni online.",
          variant: "default",
        });
        // Keep localStorage for offline verbali (don't clean up)
        setIsFirmaModalOpen(false);
        
        // Use multiple strategies to ensure navigation works on all devices
        const targetPath = `/direzione-lavori/${lavoroId}`;
        const isTablet = /iPad|Android/i.test(navigator.userAgent) && window.innerWidth >= 768;
        
        console.log('Device detection:', { isTablet, userAgent: navigator.userAgent, width: window.innerWidth });
        
        if (isTablet) {
          // For tablets, use more aggressive navigation
          console.log('Tablet detected, using aggressive navigation');
          setTimeout(() => {
            console.log('Executing tablet-specific navigation');
            window.location.href = window.location.origin + targetPath;
          }, 300);
        } else {
          // Strategy 1: Immediate navigation
          setLocation(targetPath);
          
          // Strategy 2: Delayed navigation for tablet compatibility
          setTimeout(() => {
            console.log('Executing delayed navigation');
            setLocation(targetPath);
          }, 50);
          
          // Strategy 3: Fallback navigation after longer delay
          setTimeout(() => {
            console.log('Executing fallback navigation');
            window.location.hash = '#' + targetPath;
            setLocation(targetPath);
          }, 200);
        }
      } else {
        // Clean up localStorage for new verbali only when online
        if (isNewVerbale) {
          localStorage.removeItem(`verbale_temp_${lavoroId}`);
        }
        
        // Aggressive cache invalidation for iPad Safari
        await invalidateVerbaliCache();

        toast({
          title: "Successo",
          description: "Verbale firmato e PDF generato con successo",
        });
        setIsFirmaModalOpen(false);

        // Navigate back to verbali list with delay for iPad Safari
        const isIPad = /iPad|iPhone|iPod/.test(navigator.userAgent);
        if (isIPad) {
          setTimeout(() => {
            setLocation(`/direzione-lavori/${lavoroId}`);
          }, 1500); // Longer delay for finalized verbale
        } else {
          setLocation(`/direzione-lavori/${lavoroId}`);
        }
      }
    },
    onError: async (error: any) => {
      console.log('Firma verbale error:', error);
      // Check if it's an offline error
      if (error?.message?.includes('offline') || error?.message?.includes('Failed to fetch')) {
        console.log('Processing error as offline, saving and navigating');
        
        // Save offline only if not already saved
        const completeVerbaleData = {
          tecnicoId: tecnicoInfo?.id || null,
          numeroProgressivo: numeroProgressivoManuale || nextNumeroProgressivo,
          data: new Date().toISOString().split('T')[0],  // Aggiungi data corrente (formato YYYY-MM-DD)
          lavorazioniInCorso: lavorazioniInCorso,
          criticita: criticita,
          note: note,
          osservazioni: osservazioni,
          personePresenti: JSON.stringify(personePresenti),
          firme: firme,
          stato: 'finalizzato',
          controlliDimensionali: controlliDimensionaliEnabled ? JSON.stringify(controlliDimensionaliItems) : null,
          nonConformita: nonConformitaEnabled ? JSON.stringify(nonConformitaItems) : null,
          indicazioniOperative: indicazioniOperativeEnabled ? JSON.stringify(indicazioniOperativeItems) : null
        };
        
        const offlineId = await saveVerbaleOffline(lavoroId!, completeVerbaleData, 'finalizzato');
        
        toast({
          title: "Modalità offline",
          description: "Il verbale è stato salvato offline e sarà sincronizzato quando torni online",
          variant: "default",
        });
        
        // Force close modal and navigate
        setIsFirmaModalOpen(false);
        
        const targetPath = `/direzione-lavori/${lavoroId}`;
        const isTablet = /iPad|Android/i.test(navigator.userAgent) && window.innerWidth >= 768;
        
        if (isTablet) {
          setTimeout(() => {
            window.location.href = window.location.origin + targetPath;
          }, 300);
        } else {
          setTimeout(() => {
            setLocation(targetPath);
          }, 100);
        }
      } else {
        toast({
          title: "Errore",
          description: error?.message || "Errore durante la firma del verbale",
          variant: "destructive",
        });
      }
    },
  });

  // Mutation for unlocking verbale
  const unlockMutation = useMutation({
    mutationFn: async (reason: string) => {
      const response = await apiRequest("POST", `/api/direzione-lavori/${lavoroId}/verbali/${verbaleId}/unlock`, { reason });
      return response.json();
    },
    onSuccess: async () => {
      toast({
        title: "Verbale sbloccato",
        description: "Il verbale è ora modificabile. Le firme precedenti sono state cancellate.",
      });

      // Aggressive cache invalidation
      await invalidateVerbaliCache();

      // Refresh the current verbale data
      queryClient.invalidateQueries({ queryKey: ["/api/direzione-lavori", lavoroId, "verbali", verbaleId] });
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error?.message || "Errore durante lo sblocco del verbale",
        variant: "destructive",
      });
    }
  });

  const handleSave = () => {
    // Per i nuovi verbali, assicurati che i dati temporanei siano inizializzati
    if (isNewVerbale && lavoroId) {
      const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
      if (!tempData) {
        // Inizializza i dati temporanei se non esistono ancora
        const initialData = {
          tecnicoId: tecnicoInfo?.id || null,
          numeroProgressivo: numeroProgressivoManuale || nextNumeroProgressivo,
          lavorazioniInCorso: lavorazioniInCorso || '',
          criticita: criticita || '',
          note: note || '',
          osservazioni: osservazioni || '',
          personePresenti: JSON.stringify(personePresenti || []),
          controlliDimensionali: controlliDimensionaliEnabled ? JSON.stringify(controlliDimensionaliItems) : null,
          nonConformita: nonConformitaEnabled ? JSON.stringify(nonConformitaItems) : null,
          indicazioniOperative: indicazioniOperativeEnabled ? JSON.stringify(indicazioniOperativeItems) : null
        };
        localStorage.setItem(`verbale_temp_${lavoroId}`, JSON.stringify(initialData));
      }
    }
    
    saveMutation.mutate();
  };

  const handleFirmaVerbale = () => {
    // Validazione: controllare che tutti i campi obbligatori siano compilati
    if (!lavorazioniInCorso.trim()) {
      toast({
        title: "Errore",
        description: "Il campo 'Lavorazioni in Corso' è obbligatorio.",
        variant: "destructive",
      });
      return;
    }

    // Apri il modal per le firme
    setIsFirmaModalOpen(true);
  };

  const handleOfflineVerbaleFinalization = async (firme: Record<string, string>) => {
    try {
      console.log('Starting offline verbale finalization...');
      
      // Get current verbale data
      let currentData = {
        lavorazioniInCorso,
        criticita,
        note,
        osservazioni,
        personePresenti,
      };

      // Get tecnico data
      const tecnicoInfo = tecnicoData || (verbale?.tecnico ? verbale.tecnico : null);
      if (!tecnicoInfo) {
        toast({
          title: "Errore",
          description: "Informazioni tecnico mancanti per la generazione PDF offline",
          variant: "destructive",
        });
        return;
      }

      // Get numero progressivo
      let numeroProgressivo = numeroProgressivoManuale;
      if (isNewVerbale) {
        const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
        if (tempData) {
          const parsed = JSON.parse(tempData);
          numeroProgressivo = parsed.numeroProgressivo || numeroProgressivo;
        }
      }

      // Get offline photos for all sections
      const verbaleIdStr = verbaleId?.toString() || `temp_${lavoroId}`;
      const offlinePhotos = await getOfflinePhotos(verbaleIdStr);
      
      // Convert photo names to base64 data for optional sections
      const convertPhotosToBase64 = (photos: string[]) => {
        return photos.map(photoName => {
          const base64Data = offlinePhotos[photoName];
          if (base64Data) {
            return base64Data;
          }
          // If not found in offline photos, assume it's a server photo (for hybrid cases)
          return `/api/photo/${photoName}`;
        }).filter(Boolean);
      };

      // Prepare PDF data
      const pdfData = {
        numeroProgressivo,
        data: new Date().toLocaleDateString('it-IT'),
        lavorazioniInCorso: currentData.lavorazioniInCorso,
        criticita: currentData.criticita,
        note: currentData.note,
        osservazioni: currentData.osservazioni,
        personePresenti: currentData.personePresenti,
        tecnicoNome: `${tecnicoInfo.nome} ${tecnicoInfo.cognome}`,
        tecnicoQualifica: tecnicoInfo.qualifica,
        codiceCommessa: direzioneLavori?.codiceCommessa || '',
        descrizioneCommessa: direzioneLavori?.descrizioneOperaEvento || '',
        controlliDimensionali: controlliDimensionaliEnabled ? controlliDimensionaliItems.map(item => ({
          text: item.text,
          photo: convertPhotosToBase64(item.photos)[0], // Take first photo
          drawing: item.drawing
        })) : undefined,
        nonConformita: nonConformitaEnabled ? nonConformitaItems.map(item => ({
          text: item.text,
          photo: convertPhotosToBase64(item.photos)[0],
          drawing: item.drawing
        })) : undefined,
        indicazioniOperative: indicazioniOperativeEnabled ? indicazioniOperativeItems.map(item => ({
          text: item.text,
          photo: convertPhotosToBase64(item.photos)[0],
          drawing: item.drawing
        })) : undefined,
        firme
      };

      console.log('Generating PDF offline with data:', pdfData);
      
      // Generate PDF
      const pdfBase64 = generateOfflinePDF(pdfData);
      
      // Save PDF offline
      const offlineVerbaleId = await saveVerbaleOffline(lavoroId!, {
        ...pdfData,
        firme: JSON.stringify(firme),
        personePresenti: JSON.stringify(currentData.personePresenti),
        controlliDimensionali: controlliDimensionaliEnabled ? JSON.stringify(controlliDimensionaliItems) : null,
        nonConformita: nonConformitaEnabled ? JSON.stringify(nonConformitaItems) : null,
        indicazioniOperative: indicazioniOperativeEnabled ? JSON.stringify(indicazioniOperativeItems) : null,
      }, 'finalizzato');
      
      await savePDFOffline(offlineVerbaleId, pdfBase64);
      
      toast({
        title: "Successo",
        description: "Verbale salvato offline con PDF generato",
      });
      
      // Clear temporary data
      localStorage.removeItem(`verbale_temp_${lavoroId}`);
      
      // Navigate back to commessa page
      const targetPath = `/direzione-lavori/${lavoroId}`;
      const isTablet = /iPad|Android/i.test(navigator.userAgent) && window.innerWidth >= 768;
      
      if (isTablet) {
        setTimeout(() => {
          window.location.href = window.location.origin + targetPath;
        }, 1000);
      } else {
        setLocation(targetPath);
      }
      
    } catch (error) {
      console.error('Error in offline verbale finalization:', error);
      toast({
        title: "Errore",
        description: "Errore durante la generazione PDF offline",
        variant: "destructive",
      });
    }
  };

  const handleFirmeComplete = async (firme: Record<string, string>) => {
    const deviceOnline = navigator.onLine;
    const hookOnline = isOnline();
    const isOffline = !deviceOnline || !hookOnline;
    
    console.log('HandleFirmeComplete - Mode:', { isOffline, deviceOnline, hookOnline });
    
    if (isOffline) {
      // Offline mode: generate PDF locally and save to localStorage
      await handleOfflineVerbaleFinalization(firme);
    } else {
      // Online mode: proceed with normal server-side PDF generation
      createVerbaleConFirmeMutation.mutate(firme);
    }
  };

  const handlePersonePresenteUpdate = (nuovePersone: PersonaPresente[]) => {
    setPersonePresenti(nuovePersone);
    setIsPersonePresenteModalOpen(false);
    
    // Save to localStorage for new verbali
    if (isNewVerbale && lavoroId) {
      const tempData = localStorage.getItem(`verbale_temp_${lavoroId}`);
      if (tempData) {
        try {
          const data = JSON.parse(tempData);
          data.personePresenti = JSON.stringify(nuovePersone);
          localStorage.setItem(`verbale_temp_${lavoroId}`, JSON.stringify(data));
        } catch (error) {
          console.error('Error updating localStorage with people:', error);
        }
      }
    } else if (verbaleId && lavoroId) {
      // For existing verbali, save the people immediately
      saveMutation.mutate({
        personePresenti: JSON.stringify(nuovePersone)
      });
    }
    
    toast({
      title: "Successo", 
      description: "Persone presenti aggiornate con successo.",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getCurrentTecnicoName = () => {
    if (isNewVerbale) {
      return 'Da selezionare';
    }
    return `${verbale?.tecnico.nome || ''} ${verbale?.tecnico.cognome || ''}`;
  };

  if (!match) return null;

  if (isLoading && !isNewVerbale) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="h-32 bg-gray-200 rounded mb-6"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!verbale && !isNewVerbale) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Verbale non trovato</h1>
          <Link href="/direzione-lavori">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Torna alla lista
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gray-50">
      {/* Header Unificato */}
      <PageHeader
        title={isNewVerbale ? "Nuovo Verbale" : `Verbale #${verbale?.numeroProgressivo}`}
        subtitle={
          isNewVerbale ?
            `${direzioneLavori?.codiceCommessa} - ${direzioneLavori?.oggetto}` :
            `${verbale?.direzioneLavori.codiceCommessa} - ${verbale?.direzioneLavori.oggetto}`
        }
        actions={
          <div className="flex items-center space-x-3">
            {/* Badge stato verbale */}
            {isVerbaleSignato ? (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Firmato
              </Badge>
            ) : verbale?.stato === 'bozza' ? (
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                Bozza
              </Badge>
            ) : null}

            {!isVerbaleSignato && (
              <>
                <Button
                  onClick={handleSave}
                  disabled={saveMutation.isPending}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="mr-2 h-4 w-4" />
                  {saveMutation.isPending ? "Salvataggio..." : "Salva"}
                </Button>

                <Button
                  onClick={handleFirmaVerbale}
                  variant="outline"
                  className="border-green-600 text-green-600 hover:bg-green-50"
                >
                  <PenTool className="mr-2 h-4 w-4" />
                  Firma Verbale
                </Button>
              </>
            )}
            {isVerbaleSignato && (
              <>
                <Button variant="outline" className="text-green-600 border-green-600" disabled>
                  <FileText className="mr-2 h-4 w-4" />
                  Verbale Firmato
                </Button>

                {verbale?.stato === 'finalizzato' && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" className="bg-yellow-50 border-yellow-300 hover:bg-yellow-100 text-yellow-700">
                        🔓 Sblocca per Modifica
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Sbloccare verbale per modifica?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Questa azione renderà il verbale nuovamente modificabile ma cancellerà tutte le firme esistenti.
                          Sarà necessario firmare nuovamente il verbale dopo le modifiche.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Annulla</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => unlockMutation.mutate("Richiesta modifica utente")}
                          className="bg-yellow-600 hover:bg-yellow-700"
                        >
                          Conferma Sblocco
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </>
            )}
          </div>
        }
      />
      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Dati Riepilogativi del Lavoro */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">
                Informazioni Verbale
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className={isNewVerbale ? "col-span-2" : ""}>
                  <span className="font-medium">Numero:</span>
                  {isNewVerbale ? (
                    <div className="mt-1">
                      <Input
                        value={numeroProgressivoManuale}
                        onChange={(e) => setNumeroProgressivoManuale(e.target.value)}
                        placeholder="Numero progressivo verbale"
                        className="text-sm max-w-xs"
                        disabled={isVerbaleSignato}
                      />
                    </div>
                  ) : (
                    <span className="ml-2">{verbale?.numeroProgressivo}</span>
                  )}
                </div>
                <div>
                  <span className="font-medium">Data:</span> {
                    isNewVerbale ? 
                      new Date().toLocaleDateString('it-IT') : 
                      formatDate(verbale?.data || new Date().toISOString())
                  }
                </div>
                <div>
                  <span className="font-medium">Direttore:</span> {
                    isNewVerbale ? 
                      getCurrentTecnicoName() :
                      `${verbale?.tecnico.nome} ${verbale?.tecnico.cognome}`
                  }
                </div>
                <div>
                  <span className="font-medium">Commessa:</span> {
                    isNewVerbale ? direzioneLavori?.codiceCommessa : verbale?.direzioneLavori.codiceCommessa
                  }
                </div>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Dati Riepilogativi del Progetto
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Committente</Label>
                      <p className="text-sm text-gray-900 mt-1">
                        {isNewVerbale ? direzioneLavori?.committente : verbale?.direzioneLavori.committente}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Indirizzo</Label>
                      <p className="text-sm text-gray-900 mt-1 flex items-start">
                        <MapPin className="mr-1 h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                        {isNewVerbale ? direzioneLavori?.indirizzo : verbale?.direzioneLavori.indirizzo}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Descrizione Lavori</Label>
                      <p className="text-sm text-gray-900 mt-1">
                        {isNewVerbale ? direzioneLavori?.oggetto : verbale?.direzioneLavori.oggetto}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Contratto</Label>
                      <p className="text-sm text-gray-900 mt-1">
                        {isNewVerbale ? (direzioneLavori?.contratto || 'Non specificato') : (verbale?.direzioneLavori.contratto || 'Non specificato')}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">CIG</Label>
                      <p className="text-sm text-gray-900 mt-1">
                        {isNewVerbale ? (direzioneLavori?.cig || 'Non specificato') : (verbale?.direzioneLavori.cig || 'Non specificato')}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Lavorazioni in Corso */}
            <Card>
              <CardHeader>
                <CardTitle>Stato di Avanzamento Lavori</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={lavorazioniInCorso}
                  onChange={(e) => setLavorazioniInCorso(e.target.value)}
                  placeholder="Descrizione sintetica dello stato di avanzamento lavori, con un confronto con il cronoprogramma. Evidenziare significative differenze di avanzamento rispetto al cronoprogramma"
                  rows={4}
                  className="resize-none pl-[13px] pr-[13px]"
                  disabled={isVerbaleSignato}
                />
              </CardContent>
            </Card>

            {/* Criticità e Osservazioni */}
            <Card>
              <CardHeader>
                <CardTitle>Aspetti Tecnici</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={criticita}
                  onChange={(e) => setCriticita(e.target.value)}
                  placeholder="Analizzare eventuali criticità riscontrate nelle lavorazioni previste nel progetto"
                  rows={4}
                  className="resize-none"
                  disabled={isVerbaleSignato}
                />
              </CardContent>
            </Card>

            {/* Optional Sections */}
            <OptionalSection
              title="Controlli dimensionali e geometrici"
              isEnabled={controlliDimensionaliEnabled}
              onEnabledChange={setControlliDimensionaliEnabled}
              items={controlliDimensionaliItems}
              onItemsChange={setControlliDimensionaliItems}
              disabled={isVerbaleSignato}
              textPlaceholder="Descrivere i controlli dimensionali e geometrici effettuati..."
              verbaleId={verbaleId?.toString() || (isNewVerbale ? `temp_${lavoroId}` : undefined)}
            />

            <OptionalSection
              title="Eventuali non conformità riscontrate"
              isEnabled={nonConformitaEnabled}
              onEnabledChange={setNonConformitaEnabled}
              items={nonConformitaItems}
              onItemsChange={setNonConformitaItems}
              disabled={isVerbaleSignato}
              textPlaceholder="Descrivere le non conformità riscontrate e le azioni correttive..."
              verbaleId={verbaleId?.toString() || (isNewVerbale ? `temp_${lavoroId}` : undefined)}
            />

            <OptionalSection
              title="Indicazioni operative fornite all'impresa"
              isEnabled={indicazioniOperativeEnabled}
              onEnabledChange={setIndicazioniOperativeEnabled}
              items={indicazioniOperativeItems}
              onItemsChange={setIndicazioniOperativeItems}
              disabled={isVerbaleSignato}
              textPlaceholder="Descrivere le indicazioni operative fornite all'impresa..."
              verbaleId={verbaleId?.toString() || (isNewVerbale ? `temp_${lavoroId}` : undefined)}
            />

            <Card>
              <CardHeader>
                <CardTitle>Aspetti sulla sicurezza del cantiere</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={osservazioni}
                  onChange={(e) => setOsservazioni(e.target.value)}
                  placeholder="Verificare eventuali osservazioni riguardanti la sicurezza del cantiere"
                  rows={4}
                  className="resize-none"
                  disabled={isVerbaleSignato}
                />
              </CardContent>
            </Card>

            {/* Note Aggiuntive */}
            <Card>
              <CardHeader>
                <CardTitle>Note Aggiuntive</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  placeholder="Inserisci note aggiuntive..."
                  rows={3}
                  className="resize-none"
                  disabled={isVerbaleSignato}
                />
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Info Verbale */}
            <Card>
              <CardHeader>
                <CardTitle>Informazioni Verbale</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">
                    {isNewVerbale ? new Date().toLocaleDateString('it-IT') : formatDate(verbale?.data || new Date().toISOString())}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">
                    {isNewVerbale ? "Nuovo verbale" : `${verbale?.tecnico.nome} ${verbale?.tecnico.cognome}`}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">
                    {isNewVerbale ? direzioneLavori?.indirizzo : verbale?.direzioneLavori.indirizzo}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Persone Presenti */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Persone Presenti</CardTitle>
                  {!isVerbaleSignato && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsPersonePresenteModalOpen(true)}
                      className="text-blue-600 border-blue-600 hover:bg-blue-50"
                    >
                      <Users className="h-4 w-4 mr-1" />
                      Gestisci
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Include always the work director who compiles the verbale */}
                  {(verbale?.tecnico || tecnicoData) && (
                    <div className="border-l-4 border-green-500 pl-3">
                      <div className="font-medium text-sm">
                        {verbale?.tecnico 
                          ? `${verbale.tecnico.nome} ${verbale.tecnico.cognome}` 
                          : tecnicoData 
                            ? `${tecnicoData.nome} ${tecnicoData.cognome}`
                            : 'Direttore Lavori'
                        }
                      </div>
                      <div className="text-xs text-gray-600">Direttore Lavori Compilatore</div>
                    </div>
                  )}
                  
                  {/* Include all manually added people */}
                  {personePresenti.map((persona, index) => (
                    <div key={persona.id || index} className="border-l-4 border-blue-500 pl-3">
                      <div className="font-medium text-sm">{persona.nome}</div>
                      <div className="text-xs text-gray-600">{persona.qualifica}</div>
                    </div>
                  ))}
                  
                  {!((verbale?.tecnico || tecnicoData) || personePresenti.length > 0) && (
                    <p className="text-sm text-gray-500">Nessuna persona registrata</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Stato */}
            <Card>
              <CardHeader>
                <CardTitle>Stato</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge variant="secondary" className="w-full justify-center">
                  In Compilazione
                </Badge>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      {/* Modal per le firme */}
      <FirmaDirezioneLavoriModal
        isOpen={isFirmaModalOpen}
        onClose={() => setIsFirmaModalOpen(false)}
        tecnico={
          verbale?.tecnico 
            ? { nome: verbale.tecnico.nome, cognome: verbale.tecnico.cognome } 
            : tecnicoData 
              ? { nome: tecnicoData.nome, cognome: tecnicoData.cognome }
              : undefined
        }
        personePresenti={personePresenti}
        onFirmeComplete={handleFirmeComplete}
      />
      
      {/* Modal per gestire persone presenti durante la compilazione */}
      <PersonePresentiBirezioneLavoriModal
        isOpen={isPersonePresenteModalOpen}
        onClose={() => setIsPersonePresenteModalOpen(false)}
        lavoro={verbale?.direzioneLavori || direzioneLavori || null}
        verbale={verbale}
        verbaleDataTemp={{
          personePresenti: JSON.stringify(personePresenti),
          tecnicoId: verbale?.tecnicoId || tecnicoInfo?.id
        }}
        tecnicoCompilatore={verbale?.tecnico || tecnicoData || null}
        onVerbaleCreated={(data) => {
          // Parse the persone presenti from the returned data
          const nuovePersone = JSON.parse(data.personePresenti || '[]');
          handlePersonePresenteUpdate(nuovePersone);
        }}
      />
    </div>
  );
}