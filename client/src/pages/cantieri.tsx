import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { HardHat, Plus, Search, Filter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import CantieriTable from "@/components/cantieri-table";
import CantiereModal from "@/components/cantiere-modal";
import DeleteConfirmationModal from "@/components/delete-confirmation-modal";
import { getQueryFn } from "@/lib/queryClient";
import type { Cantiere } from "@shared/schema";

export default function Cantieri() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [committenteFilter, setCommittenteFilter] = useState("all");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null);

  const { data: cantieri = [], isLoading, refetch } = useQuery<Cantiere[]>({
    queryKey: [
      searchTerm 
        ? `/api/cantieri?search=${encodeURIComponent(searchTerm)}`
        : "/api/cantieri", 
      searchTerm
    ],
    queryFn: getQueryFn({ on401: "throw" }),
  });

  // Filter cantieri based on status and committente
  const filteredCantieri = cantieri.filter(cantiere => {
    if (statusFilter && statusFilter !== "all" && cantiere.stato !== statusFilter) return false;
    if (committenteFilter && committenteFilter !== "all" && cantiere.committente !== committenteFilter) return false;
    return true;
  });

  // Get unique committenti for filter dropdown (filter out empty values)
  const uniqueCommittenti = Array.from(new Set(cantieri.map(c => c.committente).filter(c => c && c.trim() !== "")));

  const handleEdit = (cantiere: Cantiere) => {
    setSelectedCantiere(cantiere);
    setIsEditModalOpen(true);
  };

  const handleDelete = (cantiere: Cantiere) => {
    setSelectedCantiere(cantiere);
    setIsDeleteModalOpen(true);
  };

  const handleModalClose = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
    setSelectedCantiere(null);
    refetch();
  };

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Coordinamento della Sicurezza</h1>
              <p className="mt-2 text-gray-600">Visualizza e gestisci gli incarichi di CSE</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button onClick={() => setIsAddModalOpen(true)} className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                Nuovo Cantiere
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Cerca per codice commessa, indirizzo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Stato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tutti gli stati</SelectItem>
                <SelectItem value="In pianificazione">In pianificazione</SelectItem>
                <SelectItem value="In corso">In corso</SelectItem>
                <SelectItem value="Completato">Completato</SelectItem>
                <SelectItem value="Sospeso">Sospeso</SelectItem>
              </SelectContent>
            </Select>

            <Select value={committenteFilter} onValueChange={setCommittenteFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Committente" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tutti i committenti</SelectItem>
                {uniqueCommittenti.map(committente => (
                  <SelectItem key={committente} value={committente}>
                    {committente}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" className="flex items-center">
              <Filter className="mr-2 h-4 w-4" />
              Filtri avanzati
            </Button>
          </div>
        </div>

        {/* Cantieri Table */}
        <CantieriTable 
          cantieri={filteredCantieri}
          isLoading={isLoading}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>
      
      {/* Modals */}
      <CantiereModal
        isOpen={isAddModalOpen}
        onClose={handleModalClose}
        cantiere={null}
        title="Aggiungi Nuovo Cantiere"
      />
      <CantiereModal
        isOpen={isEditModalOpen}
        onClose={handleModalClose}
        cantiere={selectedCantiere}
        title="Modifica Cantiere"
      />
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleModalClose}
        cantiere={selectedCantiere}
      />
    </div>
  );
}