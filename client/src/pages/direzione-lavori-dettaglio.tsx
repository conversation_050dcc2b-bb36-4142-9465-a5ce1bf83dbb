import { useState, useMemo } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useRoute, useLocation } from "wouter";
import { Building, Plus, Calendar, User, FileText, Eye, Download, Trash2, Upload, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest, getQueryFn } from "@/lib/queryClient";
import { useOffline } from "@/hooks/useOffline";
import { getOfflineVerbali, downloadOfflinePDF, removeOfflineVerbale } from "@/lib/offline-storage";
import DirezioneLavoriModal from "@/components/direzione-lavori-modal";
import DeleteDirezioneLavoriModal from "@/components/delete-direzione-lavori-modal";
import PDFDownloadModal from "@/components/pdf-download-modal";
import PageHeader from "@/components/page-header";
import NuovoVerbaleDirezioneModal from "@/components/nuovo-verbale-direzione-modal";
import PersonePresentiBirezioneLavoriModal from "@/components/persone-presenti-direzione-modal";
import RubricaPersoneModal from "@/components/rubrica-persone-modal";
import type { DirezioneLavori, VerbaleDirezioneLavori, DirettoreLavori, Tecnico, InsertVerbaleDirezioneLavori } from "@shared/schema";

type VerbaleWithTecnico = VerbaleDirezioneLavori & { tecnico: DirettoreLavori };

export default function DirezioneLavoriDettaglio() {
  const [match, params] = useRoute("/direzione-lavori/:id");
  const lavoroId = params?.id ? parseInt(params.id) : null;
  const [isNuovoVerbaleOpen, setIsNuovoVerbaleOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isPersonePresenteOpen, setIsPersonePresenteOpen] = useState(false);
  const [isRubricaOpen, setIsRubricaOpen] = useState(false);
  const [currentVerbale, setCurrentVerbale] = useState<VerbaleDirezioneLavori | null>(null);
  const [verbaleDataTemp, setVerbaleDataTemp] = useState<any>(null);
  const [sortField, setSortField] = useState<string>('data');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [pdfDownloadModal, setPdfDownloadModal] = useState<{isOpen: boolean; verbaleId: number; filename: string}>({
    isOpen: false,
    verbaleId: 0,
    filename: ''
  });
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const { isOnline } = useOffline();
  


  const { data: lavoro, isLoading: isLoadingLavoro } = useQuery<DirezioneLavori>({
    queryKey: [`/api/direzione-lavori/${lavoroId}`],
    queryFn: getQueryFn({ on401: "throw" }),
    enabled: !!lavoroId,
  });

  const { data: verbali = [], isLoading: isLoadingVerbali } = useQuery<VerbaleWithTecnico[]>({
    queryKey: [`/api/direzione-lavori/${lavoroId}/verbali`],
    queryFn: getQueryFn({ on401: "throw" }),
    enabled: !!lavoroId,
  });

  const { data: direttori = [], isLoading: isLoadingDirettori } = useQuery<DirettoreLavori[]>({
    queryKey: [`/api/direzione-lavori/${lavoroId}/direttori`],
    queryFn: getQueryFn({ on401: "throw" }),
    enabled: !!lavoroId,
  });

  const { data: tecnici = [] } = useQuery<Tecnico[]>({
    queryKey: ["/api/tecnici"],
    queryFn: getQueryFn({ on401: "throw" }),
  });

  // Handle sorting function
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
  };

  // Get sort icon for column headers
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 text-gray-400" />;
    }
    return sortOrder === 'asc' ? 
      <ArrowUp className="h-4 w-4 text-blue-600" /> : 
      <ArrowDown className="h-4 w-4 text-blue-600" />;
  };

  // Fetch offline verbali
  const { data: offlineVerbaliData = [] } = useQuery({
    queryKey: ["offline-verbali", lavoroId],
    queryFn: async () => {
      try {
        const data = await getOfflineVerbali();
        return Array.isArray(data) ? data : [];
      } catch (error) {
        console.error('Error fetching offline verbali:', error);
        return [];
      }
    },
    enabled: !!lavoroId,
  });

  // Combine online and offline verbali using useMemo to avoid initialization errors
  const tuttiVerbali = useMemo(() => {
    // Get offline verbali for this commessa
    const offlineVerbali = offlineVerbaliData.filter(v => v.lavoroId === lavoroId);
    
    // Combine online and offline verbali
    const combined = [
      ...(verbali || []), // Use default empty array if verbali not yet loaded
      ...offlineVerbali.map(offline => ({
        id: offline.id,
        numeroProgressivo: offline.data.numeroProgressivo || 'N/A',
        data: offline.data.data || new Date(offline.timestamp).toISOString(),
        stato: offline.type,
        pdfPath: offline.id, // Use offline ID as identifier
        direzioneLavoriId: lavoroId,
        tecnicoId: offline.data.tecnicoId || 0,
        lavorazioniInCorso: offline.data.lavorazioniInCorso || '',
        criticita: offline.data.criticita || '',
        note: offline.data.note || '',
        osservazioni: offline.data.osservazioni || '',
        personePresenti: offline.data.personePresenti || '[]',
        firme: offline.data.firme || '{}',
        tecnico: {
          id: offline.data.tecnicoId || 0,
          nome: offline.data.tecnicoNome?.split(' ')[0] || 'Offline',
          cognome: offline.data.tecnicoNome?.split(' ').slice(1).join(' ') || 'User',
          qualifica: offline.data.tecnicoQualifica || 'Direttore Lavori',
          telefono: null,
          email: null,
          specializzazione: null
        },
        isOffline: true
      }))
    ];

    // Apply sorting
    const sorted = combined.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortField) {
        case 'numeroProgressivo':
          // Extract progressive number from format like BC074-VDL-020-20250617
          const aMatch = a.numeroProgressivo.match(/-VDL-(\d+)-/);
          const bMatch = b.numeroProgressivo.match(/-VDL-(\d+)-/);
          aValue = aMatch ? parseInt(aMatch[1]) : parseInt(a.id?.toString() || '0');
          bValue = bMatch ? parseInt(bMatch[1]) : parseInt(b.id?.toString() || '0');
          break;
        case 'data':
          aValue = new Date(a.data).getTime();
          bValue = new Date(b.data).getTime();
          break;
        case 'tecnico':
          aValue = `${a.tecnico.nome} ${a.tecnico.cognome}`.toLowerCase();
          bValue = `${b.tecnico.nome} ${b.tecnico.cognome}`.toLowerCase();
          break;
        case 'stato':
          aValue = a.stato;
          bValue = b.stato;
          break;
        default:
          aValue = a.data;
          bValue = b.data;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    console.log('Verbali combinati e ordinati:', { 
      online: verbali?.length || 0, 
      offline: offlineVerbali.length, 
      total: sorted.length,
      sortField,
      sortOrder
    });
    
    return sorted;
  }, [verbali, offlineVerbaliData, lavoroId, sortField, sortOrder]); // Dependencies: re-calculate when verbali, sorting options change

  const createVerbaleFromTempData = useMutation({
    mutationFn: async (verbaleData: InsertVerbaleDirezioneLavori) => {
      const response = await apiRequest("POST", `/api/direzione-lavori/${lavoroId}/verbali`, verbaleData);
      return response.json();
    },
    onSuccess: (newVerbale) => {
      queryClient.invalidateQueries({ queryKey: [`/api/direzione-lavori/${lavoroId}/verbali`] });
      setIsPersonePresenteOpen(false);
      setVerbaleDataTemp(null);
      setCurrentVerbale(null);
      toast({
        title: "Successo",
        description: "Verbale creato con successo",
      });
      setLocation(`/direzione-lavori/${lavoroId}/verbali/${newVerbale.id}`);
    },
  });

  // Delete verbale mutation
  const deleteVerbaleMutation = useMutation({
    mutationFn: async (verbaleId: number) => {
      const response = await apiRequest("DELETE", `/api/direzione-lavori/${lavoroId}/verbali/${verbaleId}`);
      if (!response.ok) throw new Error("Failed to delete verbale");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/direzione-lavori/${lavoroId}/verbali`] });
      toast({
        title: "Successo",
        description: "Verbale cancellato con successo",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante la cancellazione del verbale",
        variant: "destructive",
      });
    },
  });

  // Upload PDF mutation
  const uploadPDFMutation = useMutation({
    mutationFn: async ({ verbaleId, file }: { verbaleId: number; file: File }) => {
      const formData = new FormData();
      formData.append('pdf', file);
      
      const response = await apiUpload(`/api/direzione-lavori/${lavoroId}/verbali/${verbaleId}/upload-pdf`, formData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/direzione-lavori/${lavoroId}/verbali`] });
      toast({
        title: "Successo",
        description: "PDF caricato con successo. Il verbale è stato finalizzato.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante il caricamento del PDF",
        variant: "destructive",
      });
    },
  });

  // Handler functions
  const handleDeleteVerbale = (verbaleId: number) => {
    if (confirm("Sei sicuro di voler cancellare questo verbale? Questa azione non può essere annullata.")) {
      deleteVerbaleMutation.mutate(verbaleId);
    }
  };

  const handleUploadPDF = (verbaleId: number, file: File | undefined) => {
    if (!file) return;
    
    if (file.type !== 'application/pdf') {
      toast({
        title: "Errore",
        description: "Il file deve essere in formato PDF",
        variant: "destructive",
      });
      return;
    }
    
    uploadPDFMutation.mutate({ verbaleId, file });
  };

  if (!match || !lavoroId) {
    return <div>Lavoro non trovato</div>;
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("it-IT");
  };

  if (isLoadingLavoro) {
    return (
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gray-50">
      {/* Header Unificato */}
      <PageHeader
        title={lavoro?.codiceCommessa || "Caricamento..."}
        subtitle={lavoro ? [lavoro.denominazione, lavoro.indirizzo].filter(Boolean).join(" - ") : undefined}
        actions={
          <div className="flex items-center space-x-2">
            {lavoro?.stato && (
              <Badge variant="secondary">{lavoro.stato}</Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditModalOpen(true)}
            >
              Modifica
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsDeleteModalOpen(true)}
              className="text-red-600 hover:text-red-700"
            >
              Elimina
            </Button>
          </div>
        }
      />
      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="mr-2 h-5 w-5" />
                  Dettagli Direzione Lavori
                </CardTitle>
              </CardHeader>
              <CardContent>
                {lavoro ? (
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Committente</label>
                      <p className="text-lg">{lavoro.committente}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-500">Indirizzo</label>
                      <p className="text-lg">{lavoro.indirizzo}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-500">Oggetto</label>
                      <p className="text-gray-700">{lavoro.oggetto}</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Data Inizio</label>
                        <p className="text-lg flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          {formatDate(lavoro.dataInizio)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Data Fine Prevista</label>
                        <p className="text-lg flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          {formatDate(lavoro.dataFinePrevista)}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p>Errore nel caricamento dei dati del lavoro.</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar Azioni */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Azioni</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={() => setIsNuovoVerbaleOpen(true)}
                  className="w-full bg-primary hover:bg-primary/90"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Nuovo Verbale
                </Button>
                <Button 
                  onClick={() => setIsRubricaOpen(true)}
                  variant="outline" 
                  className="w-full"
                >
                  <User className="mr-2 h-4 w-4" />
                  Rubrica Persone
                </Button>
                <Button variant="outline" className="w-full">
                  <FileText className="mr-2 h-4 w-4" />
                  Esporta Report
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Verbali */}
        <div className="mt-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Verbali di Direzione Lavori</h2>
            <Button 
              onClick={() => setIsNuovoVerbaleOpen(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="mr-2 h-4 w-4" />
              Nuovo Verbale
            </Button>
          </div>
          
          {!isOnline && (
            <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <p className="text-sm text-orange-800">
                ⚠️ <strong>Modalità offline:</strong> I verbali effettuati offline saranno visualizzati nell'elenco una volta ristabilita la connessione.
              </p>
            </div>
          )}

          {isLoadingVerbali ? (
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-gray-200 rounded w-full"></div>
              <div className="h-8 bg-gray-200 rounded w-full"></div>
              <div className="h-8 bg-gray-200 rounded w-full"></div>
            </div>
          ) : tuttiVerbali.length > 0 ? (
            <div className="bg-white border rounded-lg overflow-hidden">
              <Table>
                <TableHeader className="bg-gray-50">
                  <TableRow>
                    <TableHead className="font-medium text-gray-600">ID SOPRALLUOGO</TableHead>
                    <TableHead 
                      className="font-medium text-gray-600 cursor-pointer hover:bg-gray-100 select-none"
                      onClick={() => handleSort('numeroProgressivo')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>NUMERO PROGRESSIVO</span>
                        {getSortIcon('numeroProgressivo')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-medium text-gray-600 cursor-pointer hover:bg-gray-100 select-none"
                      onClick={() => handleSort('data')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>DATA</span>
                        {getSortIcon('data')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-medium text-gray-600 cursor-pointer hover:bg-gray-100 select-none"
                      onClick={() => handleSort('tecnico')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>TECNICO</span>
                        {getSortIcon('tecnico')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-medium text-gray-600 cursor-pointer hover:bg-gray-100 select-none"
                      onClick={() => handleSort('stato')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>STATO</span>
                        {getSortIcon('stato')}
                      </div>
                    </TableHead>
                    <TableHead className="font-medium text-gray-600">VERBALE</TableHead>
                    <TableHead className="font-medium text-gray-600">AZIONI</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tuttiVerbali.map((verbale) => (
                    <TableRow key={verbale.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">
                        {verbale.numeroProgressivo}
                      </TableCell>
                      <TableCell className="font-medium">
                        {(() => {
                          // Estrae il numero progressivo dal formato BC074-VDL-020-20250617
                          const progressivoMatch = verbale.numeroProgressivo.match(/-VDL-(\d+)-/);
                          if (progressivoMatch) {
                            // Formatta il numero con 3 cifre e zeri iniziali
                            return progressivoMatch[1].padStart(3, '0');
                          }
                          // Fallback: formatta l'ID con 3 cifre
                          return verbale.id.toString().padStart(3, '0');
                        })()}
                      </TableCell>
                      <TableCell>
                        {formatDate(verbale.data)}
                      </TableCell>
                      <TableCell>
                        {verbale.tecnico.nome} {verbale.tecnico.cognome}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant={verbale.stato === 'finalizzato' ? "default" : "secondary"}
                            className={verbale.stato === 'finalizzato' ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                          >
                            {verbale.stato === 'finalizzato' ? "Finalizzato" : "Bozza"}
                          </Badge>
                          {verbale.isOffline && (
                            <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                              Offline
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {verbale.pdfPath ? (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-blue-600 hover:text-blue-800"
                            onClick={() => {
                              if (verbale.isOffline) {
                                // Download offline PDF directly
                                const numeroProgressivo = verbale.numeroProgressivo || `id_${verbale.id}`;
                                const filename = `Verbale-DL-${numeroProgressivo}.pdf`;
                                downloadOfflinePDF(verbale.id, filename);
                              } else {
                                // Open PDF download modal for online verbali
                                // Genera il filename nel formato atteso dal server
                                const numeroProgressivo = verbale.numeroProgressivo || `id_${verbale.id}`;
                                const filename = `verbale_direzione_${numeroProgressivo.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
                                setPdfDownloadModal({
                                  isOpen: true,
                                  verbaleId: verbale.id,
                                  filename: filename
                                });
                              }
                            }}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Scarica PDF
                          </Button>
                        ) : (
                          <Button 
                            variant="outline" 
                            size="default" 
                            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 border-blue-300 px-4 py-2 min-w-[120px]"
                            onClick={() => setLocation(`/direzione-lavori/${lavoroId}/verbali/${verbale.id}`)}
                          >
                            <FileText className="h-5 w-5 mr-2" />
                            Visualizza
                          </Button>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {/* Pulsante Visualizza per tutti i verbali */}
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-blue-600 hover:text-blue-700 border-blue-300 hover:border-blue-400"
                            onClick={() => setLocation(`/direzione-lavori/${lavoroId}/verbali/${verbale.id}`)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          {/* Azioni specifiche per stato */}
                          {verbale.stato === 'bozza' && (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                                onClick={() => {
                                  if (verbale.isOffline) {
                                    // Delete offline verbale
                                    if (confirm('Sei sicuro di voler eliminare questo verbale offline?')) {
                                      removeOfflineVerbale(verbale.id);
                                      toast({
                                        title: "Successo",
                                        description: "Verbale offline eliminato",
                                      });
                                      // Force re-render by updating a state
                                      window.location.reload();
                                    }
                                  } else {
                                    // Delete online verbale
                                    handleDeleteVerbale(verbale.id);
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              {!verbale.isOffline && (
                                <>
                                  <input
                                    type="file"
                                    accept=".pdf"
                                    style={{ display: 'none' }}
                                    id={`pdf-upload-${verbale.id}`}
                                    onChange={(e) => handleUploadPDF(verbale.id, e.target.files?.[0])}
                                  />
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-blue-600 hover:text-blue-700"
                                    onClick={() => document.getElementById(`pdf-upload-${verbale.id}`)?.click()}
                                  >
                                    <Upload className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </>
                          )}
                          
                          {/* Azioni per verbali confermati/finalizzati */}
                          {(verbale.stato === 'confermato' || verbale.stato === 'finalizzato') && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                              onClick={() => {
                                if (confirm('Sei sicuro di voler eliminare questo verbale? Questa azione non può essere annullata.')) {
                                  handleDeleteVerbale(verbale.id);
                                }
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500 mb-4">Nessun verbale presente</p>
                <Button 
                  onClick={() => setIsNuovoVerbaleOpen(true)}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Crea il primo verbale
                </Button>
              </CardContent>
            </Card>
          )}
        </div>


      </div>
      {/* Modals */}
      <DirezioneLavoriModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        lavoro={lavoro || null}
        title="Modifica Direzione Lavori"
      />
      <DeleteDirezioneLavoriModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        lavoro={lavoro || null}
      />
      <NuovoVerbaleDirezioneModal
        isOpen={isNuovoVerbaleOpen}
        onClose={() => setIsNuovoVerbaleOpen(false)}
        lavoro={lavoro || null}
        onDataCollected={(verbaleData) => {
          setVerbaleDataTemp(verbaleData);
          setIsNuovoVerbaleOpen(false);
          setIsPersonePresenteOpen(true);
        }}
      />
      <PersonePresentiBirezioneLavoriModal
        isOpen={isPersonePresenteOpen}
        onClose={() => {
          setIsPersonePresenteOpen(false);
          setCurrentVerbale(null);
          setVerbaleDataTemp(null);
        }}
        lavoro={lavoro || null}
        verbale={currentVerbale}
        verbaleDataTemp={verbaleDataTemp}
        tecnicoCompilatore={verbaleDataTemp?.tecnicoId ? direttori.find(d => d.id === verbaleDataTemp.tecnicoId) : null}
        onVerbaleCreated={(verbaleData) => {
          setVerbaleDataTemp(verbaleData);
          setIsPersonePresenteOpen(false);
          setCurrentVerbale(verbaleData);
          
          // Robust navigation for offline/tablet compatibility
          const targetPath = `/direzione-lavori/${lavoroId}/verbali/new?tecnicoId=${verbaleData.tecnicoId}`;
          const isTablet = /iPad|Android/i.test(navigator.userAgent) && window.innerWidth >= 768;
          
          console.log('Navigating to verbale compilation:', { targetPath, isTablet });
          
          if (isTablet || !navigator.onLine) {
            // For tablets or offline, use more aggressive navigation
            setTimeout(() => {
              console.log('Using window.location.href for navigation');
              window.location.href = window.location.origin + targetPath;
            }, 100);
          } else {
            // Standard navigation for desktop online
            setLocation(targetPath);
          }
        }}
      />
      
      {/* Rubrica Persone Modal */}
      {lavoro && (
        <RubricaPersoneModal
          isOpen={isRubricaOpen}
          onClose={() => setIsRubricaOpen(false)}
          lavoro={lavoro}
        />
      )}

      {/* PDF Download Modal */}
      <PDFDownloadModal
        isOpen={pdfDownloadModal.isOpen}
        onClose={() => setPdfDownloadModal({ isOpen: false, verbaleId: 0, filename: '' })}
        verbaleId={pdfDownloadModal.verbaleId}
        filename={pdfDownloadModal.filename}
      />
    </div>
  );
}