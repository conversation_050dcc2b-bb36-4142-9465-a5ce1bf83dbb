import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Settings, Plus, Edit, Trash2, User, Image } from "lucide-react";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import PageHeader from "@/components/page-header";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import TecnicoModal from "@/components/tecnico-modal";
import DeleteTecnicoModal from "@/components/delete-tecnico-modal";
import type { Tecnico } from "@shared/schema";

export default function Impostazioni() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedTecnico, setSelectedTecnico] = useState<Tecnico | null>(null);

  const { data: tecnici = [], isLoading, refetch } = useQuery<Tecnico[]>({
    queryKey: ["/api/tecnici"],
  });

  // Filter tecnici based on search term
  const filteredTecnici = tecnici.filter(tecnico => {
    if (!searchTerm.trim()) return true;
    const term = searchTerm.toLowerCase();
    return (
      tecnico.nome.toLowerCase().includes(term) ||
      tecnico.cognome.toLowerCase().includes(term) ||
      tecnico.specializzazione.toLowerCase().includes(term) ||
      (tecnico.email && tecnico.email.toLowerCase().includes(term))
    );
  });

  const handleEdit = (tecnico: Tecnico) => {
    setSelectedTecnico(tecnico);
    setIsEditModalOpen(true);
  };

  const handleDelete = (tecnico: Tecnico) => {
    setSelectedTecnico(tecnico);
    setIsDeleteModalOpen(true);
  };

  const handleModalClose = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
    setSelectedTecnico(null);
    refetch();
  };

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gray-50">
      {/* Header Unificato */}
      <PageHeader
        title="Impostazioni Sistema"
        subtitle="Gestisci tecnici e configurazioni del sistema"
        actions={
          <div className="flex space-x-2">
            <Button onClick={() => setIsAddModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Nuovo Tecnico
            </Button>
            <Link href="/impostazioni/loghi">
              <Button variant="outline">
                <Image className="mr-2 h-4 w-4" />
                Gestione Loghi
              </Button>
            </Link>
          </div>
        }
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Quick Actions Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Image className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4 flex-1">
                <h3 className="text-lg font-medium text-gray-900">Gestione Loghi</h3>
                <p className="text-sm text-gray-500">Carica e gestisci i loghi aziendali per i PDF</p>
              </div>
              <Link href="/impostazioni/loghi">
                <Button variant="outline" size="sm">
                  Gestisci
                </Button>
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <User className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4 flex-1">
                <h3 className="text-lg font-medium text-gray-900">Tecnici CSE</h3>
                <p className="text-sm text-gray-500">Gestisci i Coordinatori per la Sicurezza</p>
              </div>
              <Button variant="outline" size="sm" onClick={() => setIsAddModalOpen(true)}>
                Aggiungi
              </Button>
            </div>
          </div>
        </div>

        {/* Tecnici Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Gestione Tecnici</h2>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="max-w-md">
              <label className="block text-sm font-medium text-gray-700 mb-2">Cerca tecnici</label>
              <Input 
                type="text" 
                placeholder="Nome, cognome, specializzazione..." 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Tecnici Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {isLoading ? (
            <div className="p-6">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full mb-4" />
              ))}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                      Nome
                    </TableHead>
                    <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                      Cognome
                    </TableHead>
                    <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                      Specializzazione
                    </TableHead>
                    <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                      Telefono
                    </TableHead>
                    <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </TableHead>
                    <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                      Stato
                    </TableHead>
                    <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                      Azioni
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTecnici.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-12">
                        <div className="text-gray-500">
                          <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <p className="text-lg font-medium">Nessun tecnico trovato</p>
                          <p className="text-sm mt-2">Aggiungi un nuovo tecnico per iniziare.</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTecnici.map((tecnico) => (
                      <TableRow key={tecnico.id} className="hover:bg-gray-50">
                        <TableCell className="font-medium">{tecnico.nome}</TableCell>
                        <TableCell>{tecnico.cognome}</TableCell>
                        <TableCell>{tecnico.specializzazione}</TableCell>
                        <TableCell>{tecnico.telefono || "-"}</TableCell>
                        <TableCell>{tecnico.email || "-"}</TableCell>
                        <TableCell>
                          <Badge 
                            className={tecnico.attivo === "true" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"} 
                            variant="secondary"
                          >
                            {tecnico.attivo === "true" ? "Attivo" : "Inattivo"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-yellow-600 hover:text-yellow-800"
                              onClick={() => handleEdit(tecnico)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-red-600 hover:text-red-800"
                              onClick={() => handleDelete(tecnico)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <TecnicoModal
        isOpen={isAddModalOpen}
        onClose={handleModalClose}
        tecnico={null}
        title="Aggiungi Nuovo Tecnico"
      />

      <TecnicoModal
        isOpen={isEditModalOpen}
        onClose={handleModalClose}
        tecnico={selectedTecnico}
        title="Modifica Tecnico"
      />

      <DeleteTecnicoModal
        isOpen={isDeleteModalOpen}
        onClose={handleModalClose}
        tecnico={selectedTecnico}
      />
    </div>
  );
}