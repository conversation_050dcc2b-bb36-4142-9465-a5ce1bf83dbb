import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Building2, HardHat, ShieldCheck, FileText } from "lucide-react";
import { <PERSON> } from "wouter";

export default function Home() {
  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-[calc(100vh-4rem)]">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="bg-blue-600 p-4 rounded-full">
              <Building2 className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Orbyta Engineering SRL
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Sistema di gestione cantieri per il coordinamento della sicurezza e la direzione lavori
          </p>
        </div>

        {/* Selection Cards */}
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Coordinamento Sicurezza */}
          <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer border-2 hover:border-orange-500">
            <Link href="/cantieri">
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  <div className="bg-orange-500 p-4 rounded-full group-hover:scale-110 transition-transform duration-300">
                    <ShieldCheck className="h-10 w-10 text-white" />
                  </div>
                </div>
                <CardTitle className="text-2xl text-gray-900 dark:text-white">
                  Coordinamento Sicurezza
                </CardTitle>
                <CardDescription className="text-lg">
                  Gestione cantieri e sopralluoghi CSE
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-300">
                    <HardHat className="h-5 w-5" />
                    <span>Gestione cantieri e progetti</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-300">
                    <FileText className="h-5 w-5" />
                    <span>Sopralluoghi di sicurezza</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-300">
                    <ShieldCheck className="h-5 w-5" />
                    <span>Verbali di coordinamento</span>
                  </div>
                </div>
                <Button 
                  size="lg" 
                  className="w-full bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Accedi ai Cantieri
                </Button>
              </CardContent>
            </Link>
          </Card>

          {/* Direzione Lavori */}
          <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer border-2 hover:border-blue-500">
            <Link href="/direzione-lavori">
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  <div className="bg-blue-500 p-4 rounded-full group-hover:scale-110 transition-transform duration-300">
                    <Building2 className="h-10 w-10 text-white" />
                  </div>
                </div>
                <CardTitle className="text-2xl text-gray-900 dark:text-white">
                  Direzione Lavori
                </CardTitle>
                <CardDescription className="text-lg">
                  Gestione commesse e verbali DL
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-300">
                    <Building2 className="h-5 w-5" />
                    <span>Gestione commesse</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-300">
                    <FileText className="h-5 w-5" />
                    <span>Verbali di direzione lavori</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-300">
                    <HardHat className="h-5 w-5" />
                    <span>Controlli tecnici</span>
                  </div>
                </div>
                <Button 
                  size="lg" 
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Accedi alle Commesse
                </Button>
              </CardContent>
            </Link>
          </Card>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-16">
          <p className="text-gray-500 dark:text-gray-400">
            Sistema di digitalizzazione per l'ottimizzazione dei sopralluoghi e della documentazione di cantiere
          </p>
        </div>
      </div>
    </div>
  );
}