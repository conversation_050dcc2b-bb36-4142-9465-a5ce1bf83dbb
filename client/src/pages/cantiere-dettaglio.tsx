import { useState, useMemo } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { MapPin, User, Calendar, Building, Plus, FileText, Download, Edit, Trash2, ArrowUpDown, ArrowU<PERSON>, ArrowDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";
import { useOffline } from "@/hooks/useOffline";
import NuovoSopralluogoModal from "@/components/nuovo-sopralluogo-modal";
import CantiereModal from "@/components/cantiere-modal";
import DeleteConfirmationModal from "@/components/delete-confirmation-modal";
import PageHeader from "@/components/page-header";
import type { Cantiere, Sopralluogo, Tecnico } from "@shared/schema";

type SopralluogoWithTecnico = Sopralluogo & { tecnico: Tecnico };

export default function CantiereDettaglio() {
  const [match, params] = useRoute("/cantieri/:id");
  const cantiereId = params?.id ? parseInt(params.id) : null;
  const [isNuovoSopralluogoOpen, setIsNuovoSopralluogoOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [sopralluogoInModifica, setSopralluogoInModifica] = useState<SopralluogoWithTecnico | null>(null);
  const [sortField, setSortField] = useState<string>('data');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const { toast } = useToast();
  const { isOnline } = useOffline();

  const { data: cantiere, isLoading: isLoadingCantiere } = useQuery<Cantiere>({
    queryKey: [`/api/cantieri/${cantiereId}`],
    enabled: !!cantiereId,
  });

  const { data: sopralluoghi = [], isLoading: isLoadingSopralluoghi } = useQuery<SopralluogoWithTecnico[]>({
    queryKey: [`/api/cantieri/${cantiereId}/sopralluoghi`],
    enabled: !!cantiereId,
  });

  // Handle sorting function
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
  };

  // Get sort icon for column headers
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 text-gray-400" />;
    }
    return sortOrder === 'asc' ? 
      <ArrowUp className="h-4 w-4 text-blue-600" /> : 
      <ArrowDown className="h-4 w-4 text-blue-600" />;
  };

  // Sort sopralluoghi based on current sort settings
  const sopralluoghiOrdinati = useMemo(() => {
    if (!sopralluoghi.length) return [];
    
    const sorted = [...sopralluoghi].sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortField) {
        case 'numeroProgressivo':
          // Extract progressive number from format like BC074-CSE-020-20250617
          const aMatch = a.numeroSopralluogo.match(/-CSE-(\d+)-/);
          const bMatch = b.numeroSopralluogo.match(/-CSE-(\d+)-/);
          aValue = aMatch ? parseInt(aMatch[1]) : a.id;
          bValue = bMatch ? parseInt(bMatch[1]) : b.id;
          break;
        case 'data':
          aValue = new Date(a.data).getTime();
          bValue = new Date(b.data).getTime();
          break;
        case 'tecnico':
          aValue = `${a.tecnico.nome} ${a.tecnico.cognome}`.toLowerCase();
          bValue = `${b.tecnico.nome} ${b.tecnico.cognome}`.toLowerCase();
          break;
        case 'stato':
          aValue = a.statoSopralluogo;
          bValue = b.statoSopralluogo;
          break;
        default:
          aValue = a.data;
          bValue = b.data;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    return sorted;
  }, [sopralluoghi, sortField, sortOrder]);

  const { data: tecnici = [] } = useQuery<Tecnico[]>({
    queryKey: ["/api/tecnici"],
  });

  if (!match || !cantiereId) {
    return <div>Cantiere non trovato</div>;
  }

  const getStatusColor = (stato: string) => {
    switch (stato) {
      case "In corso":
        return "bg-green-100 text-green-800";
      case "In pianificazione":
        return "bg-blue-100 text-blue-800";
      case "Completato":
        return "bg-gray-100 text-gray-800";
      case "Sospeso":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("it-IT");
  };

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gray-50">
      {/* Header Unificato */}
      <PageHeader
        title={cantiere?.codiceCommessa || "Caricamento..."}
        subtitle={cantiere ? [cantiere.denominazione, cantiere.indirizzo].filter(Boolean).join(" - ") : undefined}
        actions={
          <div className="flex space-x-2">
            <Button onClick={() => setIsNuovoSopralluogoOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Nuovo Sopralluogo
            </Button>
            <Button variant="outline" onClick={() => setIsEditModalOpen(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Modifica
            </Button>
          </div>
        }
      />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Cantiere Details Card */}
        {isLoadingCantiere ? (
          <Card className="mb-8">
            <CardHeader>
              <Skeleton className="h-8 w-64" />
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-6 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        ) : cantiere ? (
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl font-bold">
                  Dettagli Cantiere - {cantiere.codiceCommessa}
                </CardTitle>
                <div className="flex items-center space-x-3">
                  <Badge className={getStatusColor(cantiere.stato)} variant="secondary">
                    {cantiere.stato}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditModalOpen(true)}
                    className="flex items-center"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsDeleteModalOpen(true)}
                    className="flex items-center text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-500">
                    <MapPin className="h-4 w-4 mr-2" />
                    Indirizzo
                  </div>
                  <p className="font-medium">{cantiere.indirizzo}</p>
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-500">
                    <User className="h-4 w-4 mr-2" />
                    Committente
                  </div>
                  <p className="font-medium">{cantiere.committente}</p>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-500">
                    <User className="h-4 w-4 mr-2" />
                    CSE Nominato
                  </div>
                  <p className="font-medium">{cantiere.cseNominato}</p>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    Data Inizio
                  </div>
                  <p className="font-medium">{formatDate(cantiere.dataInizio)}</p>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    Data Fine Prevista
                  </div>
                  <p className="font-medium">{formatDate(cantiere.dataFinePrevista)}</p>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-500">
                    <FileText className="h-4 w-4 mr-2" />
                    Stato
                  </div>
                  <Badge className={getStatusColor(cantiere.stato)} variant="secondary">
                    {cantiere.stato}
                  </Badge>
                </div>
              </div>

              <div className="mt-6 space-y-1">
                <div className="flex items-center text-sm text-gray-500">
                  <Building className="h-4 w-4 mr-2" />
                  Oggetto
                </div>
                <p className="font-medium text-gray-900">{cantiere.oggetto}</p>
              </div>
            </CardContent>
          </Card>
        ) : null}

        {/* Sopralluoghi Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-bold">Sopralluoghi Effettuati</CardTitle>
              <Button 
                className="bg-primary hover:bg-primary/90"
                onClick={() => setIsNuovoSopralluogoOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Nuovo Sopralluogo
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {!isOnline && (
              <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <p className="text-sm text-orange-800">
                  ⚠️ <strong>Modalità offline:</strong> I sopralluoghi effettuati offline saranno visualizzati nell'elenco una volta ristabilita la connessione.
                </p>
              </div>
            )}
            {isLoadingSopralluoghi ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-12 w-full" />
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-medium text-gray-500 uppercase tracking-wider">ID Sopralluogo</TableHead>
                      <TableHead 
                        className="font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                        onClick={() => handleSort('numeroProgressivo')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Numero Progressivo</span>
                          {getSortIcon('numeroProgressivo')}
                        </div>
                      </TableHead>
                      <TableHead 
                        className="font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                        onClick={() => handleSort('data')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Data</span>
                          {getSortIcon('data')}
                        </div>
                      </TableHead>
                      <TableHead 
                        className="font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                        onClick={() => handleSort('tecnico')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Tecnico</span>
                          {getSortIcon('tecnico')}
                        </div>
                      </TableHead>
                      <TableHead 
                        className="font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                        onClick={() => handleSort('stato')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Stato</span>
                          {getSortIcon('stato')}
                        </div>
                      </TableHead>
                      <TableHead className="font-medium text-gray-500 uppercase tracking-wider">
                        Verbale
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sopralluoghi.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-12">
                          <div className="text-gray-500">
                            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                            <p className="text-lg font-medium">Nessun sopralluogo effettuato</p>
                            <p className="text-sm mt-2">Aggiungi il primo sopralluogo per iniziare.</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      sopralluoghiOrdinati.map((sopralluogo) => (
                        <TableRow key={sopralluogo.id} className="hover:bg-gray-50">
                          <TableCell className="font-medium">
                            {sopralluogo.id}
                          </TableCell>
                          <TableCell className="font-medium">
                            {sopralluogo.numeroSopralluogo}
                          </TableCell>
                          <TableCell>{formatDate(sopralluogo.data)}</TableCell>
                          <TableCell>
                            {sopralluogo.tecnico.nome} {sopralluogo.tecnico.cognome}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Badge 
                                className={sopralluogo.statoSopralluogo === "Completato" ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"} 
                                variant="secondary"
                              >
                                {sopralluogo.statoSopralluogo}
                              </Badge>
                              {sopralluogo.stato === "bozza" && (
                                <Badge variant="outline" className="border-orange-500 text-orange-600 bg-orange-50">
                                  Bozza
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {sopralluogo.verbaleUrl ? (
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="text-blue-600 hover:text-blue-800"
                                  onClick={() => {
                                    const token = localStorage.getItem('auth_token');
                                    console.log('📥 Download PDF:', { 
                                      originalUrl: sopralluogo.verbaleUrl,
                                      token: token?.substring(0, 20) + '...',
                                      hasToken: !!token 
                                    });
                                    const urlWithToken = `${sopralluogo.verbaleUrl}?token=${token}`;
                                    console.log('📥 Final URL:', urlWithToken);
                                    window.open(urlWithToken, '_blank');
                                  }}
                                >
                                  <Download className="h-4 w-4 mr-2" />
                                  Scarica PDF
                                </Button>
                              ) : sopralluogo.stato === "bozza" ? (
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="text-orange-600 hover:text-orange-800"
                                  onClick={() => {
                                    setSopralluogoInModifica(sopralluogo);
                                    setIsNuovoSopralluogoOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Modifica
                                </Button>
                              ) : (
                                <span className="text-gray-400">Non disponibile</span>
                              )}
                            </div>
                          </TableCell>

                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Nuovo Sopralluogo Modal */}
        {cantiere && (
          <NuovoSopralluogoModal
            isOpen={isNuovoSopralluogoOpen}
            onClose={() => {
              setIsNuovoSopralluogoOpen(false);
              setSopralluogoInModifica(null);
            }}
            cantiere={cantiere}
            tecnici={tecnici}
            sopralluogoInModifica={sopralluogoInModifica}
          />
        )}

        {/* Edit Cantiere Modal */}
        {cantiere && (
          <CantiereModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            cantiere={cantiere}
            title="Aggiorna Cantiere"
          />
        )}

        {/* Delete Confirmation Modal */}
        {cantiere && (
          <DeleteConfirmationModal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            cantiere={cantiere}
          />
        )}
      </div>
    </div>
  );
}