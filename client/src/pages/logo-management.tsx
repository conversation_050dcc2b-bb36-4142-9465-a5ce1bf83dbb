import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Image, Upload, Trash2, <PERSON><PERSON><PERSON><PERSON>, Settings, Plus, Eye } from "lucide-react";
import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest, apiUpload } from "@/lib/queryClient";

interface Logo {
  nome: string;
  descrizione?: string;
  contentType: string;
  createdAt: string;
}

interface LogoPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  logoNome: string;
}

function LogoPreviewModal({ isOpen, onClose, logoNome }: LogoPreviewModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg max-w-2xl max-h-[80vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Anteprima Logo: {logoNome}</h3>
          <Button variant="ghost" onClick={onClose}>×</Button>
        </div>
        <div className="text-center">
          <img 
            src={`/api/loghi/${logoNome}`} 
            alt={`Logo ${logoNome}`}
            className="max-w-full max-h-96 mx-auto border border-gray-200 rounded"
            onError={(e) => {
              (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkVycm9yZSBjYXJpY2FtZW50bzwvdGV4dD48L3N2Zz4=';
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default function LogoManagement() {
  const [isUploadMode, setIsUploadMode] = useState(false);
  const [uploadData, setUploadData] = useState({
    nome: '',
    descrizione: '',
    file: null as File | null
  });
  const [previewLogo, setPreviewLogo] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch logos list
  const { data: logos = [], isLoading, refetch } = useQuery<Logo[]>({
    queryKey: ["/api/loghi"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/loghi");
      if (!response.ok) throw new Error("Failed to fetch logos");
      return response.json();
    },
  });

  // Upload logo mutation
  const uploadMutation = useMutation({
    mutationFn: async ({ nome, descrizione, file }: { nome: string; descrizione: string; file: File }) => {
      const formData = new FormData();
      formData.append('logo', file);
      if (descrizione) {
        formData.append('descrizione', descrizione);
      }

      const response = await apiUpload(`/api/loghi/${encodeURIComponent(nome)}`, formData);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Successo",
        description: "Logo caricato con successo",
      });
      setIsUploadMode(false);
      setUploadData({ nome: '', descrizione: '', file: null });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante il caricamento del logo",
        variant: "destructive",
      });
    },
  });

  // Delete logo mutation
  const deleteMutation = useMutation({
    mutationFn: async (nomeDesiderato: string) => {
      const response = await apiRequest("DELETE", `/api/loghi/${encodeURIComponent(nomeDesiderato)}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Delete failed');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Successo",
        description: "Logo eliminato con successo",
      });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: "Errore",
        description: error.message || "Errore durante l'eliminazione del logo",
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Errore",
          description: "Il file deve essere un'immagine (JPG, PNG, GIF)",
          variant: "destructive",
        });
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Errore", 
          description: "Il file deve essere inferiore a 5MB",
          variant: "destructive",
        });
        return;
      }

      setUploadData(prev => ({ ...prev, file }));
      
      // Auto-suggest nome if empty
      if (!uploadData.nome) {
        const baseName = file.name.split('.')[0].toLowerCase().replace(/[^a-z0-9]/g, '_');
        setUploadData(prev => ({ ...prev, nome: baseName }));
      }
    }
  };

  const handleUpload = () => {
    if (!uploadData.nome || !uploadData.file) {
      toast({
        title: "Errore",
        description: "Nome e file sono richiesti",
        variant: "destructive",
      });
      return;
    }

    // Validate nome format
    if (!/^[a-z0-9_]+$/.test(uploadData.nome)) {
      toast({
        title: "Errore",
        description: "Il nome può contenere solo lettere minuscole, numeri e underscore",
        variant: "destructive",
      });
      return;
    }

    uploadMutation.mutate(uploadData);
  };

  const handleDelete = (nomeDesiderato: string) => {
    if (confirm(`Sei sicuro di voler eliminare il logo "${nomeDesiderato}"? Questa azione non può essere annullata.`)) {
      deleteMutation.mutate(nomeDesiderato);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link href="/impostazioni">
                <Button variant="ghost" size="sm" className="mr-4">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Impostazioni
                </Button>
              </Link>
              <div className="flex-shrink-0">
                <Image className="h-8 w-8 text-primary" />
                <span className="ml-3 text-xl font-semibold text-gray-900">Gestione Loghi</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Gestione Loghi Aziendali</h1>
              <p className="mt-2 text-gray-600">Gestisci i loghi utilizzati nei documenti PDF</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button 
                onClick={() => setIsUploadMode(!isUploadMode)} 
                className="bg-primary hover:bg-primary/90"
              >
                <Plus className="mr-2 h-4 w-4" />
                {isUploadMode ? 'Annulla' : 'Carica Logo'}
              </Button>
            </div>
          </div>
        </div>

        {/* Upload Form */}
        {isUploadMode && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="mr-2 h-5 w-5" />
                Carica Nuovo Logo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nome Logo <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="text"
                    placeholder="es: logo_sinistra, logo_centrale"
                    value={uploadData.nome}
                    onChange={(e) => setUploadData(prev => ({ 
                      ...prev, 
                      nome: e.target.value.toLowerCase().replace(/[^a-z0-9_]/g, '_')
                    }))}
                    pattern="[a-z0-9_]+"
                    title="Solo lettere minuscole, numeri e underscore"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Solo lettere minuscole, numeri e underscore
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    File Immagine <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    JPG, PNG, GIF - Max 5MB
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrizione (opzionale)
                </label>
                <Textarea
                  placeholder="Descrizione del logo..."
                  value={uploadData.descrizione}
                  onChange={(e) => setUploadData(prev => ({ ...prev, descrizione: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setIsUploadMode(false);
                    setUploadData({ nome: '', descrizione: '', file: null });
                  }}
                >
                  Annulla
                </Button>
                <Button 
                  onClick={handleUpload}
                  disabled={uploadMutation.isPending || !uploadData.nome || !uploadData.file}
                >
                  {uploadMutation.isPending ? 'Caricamento...' : 'Carica Logo'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Logos Table */}
        <Card>
          <CardHeader>
            <CardTitle>Loghi Caricati ({logos.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-gray-500">Caricamento loghi...</p>
              </div>
            ) : logos.length === 0 ? (
              <div className="text-center py-12">
                <Image className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Nessun logo caricato</h3>
                <p className="text-gray-500 mb-4">Carica il primo logo per iniziare.</p>
                <Button onClick={() => setIsUploadMode(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Carica Logo
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Descrizione</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Data Caricamento</TableHead>
                      <TableHead>Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {logos.map((logo) => (
                      <TableRow key={logo.nome}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-gray-100 rounded border flex items-center justify-center overflow-hidden">
                              <img 
                                src={`/api/loghi/${logo.nome}`}
                                alt={logo.nome}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).style.display = 'none';
                                  (e.target as HTMLImageElement).nextElementSibling!.removeAttribute('style');
                                }}
                              />
                              <Image className="h-4 w-4 text-gray-400" style={{display: 'none'}} />
                            </div>
                            <span>{logo.nome}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {logo.descrizione || <span className="text-gray-400">-</span>}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{logo.contentType}</Badge>
                        </TableCell>
                        <TableCell className="text-sm text-gray-500">
                          {formatDate(logo.createdAt)}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setPreviewLogo(logo.nome)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(logo.nome)}
                              className="text-red-600 hover:text-red-800"
                              disabled={deleteMutation.isPending}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-lg">ℹ️ Informazioni Loghi</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p><strong>Nomi consigliati:</strong> logo_sinistra, logo_centrale, logo_destro</p>
            <p><strong>Formati supportati:</strong> JPG, PNG, GIF</p>
            <p><strong>Dimensione massima:</strong> 5MB</p>
            <p><strong>Utilizzo:</strong> I loghi vengono utilizzati automaticamente nella generazione dei PDF dei verbali</p>
            <p><strong>Storage:</strong> I loghi sono salvati nel database PostgreSQL in formato base64</p>
          </CardContent>
        </Card>
      </div>

      {/* Preview Modal */}
      <LogoPreviewModal
        isOpen={!!previewLogo}
        onClose={() => setPreviewLogo(null)}
        logoNome={previewLogo || ''}
      />
    </div>
  );
}