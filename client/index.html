<!DOCTYPE html>
<html lang="it">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>Orbyta Engineering - Gestione Cantieri</title>
    <meta name="description" content="Sistema di gestione cantieri per coordinamento sicurezza e direzione lavori - Orbyta Engineering SRL" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Orbyta Cantieri" />
    
    <!-- PWA Icons -->
    <link rel="icon" type="image/svg+xml" href="/icon-192.svg" />
    <link rel="apple-touch-icon" href="/icon-192.svg" />
    <link rel="icon" sizes="192x192" href="/icon-192.svg" />
    <link rel="icon" sizes="512x512" href="/icon-512.svg" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('Service Worker registered successfully:', registration.scope);
              
              // Background sync sarà registrato solo su richiesta esplicita
              console.log('Service Worker ready - Background sync available on demand');
            })
            .catch((error) => {
              console.log('Service Worker registration failed:', error);
            });
        });
        
        // Listen for PWA install prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
          e.preventDefault();
          deferredPrompt = e;
          
          // Show custom install button/banner
          console.log('PWA install prompt available');
        });
      }
    </script>
    
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>