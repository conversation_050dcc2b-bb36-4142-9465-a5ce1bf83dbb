# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a construction management system for Orbyta Engineering SRL that handles:
1. **Safety Coordination (Coordinamento Sicurezza)** - Managing construction sites (cantieri) and safety inspections (sopralluoghi)
2. **Work Direction (Direzione Lavori)** - Managing work direction activities and official reports

## Common Development Commands

### Development Server
```bash
npm run dev
```
Starts the development server with hot reload on port 5000. This serves both the Express API and React client.

### Build
```bash
npm run build
```
Builds the React frontend with Vite and bundles the Express server with esbuild.

### Production Start
```bash
npm start
```
Starts the production server (requires build first).

### Type Checking
```bash
npm run check
```
Runs TypeScript type checking across the entire codebase.

### Database Operations
```bash
npm run db:push
```
Pushes database schema changes to PostgreSQL using Drizzle Kit.

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Wouter** for routing (lightweight alternative to React Router)
- **TanStack Query** for server state management
- **shadcn/ui** components with Radix UI primitives
- **Tailwind CSS** for styling
- **React Hook Form** with Zod validation
- **Vite** for build tooling

### Backend
- **Express.js** with TypeScript
- **Drizzle ORM** with PostgreSQL
- **Neon serverless** PostgreSQL driver
- **Multer** for file uploads
- **jsPDF** for PDF generation
- **esbuild** for production bundling

### Database
- **PostgreSQL** with Drizzle ORM
- Schema defined in `shared/schema.ts`
- Migrations in `migrations/` directory

## Architecture

### Project Structure
```
/client/          # React frontend
  /src/
    /components/  # Reusable UI components
    /pages/       # Page components
    /lib/         # Utilities and query client
    /hooks/       # Custom React hooks
/server/          # Express backend
/shared/          # Shared types and schema
/migrations/      # Database migrations
/uploads/         # File storage (protected with backup system)
```

### Key Database Tables
- **cantieri**: Construction sites
- **sopralluoghi**: Safety inspections
- **tecnici**: Technical personnel/safety coordinators
- **direzione_lavori**: Work direction projects
- **verbali_direzione_lavori**: Official work direction reports
- **direttori_lavori**: Work directors
- **persone_presenti**: People present during inspections
- **rubrica_persone_presenti**: Address book for people present per project

### API Routes
All API routes are under `/api/` prefix and defined in `server/routes.ts`.

### File Storage
- Photos and PDFs stored in `uploads/` directory
- Automatic backup system preserves files during deployments
- Image compression applied during upload (max 800px, 50% quality)

## Development Guidelines

### Database Schema Changes
1. Update `shared/schema.ts`
2. Run `npm run db:push` to apply changes
3. Check `migrations/` directory for generated SQL

### Adding New Components
- UI components use shadcn/ui patterns
- Follow existing component structure in `/client/src/components/`
- Use TypeScript interfaces from `shared/schema.ts`

### API Development
- Express routes in `server/routes.ts`
- Use Drizzle ORM for database operations
- Follow existing error handling patterns

### PDF Generation
- Server-side PDF generation using jsPDF
- Automatic logo embedding with fallback system
- EXIF orientation correction for photos
- Offline PDF generation capabilities

## Special Features

### PWA and Offline Support
- Service Worker with offline-first strategy
- IndexedDB caching for offline data access
- Automatic sync when connection restored
- Offline action queue for user actions

### Progressive Web App
- Installable app configuration
- Real-time offline/online indicator
- Complete offline workflow support

### Image Processing
- Automatic EXIF orientation correction
- Compression during upload (800px max, 50% quality)
- Sharp library for image processing

### Authentication
Basic user authentication structure exists but is not fully implemented.

## Environment Setup

### Required Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `NODE_ENV`: development/production

### Development Environment
- Node.js 20+
- PostgreSQL database
- All dependencies installed with `npm install`

### Deployment
- Built for Replit deployment
- Automatic file backup and recovery system
- Port 5000 for both development and production

## Italian Language Context
The application is built for Italian construction management, so:
- All user-facing text is in Italian
- Database field names often use Italian terms
- PDF generation includes Italian-specific formatting
- Date formats follow Italian conventions

## Common Issues

### Database Connection
Ensure `DATABASE_URL` is properly set and database is accessible.

### File Upload Issues
Check that `uploads/` directory exists and has proper permissions.

### PDF Generation
If PDF generation fails, check image processing and ensure all required fields are present.

### Offline Functionality
Service Worker may need to be cleared during development for testing offline features.