
const { config } = require('dotenv');
const bcrypt = require('bcryptjs');
const { drizzle } = require('drizzle-orm/neon-http');
const { neon } = require('@neondatabase/serverless');
const { pgTable, text, serial } = require('drizzle-orm/pg-core');

// Define users table directly
const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

// Carica le variabili d'ambiente
config();

// Configura il database con HTTP (senza WebSocket)
const sql = neon("postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require");
const db = drizzle({ client: sql });

async function createInitialUsers() {
  try {
    console.log('🔧 Creazione utenti iniziali...');
    
    // Admin user
    const adminPassword = await bcrypt.hash('Adm1n!Secure2025$', 10);
    const adminUser = await db.insert(users).values({
      username: 'admin',
      password: adminPassword
    }).returning();
    console.log('✅ Utente admin creato:', adminUser[0].username);
    
    // Orbyta user
    const orbytaPassword = await bcrypt.hash('Orbyta#Engineering2025!', 10);
    const orbytaUser = await db.insert(users).values({
      username: 'orbyta',
      password: orbytaPassword
    }).returning();
    console.log('✅ Utente orbyta creato:', orbytaUser[0].username);
    
    console.log('\n🎯 Credenziali create:');
    console.log('  admin / Adm1n!Secure2025$');
    console.log('  orbyta / Orbyta#Engineering2025!');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Errore creazione utenti:', error);
    process.exit(1);
  }
}

createInitialUsers();
