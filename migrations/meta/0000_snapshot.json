{"id": "bd2de3c8-335e-4c1e-81e6-190b89760673", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.cantieri": {"name": "cantieri", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "codice_commessa": {"name": "codice_commessa", "type": "text", "primaryKey": false, "notNull": true}, "indirizzo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "committente": {"name": "committente", "type": "text", "primaryKey": false, "notNull": true}, "oggetto": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "cse_nominato": {"name": "cse_nominato", "type": "text", "primaryKey": false, "notNull": true}, "data_inizio": {"name": "data_inizio", "type": "date", "primaryKey": false, "notNull": false}, "data_fine_prevista": {"name": "data_fine_prevista", "type": "date", "primaryKey": false, "notNull": false}, "stato": {"name": "stato", "type": "text", "primaryKey": false, "notNull": true, "default": "'In pianificazione'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"cantieri_codice_commessa_unique": {"name": "cantieri_codice_commessa_unique", "nullsNotDistinct": false, "columns": ["codice_commessa"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.direttori_lavori": {"name": "direttori_lavori", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "nome": {"name": "nome", "type": "text", "primaryKey": false, "notNull": true}, "cognome": {"name": "cognome", "type": "text", "primaryKey": false, "notNull": true}, "qualifica": {"name": "qualifica", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "attivo": {"name": "attivo", "type": "text", "primaryKey": false, "notNull": true, "default": "'true'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.direzione_lavori": {"name": "direzione_lavori", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "codice_commessa": {"name": "codice_commessa", "type": "text", "primaryKey": false, "notNull": true}, "indirizzo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "committente": {"name": "committente", "type": "text", "primaryKey": false, "notNull": true}, "oggetto": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "contratto": {"name": "contratto", "type": "text", "primaryKey": false, "notNull": false}, "cig": {"name": "cig", "type": "text", "primaryKey": false, "notNull": false}, "data_inizio": {"name": "data_inizio", "type": "date", "primaryKey": false, "notNull": false}, "data_fine_prevista": {"name": "data_fine_prevista", "type": "date", "primaryKey": false, "notNull": false}, "stato": {"name": "stato", "type": "text", "primaryKey": false, "notNull": true, "default": "'In pianificazione'"}, "logo_sinistra": {"name": "logo_sinistra", "type": "text", "primaryKey": false, "notNull": false}, "logo_centrale": {"name": "logo_centrale", "type": "text", "primaryKey": false, "notNull": false}, "logo_destro": {"name": "logo_destro", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"direzione_lavori_codice_commessa_unique": {"name": "direzione_lavori_codice_commessa_unique", "nullsNotDistinct": false, "columns": ["codice_commessa"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.direzione_lavori_direttori": {"name": "direzione_lavori_direttori", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "direzione_lavori_id": {"name": "direzione_lavori_id", "type": "integer", "primaryKey": false, "notNull": true}, "direttore_lavori_id": {"name": "direttore_lavori_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"direzione_lavori_direttori_direzione_lavori_id_direzione_lavori_id_fk": {"name": "direzione_lavori_direttori_direzione_lavori_id_direzione_lavori_id_fk", "tableFrom": "direzione_lavori_direttori", "tableTo": "direzione_lavori", "columnsFrom": ["direzione_lavori_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "direzione_lavori_direttori_direttore_lavori_id_direttori_lavori_id_fk": {"name": "direzione_lavori_direttori_direttore_lavori_id_direttori_lavori_id_fk", "tableFrom": "direzione_lavori_direttori", "tableTo": "direttori_lavori", "columnsFrom": ["direttore_lavori_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.loghi_aziendali": {"name": "loghi_aziendali", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "nome": {"name": "nome", "type": "text", "primaryKey": false, "notNull": true}, "descrizione": {"name": "descrizione", "type": "text", "primaryKey": false, "notNull": false}, "contenuto": {"name": "contenuto", "type": "text", "primaryKey": false, "notNull": true}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'image/png'"}, "dimensioni": {"name": "dimensioni", "type": "text", "primaryKey": false, "notNull": false}, "attivo": {"name": "attivo", "type": "text", "primaryKey": false, "notNull": true, "default": "'true'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"loghi_aziendali_nome_unique": {"name": "loghi_aziendali_nome_unique", "nullsNotDistinct": false, "columns": ["nome"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.persone_presenti": {"name": "persone_presenti", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "cantiere_id": {"name": "cantiere_id", "type": "integer", "primaryKey": false, "notNull": true}, "nome": {"name": "nome", "type": "text", "primaryKey": false, "notNull": true}, "qualifica": {"name": "qualifica", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "telefono": {"name": "telefono", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"persone_presenti_cantiere_id_cantieri_id_fk": {"name": "persone_presenti_cantiere_id_cantieri_id_fk", "tableFrom": "persone_presenti", "tableTo": "cantieri", "columnsFrom": ["cantiere_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rubrica_persone_presenti": {"name": "rubrica_persone_presenti", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "direzione_lavori_id": {"name": "direzione_lavori_id", "type": "integer", "primaryKey": false, "notNull": true}, "nome": {"name": "nome", "type": "text", "primaryKey": false, "notNull": true}, "qualifica": {"name": "qualifica", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "telefono": {"name": "telefono", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"rubrica_persone_presenti_direzione_lavori_id_direzione_lavori_id_fk": {"name": "rubrica_persone_presenti_direzione_lavori_id_direzione_lavori_id_fk", "tableFrom": "rubrica_persone_presenti", "tableTo": "direzione_lavori", "columnsFrom": ["direzione_lavori_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sopralluoghi": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "cantiere_id": {"name": "cantiere_id", "type": "serial", "primaryKey": false, "notNull": true}, "numero_sopralluogo": {"name": "numero_sopralluogo", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "date", "primaryKey": false, "notNull": true}, "tecnico_id": {"name": "tecnico_id", "type": "serial", "primaryKey": false, "notNull": true}, "verbale_url": {"name": "verbale_url", "type": "text", "primaryKey": false, "notNull": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "lavorazioni_in_corso": {"name": "lavorazioni_in_corso", "type": "text", "primaryKey": false, "notNull": false}, "stato_sopralluogo": {"name": "stato_sopralluogo", "type": "text", "primaryKey": false, "notNull": true, "default": "'In corso'"}, "stato": {"name": "stato", "type": "text", "primaryKey": false, "notNull": true, "default": "'bozza'"}, "checklist_data": {"name": "checklist_data", "type": "text", "primaryKey": false, "notNull": false}, "persone_presenti_data": {"name": "persone_presenti_data", "type": "text", "primaryKey": false, "notNull": false}, "current_step": {"name": "current_step", "type": "text", "primaryKey": false, "notNull": false, "default": "'info'"}, "pdf_contenuto": {"name": "pdf_contenuto", "type": "text", "primaryKey": false, "notNull": false}, "pdf_nome": {"name": "pdf_nome", "type": "text", "primaryKey": false, "notNull": false}, "pdf_size": {"name": "pdf_size", "type": "integer", "primaryKey": false, "notNull": false}, "pdf_generated_at": {"name": "pdf_generated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tecnici": {"name": "tecnici", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "nome": {"name": "nome", "type": "text", "primaryKey": false, "notNull": true}, "cognome": {"name": "cognome", "type": "text", "primaryKey": false, "notNull": true}, "specializzazione": {"name": "specializzazione", "type": "text", "primaryKey": false, "notNull": true}, "telefono": {"name": "telefono", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "attivo": {"name": "attivo", "type": "text", "primaryKey": false, "notNull": true, "default": "'true'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verbali_direzione_lavori": {"name": "verbali_direzione_lavori", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "direzione_lavori_id": {"name": "direzione_lavori_id", "type": "integer", "primaryKey": false, "notNull": true}, "numero_progressivo": {"name": "numero_progressivo", "type": "text", "primaryKey": false, "notNull": true}, "tecnico_id": {"name": "tecnico_id", "type": "integer", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "date", "primaryKey": false, "notNull": true}, "lavorazioni_in_corso": {"name": "lavorazioni_in_corso", "type": "text", "primaryKey": false, "notNull": false}, "criticita": {"name": "criticita", "type": "text", "primaryKey": false, "notNull": false}, "osservazioni": {"name": "osservazioni", "type": "text", "primaryKey": false, "notNull": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "checklist": {"name": "checklist", "type": "text", "primaryKey": false, "notNull": true, "default": "'[]'"}, "persone_presenti": {"name": "persone_presenti", "type": "text", "primaryKey": false, "notNull": false}, "firme": {"name": "firme", "type": "text", "primaryKey": false, "notNull": false}, "pdf_path": {"name": "pdf_path", "type": "text", "primaryKey": false, "notNull": false}, "stato": {"name": "stato", "type": "text", "primaryKey": false, "notNull": true, "default": "'bozza'"}, "controlli_dimensionali": {"name": "controlli_dimensionali", "type": "text", "primaryKey": false, "notNull": false}, "controlli_dimensionali_disegno": {"name": "controlli_dimensionali_disegno", "type": "text", "primaryKey": false, "notNull": false}, "controlli_dimensionali_foto": {"name": "controlli_dimensionali_foto", "type": "text[]", "primaryKey": false, "notNull": false}, "non_conformita": {"name": "non_conformita", "type": "text", "primaryKey": false, "notNull": false}, "non_conformita_disegno": {"name": "non_conformita_disegno", "type": "text", "primaryKey": false, "notNull": false}, "non_conformita_foto": {"name": "non_conformita_foto", "type": "text[]", "primaryKey": false, "notNull": false}, "indicazioni_operative": {"name": "indicazioni_operative", "type": "text", "primaryKey": false, "notNull": false}, "indicazioni_operative_disegno": {"name": "indicazioni_operative_disegno", "type": "text", "primaryKey": false, "notNull": false}, "indicazioni_operative_foto": {"name": "indicazioni_operative_foto", "type": "text[]", "primaryKey": false, "notNull": false}, "pdf_contenuto": {"name": "pdf_contenuto", "type": "text", "primaryKey": false, "notNull": false}, "pdf_nome": {"name": "pdf_nome", "type": "text", "primaryKey": false, "notNull": false}, "pdf_size": {"name": "pdf_size", "type": "integer", "primaryKey": false, "notNull": false}, "pdf_generated_at": {"name": "pdf_generated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"verbali_direzione_lavori_direzione_lavori_id_direzione_lavori_id_fk": {"name": "verbali_direzione_lavori_direzione_lavori_id_direzione_lavori_id_fk", "tableFrom": "verbali_direzione_lavori", "tableTo": "direzione_lavori", "columnsFrom": ["direzione_lavori_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "verbali_direzione_lavori_tecnico_id_direttori_lavori_id_fk": {"name": "verbali_direzione_lavori_tecnico_id_direttori_lavori_id_fk", "tableFrom": "verbali_direzione_lavori", "tableTo": "direttori_lavori", "columnsFrom": ["tecnico_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}