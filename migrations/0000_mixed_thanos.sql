CREATE TABLE "cantieri" (
	"id" serial PRIMARY KEY NOT NULL,
	"codice_commessa" text NOT NULL,
	"indirizzo" text NOT NULL,
	"committente" text NOT NULL,
	"oggetto" text NOT NULL,
	"cse_nominato" text NOT NULL,
	"data_inizio" date,
	"data_fine_prevista" date,
	"stato" text DEFAULT 'In pianificazione' NOT NULL,
	CONSTRAINT "cantieri_codice_commessa_unique" UNIQUE("codice_commessa")
);
--> statement-breakpoint
CREATE TABLE "direttori_lavori" (
	"id" serial PRIMARY KEY NOT NULL,
	"nome" text NOT NULL,
	"cognome" text NOT NULL,
	"qualifica" text NOT NULL,
	"email" text,
	"attivo" text DEFAULT 'true' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "direzione_lavori" (
	"id" serial PRIMARY KEY NOT NULL,
	"codice_commessa" text NOT NULL,
	"indirizzo" text NOT NULL,
	"committente" text NOT NULL,
	"oggetto" text NOT NULL,
	"contratto" text,
	"cig" text,
	"data_inizio" date,
	"data_fine_prevista" date,
	"stato" text DEFAULT 'In pianificazione' NOT NULL,
	"logo_sinistra" text,
	"logo_centrale" text,
	"logo_destro" text,
	CONSTRAINT "direzione_lavori_codice_commessa_unique" UNIQUE("codice_commessa")
);
--> statement-breakpoint
CREATE TABLE "direzione_lavori_direttori" (
	"id" serial PRIMARY KEY NOT NULL,
	"direzione_lavori_id" integer NOT NULL,
	"direttore_lavori_id" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "loghi_aziendali" (
	"id" serial PRIMARY KEY NOT NULL,
	"nome" text NOT NULL,
	"descrizione" text,
	"contenuto" text NOT NULL,
	"content_type" text DEFAULT 'image/png' NOT NULL,
	"dimensioni" text,
	"attivo" text DEFAULT 'true' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "loghi_aziendali_nome_unique" UNIQUE("nome")
);
--> statement-breakpoint
CREATE TABLE "persone_presenti" (
	"id" serial PRIMARY KEY NOT NULL,
	"cantiere_id" integer NOT NULL,
	"nome" text NOT NULL,
	"qualifica" text NOT NULL,
	"email" text,
	"telefono" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "rubrica_persone_presenti" (
	"id" serial PRIMARY KEY NOT NULL,
	"direzione_lavori_id" integer NOT NULL,
	"nome" text NOT NULL,
	"qualifica" text NOT NULL,
	"email" text,
	"telefono" text,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "sopralluoghi" (
	"id" serial PRIMARY KEY NOT NULL,
	"cantiere_id" serial NOT NULL,
	"numero_sopralluogo" text NOT NULL,
	"data" date NOT NULL,
	"tecnico_id" serial NOT NULL,
	"verbale_url" text,
	"note" text,
	"lavorazioni_in_corso" text,
	"stato_sopralluogo" text DEFAULT 'In corso' NOT NULL,
	"stato" text DEFAULT 'bozza' NOT NULL,
	"checklist_data" text,
	"persone_presenti_data" text,
	"current_step" text DEFAULT 'info',
	"pdf_contenuto" text,
	"pdf_nome" text,
	"pdf_size" integer,
	"pdf_generated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "tecnici" (
	"id" serial PRIMARY KEY NOT NULL,
	"nome" text NOT NULL,
	"cognome" text NOT NULL,
	"specializzazione" text NOT NULL,
	"telefono" text,
	"email" text,
	"attivo" text DEFAULT 'true' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"username" text NOT NULL,
	"password" text NOT NULL,
	CONSTRAINT "users_username_unique" UNIQUE("username")
);
--> statement-breakpoint
CREATE TABLE "verbali_direzione_lavori" (
	"id" serial PRIMARY KEY NOT NULL,
	"direzione_lavori_id" integer NOT NULL,
	"numero_progressivo" text NOT NULL,
	"tecnico_id" integer NOT NULL,
	"data" date NOT NULL,
	"lavorazioni_in_corso" text,
	"criticita" text,
	"osservazioni" text,
	"note" text,
	"checklist" text DEFAULT '[]' NOT NULL,
	"persone_presenti" text,
	"firme" text,
	"pdf_path" text,
	"stato" text DEFAULT 'bozza' NOT NULL,
	"controlli_dimensionali" text,
	"controlli_dimensionali_disegno" text,
	"controlli_dimensionali_foto" text[],
	"non_conformita" text,
	"non_conformita_disegno" text,
	"non_conformita_foto" text[],
	"indicazioni_operative" text,
	"indicazioni_operative_disegno" text,
	"indicazioni_operative_foto" text[],
	"pdf_contenuto" text,
	"pdf_nome" text,
	"pdf_size" integer,
	"pdf_generated_at" timestamp,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "direzione_lavori_direttori" ADD CONSTRAINT "direzione_lavori_direttori_direzione_lavori_id_direzione_lavori_id_fk" FOREIGN KEY ("direzione_lavori_id") REFERENCES "public"."direzione_lavori"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "direzione_lavori_direttori" ADD CONSTRAINT "direzione_lavori_direttori_direttore_lavori_id_direttori_lavori_id_fk" FOREIGN KEY ("direttore_lavori_id") REFERENCES "public"."direttori_lavori"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "persone_presenti" ADD CONSTRAINT "persone_presenti_cantiere_id_cantieri_id_fk" FOREIGN KEY ("cantiere_id") REFERENCES "public"."cantieri"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rubrica_persone_presenti" ADD CONSTRAINT "rubrica_persone_presenti_direzione_lavori_id_direzione_lavori_id_fk" FOREIGN KEY ("direzione_lavori_id") REFERENCES "public"."direzione_lavori"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "verbali_direzione_lavori" ADD CONSTRAINT "verbali_direzione_lavori_direzione_lavori_id_direzione_lavori_id_fk" FOREIGN KEY ("direzione_lavori_id") REFERENCES "public"."direzione_lavori"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "verbali_direzione_lavori" ADD CONSTRAINT "verbali_direzione_lavori_tecnico_id_direttori_lavori_id_fk" FOREIGN KEY ("tecnico_id") REFERENCES "public"."direttori_lavori"("id") ON DELETE no action ON UPDATE no action;