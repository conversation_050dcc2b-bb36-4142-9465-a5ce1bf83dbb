import { pgTable, text, serial, date, integer, timestamp, unique } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const cantieri = pgTable("cantieri", {
  id: serial("id").primaryKey(),
  codiceCommessa: text("codice_commessa").notNull().unique(),
  indirizzo: text("indirizzo").notNull(),
  committente: text("committente").notNull(),
  oggetto: text("oggetto").notNull(),
  cseNominato: text("cse_nominato").notNull(),
  dataInizio: date("data_inizio"),
  dataFinePrevista: date("data_fine_prevista"),
  stato: text("stato").notNull().default("In pianificazione"),
});

export const insertCantiereSchema = createInsertSchema(cantieri).omit({
  id: true,
});

export type InsertCantiere = z.infer<typeof insertCantiereSchema>;
export type Cantiere = typeof cantieri.$inferSelect;

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export const tecnici = pgTable("tecnici", {
  id: serial("id").primaryKey(),
  nome: text("nome").notNull(),
  cognome: text("cognome").notNull(),
  specializzazione: text("specializzazione").notNull(),
  telefono: text("telefono"),
  email: text("email"),
  attivo: text("attivo").notNull().default("true"),
});

export const insertTecnicoSchema = createInsertSchema(tecnici).omit({
  id: true,
});

export type InsertTecnico = z.infer<typeof insertTecnicoSchema>;
export type Tecnico = typeof tecnici.$inferSelect;

export const sopralluoghi = pgTable("sopralluoghi", {
  id: serial("id").primaryKey(),
  cantiereId: serial("cantiere_id").notNull(),
  numeroSopralluogo: text("numero_sopralluogo").notNull(),
  data: date("data").notNull(),
  tecnicoId: serial("tecnico_id").notNull(),
  verbaleUrl: text("verbale_url"),
  note: text("note"),
  lavorazioniInCorso: text("lavorazioni_in_corso"),
  statoSopralluogo: text("stato_sopralluogo").notNull().default("In corso"),
  stato: text("stato").notNull().default('bozza'), // 'bozza', 'finalizzato'
  checklistData: text("checklist_data"), // JSON string with checklist items
  personePresentiData: text("persone_presenti_data"), // JSON string with persone presenti
  currentStep: text("current_step").default("info"), // 'info', 'persone', 'checklist'
  // Campi per salvataggio PDF nel database (come verbali direzione lavori)
  pdfContenuto: text("pdf_contenuto"), // PDF codificato in base64
  pdfNome: text("pdf_nome"), // Nome del file PDF
  pdfSize: integer("pdf_size"), // Dimensione del PDF in bytes
  pdfGeneratedAt: timestamp("pdf_generated_at", { withTimezone: true }), // Data generazione PDF
});

export const insertSopralluogoSchema = createInsertSchema(sopralluoghi).omit({
  id: true,
});

export type InsertSopralluogo = z.infer<typeof insertSopralluogoSchema>;
export type Sopralluogo = typeof sopralluoghi.$inferSelect;

// Tabella per le persone presenti nei cantieri
export const personePresenti = pgTable("persone_presenti", {
  id: serial("id").primaryKey(),
  cantiereId: integer("cantiere_id").notNull().references(() => cantieri.id, { onDelete: "cascade" }),
  nome: text("nome").notNull(),
  qualifica: text("qualifica").notNull(),
  email: text("email"),
  telefono: text("telefono"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertPersonaPresenteSchema = createInsertSchema(personePresenti).omit({
  id: true,
  createdAt: true,
});

export type InsertPersonaPresente = z.infer<typeof insertPersonaPresenteSchema>;
export type PersonaPresente = typeof personePresenti.$inferSelect;

// Tabella per i direttori lavori
export const direttoriLavori = pgTable("direttori_lavori", {
  id: serial("id").primaryKey(),
  nome: text("nome").notNull(),
  cognome: text("cognome").notNull(),
  qualifica: text("qualifica").notNull(),
  email: text("email"),
  attivo: text("attivo").notNull().default("true"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertDirettoreLavoriSchema = createInsertSchema(direttoriLavori).omit({
  id: true,
  createdAt: true,
});

export type InsertDirettoreLavori = z.infer<typeof insertDirettoreLavoriSchema>;
export type DirettoreLavori = typeof direttoriLavori.$inferSelect;

// Tabella per gestire i lavori della direzione lavori
export const direzioneLavori = pgTable("direzione_lavori", {
  id: serial("id").primaryKey(),
  codiceCommessa: text("codice_commessa").notNull().unique(),
  indirizzo: text("indirizzo").notNull(),
  committente: text("committente").notNull(),
  oggetto: text("oggetto").notNull(),
  contratto: text("contratto"),
  cig: text("cig"),
  dataInizio: date("data_inizio"),
  dataFinePrevista: date("data_fine_prevista"),
  stato: text("stato").notNull().default("In pianificazione"),
  logoSinistra: text("logo_sinistra"), // Path del logo a sinistra
  logoCentrale: text("logo_centrale"), // Path del logo centrale  
  logoDestro: text("logo_destro"), // Path del logo a destra
});

// Tabella di giunzione per gestire più direttori per commessa
export const direzioneLavoriDirettori = pgTable("direzione_lavori_direttori", {
  id: serial("id").primaryKey(),
  direzioneLavoriId: integer("direzione_lavori_id").notNull().references(() => direzioneLavori.id, { onDelete: "cascade" }),
  direttoreLavoriId: integer("direttore_lavori_id").notNull().references(() => direttoriLavori.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertDirezioneLavoriSchema = createInsertSchema(direzioneLavori).omit({
  id: true,
});

export const insertDirezioneLavoriDirettoriSchema = createInsertSchema(direzioneLavoriDirettori).omit({
  id: true,
  createdAt: true,
});

export type InsertDirezioneLavori = z.infer<typeof insertDirezioneLavoriSchema>;
export type DirezioneLavori = typeof direzioneLavori.$inferSelect;
export type InsertDirezioneLavoriDirettori = z.infer<typeof insertDirezioneLavoriDirettoriSchema>;
export type DirezioneLavoriDirettori = typeof direzioneLavoriDirettori.$inferSelect;

// Tabella per i verbali di direzione lavori
export const verbaliDirezioneLavori = pgTable("verbali_direzione_lavori", {
  id: serial("id").primaryKey(),
  direzioneLavoriId: integer("direzione_lavori_id").notNull().references(() => direzioneLavori.id, { onDelete: "cascade" }),
  numeroProgressivo: text("numero_progressivo").notNull(),
  tecnicoId: integer("tecnico_id").notNull().references(() => direttoriLavori.id),
  data: date("data").notNull(),
  lavorazioniInCorso: text("lavorazioni_in_corso"),
  criticita: text("criticita"),
  osservazioni: text("osservazioni"),
  note: text("note"),
  checklist: text("checklist").notNull().default('[]'),
  personePresenti: text("persone_presenti"),
  firme: text("firme"),
  pdfPath: text("pdf_path"),
  stato: text("stato").notNull().default('bozza'), // 'bozza', 'finalizzato'
  // Nuovi campi per sezioni opzionali
  controlliDimensionali: text("controlli_dimensionali"), // Testo libero
  controlliDimensionaliDisegno: text("controlli_dimensionali_disegno"), // Dati disegno millimetrato
  controlliDimensionaliFoto: text("controlli_dimensionali_foto").array(), // Array di percorsi foto
  nonConformita: text("non_conformita"), // Testo libero
  nonConformitaDisegno: text("non_conformita_disegno"), // Dati disegno millimetrato
  nonConformitaFoto: text("non_conformita_foto").array(), // Array di percorsi foto
  indicazioniOperative: text("indicazioni_operative"), // Testo libero
  indicazioniOperativeDisegno: text("indicazioni_operative_disegno"), // Dati disegno millimetrato
  indicazioniOperativeFoto: text("indicazioni_operative_foto").array(), // Array di percorsi foto
  // Campi per storage PDF in database
  pdfContenuto: text("pdf_contenuto"), // PDF codificato in base64
  pdfNome: text("pdf_nome"), // Nome del file PDF
  pdfSize: integer("pdf_size"), // Dimensione del PDF in bytes
  pdfGeneratedAt: timestamp("pdf_generated_at"), // Quando è stato generato il PDF
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertVerbaleDirezioneLavoriSchema = createInsertSchema(verbaliDirezioneLavori).omit({
  id: true,
  createdAt: true,
});

export type InsertVerbaleDirezioneLavori = z.infer<typeof insertVerbaleDirezioneLavoriSchema>;
export type VerbaleDirezioneLavori = typeof verbaliDirezioneLavori.$inferSelect;

// Tabella per rubrica persone presenti per commessa
export const rubricaPersonePresenti = pgTable("rubrica_persone_presenti", {
  id: serial("id").primaryKey(),
  direzioneLavoriId: integer("direzione_lavori_id").notNull().references(() => direzioneLavori.id),
  nome: text("nome").notNull(),
  qualifica: text("qualifica").notNull(),
  email: text("email"),
  telefono: text("telefono"),
  createdAt: timestamp("created_at").defaultNow()
});

export const insertRubricaPersonaPresenteSchema = createInsertSchema(rubricaPersonePresenti).omit({
  id: true,
  createdAt: true
});

export type InsertRubricaPersonaPresente = z.infer<typeof insertRubricaPersonaPresenteSchema>;
export type RubricaPersonaPresente = typeof rubricaPersonePresenti.$inferSelect;

// Tabella per la gestione dei loghi aziendali
export const loghiAziendali = pgTable("loghi_aziendali", {
  id: serial("id").primaryKey(),
  nome: text("nome").notNull().unique(), // es: "logo_sinistra", "logo_centrale", "logo_destro"
  descrizione: text("descrizione"), // Descrizione opzionale del logo
  contenuto: text("contenuto").notNull(), // Base64 del logo
  contentType: text("content_type").notNull().default("image/png"), // MIME type
  dimensioni: text("dimensioni"), // JSON con larghezza/altezza originali
  attivo: text("attivo").notNull().default("true"), // true/false
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertLogoAziendaleSchema = createInsertSchema(loghiAziendali).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertLogoAziendale = z.infer<typeof insertLogoAziendaleSchema>;
export type LogoAziendale = typeof loghiAziendali.$inferSelect;
