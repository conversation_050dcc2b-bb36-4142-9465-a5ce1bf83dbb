<!DOCTYPE html>
<html>
<head>
    <title>Test Verbale Creation</title>
</head>
<body>
    <h1>Test Creazione Verbale</h1>
    <button onclick="testCreateVerbale()">Crea Verbale di Test</button>
    <div id="result"></div>
    
    <script>
        async function testCreateVerbale() {
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = 'Creazione verbale in corso...';
                
                const response = await fetch('/api/verbali-direzione-lavori', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        direzioneLavoriId: 5,
                        tecnicoId: 2,
                        data: new Date().toISOString().split('T')[0],
                        oraInizio: '09:00',
                        oraFine: '10:00',
                        temperatura: '20°C',
                        condizioni: 'Sereno',
                        note: 'Test verbale creato da pagina di test',
                        lavorazioniInCorso: 'Lavori di prova',
                        criticita: 'Nessuna',
                        osservazioni: 'Test funzionamento',
                        status: 'bozza',
                        numeroProgressivo: '999'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h3>✅ Verbale creato con successo!</h3>
                        <p><strong>ID:</strong> ${data.id}</p>
                        <p><strong>Status:</strong> ${data.status}</p>
                        <p><strong>Data:</strong> ${data.data}</p>
                        <p><strong>Numero Progressivo:</strong> ${data.numeroProgressivo}</p>
                    `;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `
                        <h3>❌ Errore nella creazione</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Errore:</strong> ${error}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ Errore di rete</h3>
                    <p><strong>Errore:</strong> ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>