// Test semplice per verificare Object Storage
console.log('🧪 Test Object Storage - Orbyte Engineering');

// Test ambiente
console.log('Environment variables:');
console.log('- REPLIT_APP_NAME:', process.env.REPLIT_APP_NAME || 'undefined');
console.log('- REPL_ID:', process.env.REPL_ID || 'undefined');
console.log('- REPL_SLUG:', process.env.REPL_SLUG || 'undefined');

const isReplitEnv = !!(
  process.env.REPLIT_APP_NAME ||
  process.env.REPL_ID ||
  process.env.REPL_SLUG
);

console.log('');
console.log('🔍 Detected environment:');
console.log('- Is Replit:', isReplitEnv);
console.log('- Node.js version:', process.version);
console.log('- Platform:', process.platform);

// Test import Object Storage SDK
try {
  console.log('');
  console.log('📦 Testing Object Storage SDK import...');
  
  import('@replit/object-storage').then(module => {
    console.log('✅ Object Storage SDK imported successfully');
    console.log('- Client available:', typeof module.Client === 'function');
    
    if (isReplitEnv) {
      console.log('');
      console.log('☁️ Creating Object Storage client...');
      
      try {
        const client = new module.Client();
        console.log('✅ Object Storage client created successfully');
        console.log('- Client type:', typeof client);
      } catch (error) {
        console.log('❌ Object Storage client creation failed:', error.message);
      }
    } else {
      console.log('');
      console.log('💾 Local environment - Object Storage not available');
      console.log('- Using local filesystem fallback');
    }
    
  }).catch(error => {
    console.log('❌ Object Storage SDK import failed:', error.message);
  });
  
} catch (error) {
  console.log('❌ Object Storage test failed:', error.message);
}